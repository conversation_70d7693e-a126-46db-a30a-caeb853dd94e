"""
Event Trading Contracts and Market Events

This module demonstrates how to work with event-based contracts and
market events using the TWS API. Event contracts are special financial
instruments tied to specific events or outcomes.

Key Topics Covered:
- Understanding event contract structures
- Creating event-based contract specifications
- Working with binary outcome contracts
- Managing event contract data and pricing
- Understanding event contract lifecycles
- Best practices for event contract trading

Event contracts provide unique trading opportunities based on
specific market events, economic announcements, or other
measurable outcomes.
"""

import threading
import time
import logging
from typing import Dict, List, Optional, Callable
from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract, ContractDetails


class EventContractsApp(EClient, EWrapper):
    """
    TWS API application for event trading contracts.
    
    This class demonstrates how to work with event-based contracts,
    including contract creation, data retrieval, and management.
    
    Attributes:
        event_contracts (dict): Storage for event contract information
        contract_details (dict): Detailed contract information
        event_callbacks (dict): Callbacks for event contract events
    """
    
    def __init__(self):
        """Initialize the event contracts application."""
        EClient.__init__(self, self)
        
        # Data storage
        self.event_contracts: Dict[int, Contract] = {}
        self.contract_details: Dict[int, List[ContractDetails]] = {}
        
        # Event contract types
        self.event_contract_types = {
            'BINARY': 'Binary outcome contracts',
            'EVENT': 'Event-based contracts',
            'PREDICTION': 'Prediction market contracts'
        }
        
        # Callback management
        self.event_callbacks = {
            'contract_details': [],
            'contract_details_end': [],
            'event_data': []
        }
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def connectAck(self):
        """Handle successful connection."""
        self.logger.info("Connected to TWS - Event Contracts service ready")
    
    def nextValidId(self, orderId):
        """
        Receive next valid order ID.
        
        Args:
            orderId (int): Next valid order ID
        """
        self.logger.info(f"Connection ready. Next valid order ID: {orderId}")
    
    def contractDetails(self, reqId: int, contractDetails: ContractDetails):
        """
        Receive contract details for event contracts.
        
        Args:
            reqId (int): Request ID
            contractDetails (ContractDetails): Contract details
        """
        # Initialize storage if needed
        if reqId not in self.contract_details:
            self.contract_details[reqId] = []
        
        # Store contract details
        self.contract_details[reqId].append(contractDetails)
        
        contract = contractDetails.contract
        
        self.logger.info(f"Event Contract Details - ReqId: {reqId}")
        self.logger.info(f"  Symbol: {contract.symbol}")
        self.logger.info(f"  Security Type: {contract.secType}")
        self.logger.info(f"  Exchange: {contract.exchange}")
        self.logger.info(f"  Description: {contractDetails.longName}")
        
        # Check if this is an event contract
        if self._is_event_contract(contractDetails):
            self.logger.info("  ✓ Event Contract Detected")
            self._process_event_contract(contractDetails)
        
        # Trigger callbacks
        for callback in self.event_callbacks['contract_details']:
            try:
                callback(reqId, contractDetails)
            except Exception as e:
                self.logger.error(f"Contract details callback error: {e}")
    
    def contractDetailsEnd(self, reqId: int):
        """
        Handle end of contract details transmission.
        
        Args:
            reqId (int): Request ID
        """
        details_count = len(self.contract_details.get(reqId, []))
        self.logger.info(f"Contract Details End - ReqId: {reqId}, Count: {details_count}")
        
        # Trigger callbacks
        for callback in self.event_callbacks['contract_details_end']:
            try:
                callback(reqId)
            except Exception as e:
                self.logger.error(f"Contract details end callback error: {e}")
    
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors related to event contract requests.
        
        Args:
            reqId (int): Request ID
            errorCode (int): Error code
            errorString (str): Error description
            advancedOrderRejectJson (str): Additional error details
        """
        self.logger.error(f"Error {reqId} {errorCode} {errorString}")
    
    def _is_event_contract(self, contract_details: ContractDetails) -> bool:
        """
        Determine if a contract is an event contract.
        
        Args:
            contract_details (ContractDetails): Contract details to check
            
        Returns:
            bool: True if it's an event contract
        """
        contract = contract_details.contract
        
        # Check for event contract indicators
        event_indicators = [
            'EVENT' in contract.secType.upper(),
            'BINARY' in contract.secType.upper(),
            'PREDICTION' in (contract_details.longName or '').upper(),
            'EVENT' in (contract_details.longName or '').upper()
        ]
        
        return any(event_indicators)
    
    def _process_event_contract(self, contract_details: ContractDetails):
        """
        Process event contract specific information.
        
        Args:
            contract_details (ContractDetails): Event contract details
        """
        contract = contract_details.contract
        
        # Extract event-specific information
        event_info = {
            'contract': contract,
            'details': contract_details,
            'event_type': self._determine_event_type(contract_details),
            'event_description': contract_details.longName,
            'trading_hours': contract_details.tradingHours,
            'liquid_hours': contract_details.liquidHours,
            'min_tick': contract_details.minTick,
            'price_magnifier': contract_details.priceMagnifier,
            'processed_at': time.time()
        }
        
        self.logger.info(f"Event Contract Info:")
        self.logger.info(f"  Event Type: {event_info['event_type']}")
        self.logger.info(f"  Min Tick: {event_info['min_tick']}")
        self.logger.info(f"  Price Magnifier: {event_info['price_magnifier']}")
        
        # Trigger event data callbacks
        for callback in self.event_callbacks['event_data']:
            try:
                callback(event_info)
            except Exception as e:
                self.logger.error(f"Event data callback error: {e}")
    
    def _determine_event_type(self, contract_details: ContractDetails) -> str:
        """
        Determine the type of event contract.
        
        Args:
            contract_details (ContractDetails): Contract details
            
        Returns:
            str: Event contract type
        """
        contract = contract_details.contract
        description = (contract_details.longName or '').upper()
        
        if 'BINARY' in contract.secType.upper() or 'BINARY' in description:
            return 'BINARY'
        elif 'PREDICTION' in description:
            return 'PREDICTION'
        elif 'EVENT' in contract.secType.upper() or 'EVENT' in description:
            return 'EVENT'
        else:
            return 'UNKNOWN'
    
    def create_event_contract(self, symbol: str, exchange: str = "SMART",
                            currency: str = "USD", sec_type: str = "EVENT") -> Contract:
        """
        Create an event contract specification.
        
        Args:
            symbol (str): Event contract symbol
            exchange (str): Exchange
            currency (str): Currency
            sec_type (str): Security type
            
        Returns:
            Contract: Event contract object
        """
        contract = Contract()
        contract.symbol = symbol
        contract.secType = sec_type
        contract.exchange = exchange
        contract.currency = currency
        
        self.logger.info(f"Created event contract: {symbol} {sec_type} {exchange} {currency}")
        
        return contract
    
    def create_binary_contract(self, symbol: str, strike: float = None,
                             expiry: str = None, exchange: str = "SMART") -> Contract:
        """
        Create a binary outcome contract.
        
        Args:
            symbol (str): Binary contract symbol
            strike (float): Strike price (if applicable)
            expiry (str): Expiry date (YYYYMMDD format)
            exchange (str): Exchange
            
        Returns:
            Contract: Binary contract object
        """
        contract = Contract()
        contract.symbol = symbol
        contract.secType = "BINARY"
        contract.exchange = exchange
        contract.currency = "USD"
        
        if strike is not None:
            contract.strike = strike
        
        if expiry:
            contract.lastTradeDateOrContractMonth = expiry
        
        self.logger.info(f"Created binary contract: {symbol} strike={strike} expiry={expiry}")
        
        return contract
    
    def request_event_contract_details(self, req_id: int, contract: Contract) -> bool:
        """
        Request details for an event contract.
        
        Args:
            req_id (int): Request identifier
            contract (Contract): Event contract
            
        Returns:
            bool: True if request was sent successfully
        """
        try:
            self.reqContractDetails(req_id, contract)
            self.event_contracts[req_id] = contract
            
            self.logger.info(f"Requested event contract details - ReqId: {req_id}, "
                           f"Symbol: {contract.symbol}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to request event contract details: {e}")
            return False
    
    def search_event_contracts(self, req_id: int, pattern: str) -> bool:
        """
        Search for event contracts matching a pattern.
        
        Args:
            req_id (int): Request identifier
            pattern (str): Search pattern
            
        Returns:
            bool: True if search was initiated successfully
        """
        # Create a generic event contract for searching
        search_contract = Contract()
        search_contract.symbol = pattern
        search_contract.secType = "EVENT"
        search_contract.exchange = "SMART"
        
        return self.request_event_contract_details(req_id, search_contract)
    
    def get_event_contract_details(self, req_id: int) -> List[ContractDetails]:
        """
        Get stored event contract details.
        
        Args:
            req_id (int): Request identifier
            
        Returns:
            list: List of contract details
        """
        return self.contract_details.get(req_id, [])
    
    def get_event_contracts_summary(self) -> Dict:
        """
        Get summary of event contracts.
        
        Returns:
            dict: Event contracts summary
        """
        total_requests = len(self.event_contracts)
        total_details = sum(len(details) for details in self.contract_details.values())
        
        # Count by type
        type_counts = {}
        for details_list in self.contract_details.values():
            for details in details_list:
                if self._is_event_contract(details):
                    event_type = self._determine_event_type(details)
                    type_counts[event_type] = type_counts.get(event_type, 0) + 1
        
        summary = {
            'total_requests': total_requests,
            'total_contracts': total_details,
            'event_contracts': sum(type_counts.values()),
            'by_type': type_counts,
            'available_types': list(self.event_contract_types.keys())
        }
        
        return summary
    
    def format_event_contract_info(self, contract_details: ContractDetails) -> str:
        """
        Format event contract information for display.
        
        Args:
            contract_details (ContractDetails): Contract details
            
        Returns:
            str: Formatted contract information
        """
        contract = contract_details.contract
        
        info_lines = [
            f"Event Contract: {contract.symbol}",
            f"Type: {contract.secType}",
            f"Exchange: {contract.exchange}",
            f"Currency: {contract.currency}",
            f"Description: {contract_details.longName}",
            f"Min Tick: {contract_details.minTick}",
            f"Trading Hours: {contract_details.tradingHours}"
        ]
        
        if hasattr(contract, 'strike') and contract.strike:
            info_lines.append(f"Strike: {contract.strike}")
        
        if hasattr(contract, 'lastTradeDateOrContractMonth') and contract.lastTradeDateOrContractMonth:
            info_lines.append(f"Expiry: {contract.lastTradeDateOrContractMonth}")
        
        return "\n".join(info_lines)
    
    def add_event_callback(self, event_type: str, callback: Callable):
        """
        Add a callback for event contract events.
        
        Args:
            event_type (str): Type of event
            callback: Callback function to add
        """
        if event_type in self.event_callbacks:
            self.event_callbacks[event_type].append(callback)


def demonstrate_event_contracts():
    """
    Demonstrate event contracts functionality with practical examples.
    """
    print("=== Event Trading Contracts Demo ===")
    print("Note: Event contracts may not be available in all markets")
    
    app = EventContractsApp()
    
    # Add callbacks for demonstration
    def on_contract_details(req_id, contract_details):
        contract = contract_details.contract
        print(f"Contract found: {contract.symbol} ({contract.secType})")
    
    def on_event_data(event_info):
        print(f"Event Contract: {event_info['event_type']} - {event_info['event_description']}")
    
    def on_details_end(req_id):
        details = app.get_event_contract_details(req_id)
        print(f"Search complete: {len(details)} contracts found")
    
    app.add_event_callback('contract_details', on_contract_details)
    app.add_event_callback('event_data', on_event_data)
    app.add_event_callback('contract_details_end', on_details_end)
    
    try:
        # Connect to TWS
        app.connect("127.0.0.1", 7497, 0)
        
        # Start processing thread
        api_thread = threading.Thread(target=app.run, daemon=True)
        api_thread.start()
        
        # Wait for connection
        time.sleep(3)
        
        if app.isConnected():
            print("Connected - searching for event contracts")
            
            # Search for event contracts (these are examples - actual symbols may vary)
            search_patterns = ["EVENT", "BINARY", "PREDICTION"]
            
            for i, pattern in enumerate(search_patterns, 9001):
                if app.search_event_contracts(i, pattern):
                    print(f"Searching for {pattern} contracts...")
                time.sleep(1)
            
            # Create example event contracts
            print("\n--- Creating Example Event Contracts ---")
            
            # Generic event contract
            event_contract = app.create_event_contract("EXAMPLE_EVENT", "SMART", "USD")
            print(f"Created event contract: {event_contract.symbol}")
            
            # Binary contract
            binary_contract = app.create_binary_contract("BINARY_EXAMPLE", 100.0, "20241231")
            print(f"Created binary contract: {binary_contract.symbol}")
            
            # Wait for search results
            print("\nWaiting for search results...")
            time.sleep(10)
            
            # Display summary
            summary = app.get_event_contracts_summary()
            print("\n--- Event Contracts Summary ---")
            print(f"Total requests: {summary['total_requests']}")
            print(f"Total contracts found: {summary['total_contracts']}")
            print(f"Event contracts: {summary['event_contracts']}")
            
            if summary['by_type']:
                print("By type:")
                for event_type, count in summary['by_type'].items():
                    print(f"  {event_type}: {count}")
            
            # Show detailed information for found contracts
            for req_id, details_list in app.contract_details.items():
                if details_list:
                    print(f"\n--- Request {req_id} Details ---")
                    for i, details in enumerate(details_list[:2]):  # Show first 2
                        print(f"Contract {i+1}:")
                        print(app.format_event_contract_info(details))
                        print("-" * 40)
        
        else:
            print("Failed to connect to TWS")
    
    except KeyboardInterrupt:
        print("\nShutting down...")
    
    finally:
        app.disconnect()


if __name__ == "__main__":
    """
    Main execution block for event contracts demonstrations.
    """
    print("TWS API Event Trading Contracts Examples")
    print("=" * 45)
    
    # Run the demonstration
    demonstrate_event_contracts()
