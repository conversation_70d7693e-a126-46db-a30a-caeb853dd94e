"""
Real-time News Feeds and Subscriptions

This module demonstrates how to subscribe to and receive real-time news
feeds from the TWS API. News feeds provide continuous updates of market
news, company announcements, and financial information.

Key Topics Covered:
- Subscribing to real-time news feeds
- Handling news tick updates and notifications
- Processing news headlines and metadata
- Managing news subscriptions for specific symbols
- Understanding news provider codes and sources
- Filtering news by relevance and categories
- Best practices for news feed management

Real-time news feeds are essential for staying informed about market
developments and making timely trading decisions based on current events.
"""

import threading
import time
import logging
from typing import Dict, List, Optional, Set, Callable
from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract


class NewsFeedsApp(EClient, EWrapper):
    """
    TWS API application for real-time news feeds.
    
    This class demonstrates how to subscribe to, receive, and manage
    real-time news feeds with comprehensive news processing capabilities.
    
    Attributes:
        news_subscriptions (dict): Active news subscriptions by contract
        news_data (dict): Storage for received news items
        news_providers (dict): Available news providers and their codes
        news_callbacks (dict): Callbacks for news events
    """
    
    def __init__(self):
        """Initialize the news feeds application."""
        EClient.__init__(self, self)
        
        # Data storage
        self.news_subscriptions: Dict[int, Contract] = {}
        self.news_data: Dict[str, Dict] = {}  # article_id -> news_data
        
        # News provider information
        self.news_providers = {
            'BRFG': 'Briefing.com',
            'BRFUPDN': 'Briefing.com UpDown',
            'DJ-N': 'Dow Jones News',
            'DJ-RT': 'Dow Jones Real-Time',
            'FLYONTHEWALL': 'Fly on the Wall',
            'MT_NEWSWIRES': 'Market Watch',
            'BZ': 'Benzinga Pro',
            'BZ+': 'Benzinga Pro (Premium)',
            'STREETINSIDER': 'StreetInsider',
            'LIVESQUAWK': 'LiveSquawk'
        }
        
        # Callback management
        self.news_callbacks = {
            'news_tick': [],
            'news_article': [],
            'news_providers_update': []
        }
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def connectAck(self):
        """Handle successful connection."""
        self.logger.info("Connected to TWS - News Feeds service ready")
    
    def nextValidId(self, orderId):
        """
        Receive next valid order ID.
        
        Args:
            orderId (int): Next valid order ID
        """
        self.logger.info(f"Connection ready. Next valid order ID: {orderId}")
    
    def tickNews(self, tickerId: int, timeStamp: int, providerCode: str, 
                articleId: str, headline: str, extraData: str):
        """
        Receive news tick updates.
        
        Args:
            tickerId (int): Ticker ID for the news subscription
            timeStamp (int): News timestamp
            providerCode (str): News provider code
            articleId (str): Unique article identifier
            headline (str): News headline
            extraData (str): Additional news data
        """
        # Convert timestamp to readable format
        news_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timeStamp))
        
        # Store news data
        self.news_data[articleId] = {
            'ticker_id': tickerId,
            'timestamp': timeStamp,
            'time_formatted': news_time,
            'provider_code': providerCode,
            'provider_name': self.news_providers.get(providerCode, providerCode),
            'article_id': articleId,
            'headline': headline,
            'extra_data': extraData,
            'received_at': time.time()
        }
        
        # Get contract symbol if available
        contract = self.news_subscriptions.get(tickerId)
        symbol = contract.symbol if contract else f"TickerId_{tickerId}"
        
        self.logger.info(f"News Tick - {symbol}: {headline}")
        self.logger.debug(f"  Provider: {providerCode}, Article ID: {articleId}, Time: {news_time}")
        
        # Trigger callbacks
        for callback in self.news_callbacks['news_tick']:
            try:
                callback(tickerId, timeStamp, providerCode, articleId, headline, extraData)
            except Exception as e:
                self.logger.error(f"News tick callback error: {e}")
    
    def newsArticle(self, requestId: int, articleType: int, articleText: str):
        """
        Receive full news article content.
        
        Args:
            requestId (int): Request ID for the article
            articleType (int): Type of article (0=text, 1=html)
            articleText (str): Full article content
        """
        article_type_names = {0: 'Text', 1: 'HTML'}
        type_name = article_type_names.get(articleType, f"Type_{articleType}")
        
        self.logger.info(f"News Article - Request ID: {requestId}, Type: {type_name}")
        self.logger.debug(f"Article content length: {len(articleText)} characters")
        
        # Store article content (could be linked to article_id if available)
        article_data = {
            'request_id': requestId,
            'article_type': articleType,
            'article_text': articleText,
            'received_at': time.time()
        }
        
        # Trigger callbacks
        for callback in self.news_callbacks['news_article']:
            try:
                callback(requestId, articleType, articleText)
            except Exception as e:
                self.logger.error(f"News article callback error: {e}")
    
    def newsProviders(self, newsProviders):
        """
        Receive news providers information.
        
        Args:
            newsProviders: List of available news providers
        """
        self.logger.info("News Providers Update:")
        
        # Update providers dictionary
        for provider in newsProviders:
            provider_code = provider.providerCode
            provider_name = provider.providerName
            self.news_providers[provider_code] = provider_name
            
            self.logger.info(f"  {provider_code}: {provider_name}")
        
        # Trigger callbacks
        for callback in self.news_callbacks['news_providers_update']:
            try:
                callback(newsProviders)
            except Exception as e:
                self.logger.error(f"News providers callback error: {e}")
    
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors related to news requests.
        
        Args:
            reqId (int): Request ID
            errorCode (int): Error code
            errorString (str): Error description
            advancedOrderRejectJson (str): Additional error details
        """
        self.logger.error(f"Error {reqId} {errorCode} {errorString}")
        
        # Handle news-specific errors
        if reqId in self.news_subscriptions:
            self.logger.error(f"News subscription {reqId} error: {errorString}")
    
    def request_news_providers(self):
        """Request information about available news providers."""
        try:
            self.reqNewsProviders()
            self.logger.info("Requested news providers information")
        except Exception as e:
            self.logger.error(f"Failed to request news providers: {e}")
    
    def subscribe_news_feed(self, req_id: int, contract: Contract, 
                           news_provider: str = "", generic_tick_list: str = "292") -> bool:
        """
        Subscribe to news feed for a specific contract.
        
        Args:
            req_id (int): Unique request identifier
            contract (Contract): Contract to get news for
            news_provider (str): Specific news provider (empty for all)
            generic_tick_list (str): Generic tick list (292 for news)
            
        Returns:
            bool: True if subscription was successful
        """
        if req_id in self.news_subscriptions:
            self.logger.error(f"Request ID {req_id} already has an active news subscription")
            return False
        
        try:
            # Subscribe to market data with news tick (292)
            self.reqMktData(req_id, contract, generic_tick_list, False, False, [])
            
            self.news_subscriptions[req_id] = contract
            
            self.logger.info(f"Subscribed to news feed - ReqId: {req_id}, "
                           f"Symbol: {contract.symbol}, Provider: {news_provider or 'All'}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to subscribe to news feed: {e}")
            return False
    
    def cancel_news_feed(self, req_id: int) -> bool:
        """
        Cancel a news feed subscription.
        
        Args:
            req_id (int): Request ID to cancel
            
        Returns:
            bool: True if cancellation was successful
        """
        if req_id not in self.news_subscriptions:
            self.logger.warning(f"No active news subscription found for request ID {req_id}")
            return False
        
        try:
            self.cancelMktData(req_id)
            del self.news_subscriptions[req_id]
            
            self.logger.info(f"Canceled news feed subscription - ReqId: {req_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cancel news feed: {e}")
            return False
    
    def request_news_article(self, req_id: int, provider_code: str, article_id: str) -> bool:
        """
        Request full content of a specific news article.
        
        Args:
            req_id (int): Request identifier
            provider_code (str): News provider code
            article_id (str): Article identifier
            
        Returns:
            bool: True if request was successful
        """
        try:
            self.reqNewsArticle(req_id, provider_code, article_id, [])
            
            self.logger.info(f"Requested news article - ReqId: {req_id}, "
                           f"Provider: {provider_code}, Article: {article_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to request news article: {e}")
            return False
    
    def get_news_data(self, article_id: Optional[str] = None) -> Dict:
        """
        Get stored news data.
        
        Args:
            article_id (str, optional): Specific article ID to retrieve
            
        Returns:
            dict: News data
        """
        if article_id is not None:
            return self.news_data.get(article_id, {})
        else:
            return self.news_data.copy()
    
    def get_recent_news(self, symbol: Optional[str] = None, hours: int = 24) -> List[Dict]:
        """
        Get recent news items within specified time frame.
        
        Args:
            symbol (str, optional): Filter by symbol
            hours (int): Number of hours to look back
            
        Returns:
            list: List of recent news items
        """
        cutoff_time = time.time() - (hours * 3600)
        recent_news = []
        
        for article_id, news_item in self.news_data.items():
            if news_item['received_at'] >= cutoff_time:
                # Filter by symbol if specified
                if symbol:
                    ticker_id = news_item['ticker_id']
                    contract = self.news_subscriptions.get(ticker_id)
                    if not contract or contract.symbol.upper() != symbol.upper():
                        continue
                
                recent_news.append(news_item)
        
        # Sort by timestamp (most recent first)
        recent_news.sort(key=lambda x: x['timestamp'], reverse=True)
        return recent_news
    
    def get_news_by_provider(self, provider_code: str) -> List[Dict]:
        """
        Get news items from a specific provider.
        
        Args:
            provider_code (str): Provider code to filter by
            
        Returns:
            list: List of news items from the provider
        """
        provider_news = []
        
        for article_id, news_item in self.news_data.items():
            if news_item['provider_code'] == provider_code:
                provider_news.append(news_item)
        
        # Sort by timestamp (most recent first)
        provider_news.sort(key=lambda x: x['timestamp'], reverse=True)
        return provider_news
    
    def get_news_summary(self) -> Dict:
        """
        Get summary statistics of received news.
        
        Returns:
            dict: News summary statistics
        """
        if not self.news_data:
            return {'total_articles': 0}
        
        providers = {}
        symbols = {}
        
        for news_item in self.news_data.values():
            # Count by provider
            provider = news_item['provider_code']
            providers[provider] = providers.get(provider, 0) + 1
            
            # Count by symbol
            ticker_id = news_item['ticker_id']
            contract = self.news_subscriptions.get(ticker_id)
            if contract:
                symbol = contract.symbol
                symbols[symbol] = symbols.get(symbol, 0) + 1
        
        # Get time range
        timestamps = [item['timestamp'] for item in self.news_data.values()]
        
        summary = {
            'total_articles': len(self.news_data),
            'providers': providers,
            'symbols': symbols,
            'time_range': {
                'earliest': min(timestamps) if timestamps else None,
                'latest': max(timestamps) if timestamps else None
            },
            'active_subscriptions': len(self.news_subscriptions)
        }
        
        return summary
    
    def add_news_callback(self, event_type: str, callback: Callable):
        """
        Add a callback for news events.
        
        Args:
            event_type (str): Type of event
            callback: Callback function to add
        """
        if event_type in self.news_callbacks:
            self.news_callbacks[event_type].append(callback)


def demonstrate_news_feeds():
    """
    Demonstrate news feeds functionality with practical examples.
    """
    print("=== News Feeds Demo ===")
    
    app = NewsFeedsApp()
    
    # Add callbacks for demonstration
    def on_news_tick(ticker_id, timestamp, provider_code, article_id, headline, extra_data):
        news_time = time.strftime('%H:%M:%S', time.localtime(timestamp))
        provider_name = app.news_providers.get(provider_code, provider_code)
        print(f"[{news_time}] {provider_name}: {headline}")
    
    def on_news_article(request_id, article_type, article_text):
        print(f"Full article received (Request {request_id}): {len(article_text)} characters")
    
    def on_providers_update(providers):
        print(f"News providers updated: {len(providers)} providers available")
    
    app.add_news_callback('news_tick', on_news_tick)
    app.add_news_callback('news_article', on_news_article)
    app.add_news_callback('news_providers_update', on_providers_update)
    
    try:
        # Connect to TWS
        app.connect("127.0.0.1", 7497, 0)
        
        # Start processing thread
        api_thread = threading.Thread(target=app.run, daemon=True)
        api_thread.start()
        
        # Wait for connection
        time.sleep(3)
        
        if app.isConnected():
            print("Connected - requesting news feeds")
            
            # Request news providers information
            app.request_news_providers()
            time.sleep(2)
            
            # Create sample contracts for news subscriptions
            from api.tws_api.doc.contracts.contract_creation import ContractBuilder
            builder = ContractBuilder()
            
            # Subscribe to news for popular stocks
            aapl_contract = builder.create_stock_contract("AAPL")
            if app.subscribe_news_feed(9001, aapl_contract):
                print("Subscribed to AAPL news")
            
            tsla_contract = builder.create_stock_contract("TSLA")
            if app.subscribe_news_feed(9002, tsla_contract):
                print("Subscribed to TSLA news")
            
            spy_contract = builder.create_stock_contract("SPY")
            if app.subscribe_news_feed(9003, spy_contract):
                print("Subscribed to SPY news")
            
            # Monitor news for a while
            print("Monitoring news feeds for 30 seconds...")
            time.sleep(30)
            
            # Show news summary
            summary = app.get_news_summary()
            print("\n--- News Summary ---")
            print(f"Total articles received: {summary['total_articles']}")
            print(f"Active subscriptions: {summary['active_subscriptions']}")
            
            if summary['providers']:
                print("Articles by provider:")
                for provider, count in summary['providers'].items():
                    provider_name = app.news_providers.get(provider, provider)
                    print(f"  {provider_name}: {count}")
            
            if summary['symbols']:
                print("Articles by symbol:")
                for symbol, count in summary['symbols'].items():
                    print(f"  {symbol}: {count}")
            
            # Show recent news for AAPL
            aapl_news = app.get_recent_news("AAPL", 1)  # Last 1 hour
            if aapl_news:
                print(f"\nRecent AAPL news ({len(aapl_news)} items):")
                for news_item in aapl_news[:3]:  # Show first 3
                    print(f"  [{news_item['time_formatted']}] {news_item['headline']}")
            
            # Cancel subscriptions
            for req_id in list(app.news_subscriptions.keys()):
                app.cancel_news_feed(req_id)
            print("News feed subscriptions canceled")
        
        else:
            print("Failed to connect to TWS")
    
    except KeyboardInterrupt:
        print("\nShutting down...")
    
    finally:
        # Cancel all active subscriptions
        for req_id in list(app.news_subscriptions.keys()):
            app.cancel_news_feed(req_id)
        
        app.disconnect()


if __name__ == "__main__":
    """
    Main execution block for news feeds demonstrations.
    """
    print("TWS API News Feeds Examples")
    print("=" * 35)
    
    # Run the demonstration
    demonstrate_news_feeds()
