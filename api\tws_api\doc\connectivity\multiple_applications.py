"""
Multiple Application Management for TWS API

This module demonstrates how to manage multiple API applications connecting
to a single TWS session. TWS supports up to 32 simultaneous API connections,
each identified by a unique client ID.

Key Topics Covered:
- Multi-client connection management
- Client ID allocation and tracking
- Application separation and isolation
- Resource sharing and coordination
- Connection monitoring across multiple clients
- Best practices for multi-application architectures

This is essential for complex trading systems that require multiple
specialized applications or services connecting to the same TWS instance.
"""

import threading
import time
import logging
from typing import Dict, List, Optional, Callable
from enum import Enum
from ibapi.client import EClient
from ibapi.wrapper import EWrapper


class ApplicationType(Enum):
    """Enumeration of different application types."""
    MARKET_DATA = "market_data"
    ORDER_MANAGEMENT = "order_management"
    ACCOUNT_MONITORING = "account_monitoring"
    RISK_MANAGEMENT = "risk_management"
    ANALYTICS = "analytics"
    GENERAL_PURPOSE = "general_purpose"


class ManagedIBApp(EClient, EWrapper):
    """
    Managed TWS API application with enhanced tracking and coordination.
    
    This class extends the basic TWS API application with features for
    multi-application environments, including application identification,
    resource tracking, and coordination capabilities.
    
    Attributes:
        app_name (str): Human-readable application name
        app_type (ApplicationType): Type/purpose of the application
        client_id (int): Unique client ID for this application
        manager (MultiClientManager): Reference to the managing instance
        connection_stats (dict): Connection and usage statistics
        active_requests (dict): Currently active requests
    """
    
    def __init__(self, app_name: str, app_type: ApplicationType, client_id: int):
        """
        Initialize a managed TWS API application.
        
        Args:
            app_name (str): Human-readable name for the application
            app_type (ApplicationType): Type/purpose of the application
            client_id (int): Unique client ID (0-31)
        """
        EClient.__init__(self, self)
        
        self.app_name = app_name
        self.app_type = app_type
        self.client_id = client_id
        self.manager = None
        
        # Connection and status tracking
        self.connected = False
        self.connection_time = None
        self.last_activity = time.time()
        
        # Statistics and monitoring
        self.connection_stats = {
            'connection_attempts': 0,
            'successful_connections': 0,
            'total_uptime': 0,
            'requests_sent': 0,
            'responses_received': 0,
            'errors_received': 0
        }
        
        # Active request tracking
        self.active_requests = {}
        self.next_req_id = 1000 + (client_id * 1000)  # Unique request ID range
        
        # Setup logging with application context
        self.logger = logging.getLogger(f"IBApp.{app_name}")
        self.logger.setLevel(logging.INFO)
    
    def set_manager(self, manager):
        """
        Set the multi-client manager reference.
        
        Args:
            manager (MultiClientManager): The managing instance
        """
        self.manager = manager
    
    def connectAck(self):
        """Handle successful connection acknowledgment."""
        self.connected = True
        self.connection_time = time.time()
        self.connection_stats['successful_connections'] += 1
        
        self.logger.info(f"Application '{self.app_name}' connected (Client ID: {self.client_id})")
        
        # Notify manager of successful connection
        if self.manager:
            self.manager._on_client_connected(self.client_id)
    
    def connectionClosed(self):
        """Handle connection closure."""
        if self.connected and self.connection_time:
            uptime = time.time() - self.connection_time
            self.connection_stats['total_uptime'] += uptime
        
        self.connected = False
        self.logger.warning(f"Application '{self.app_name}' disconnected")
        
        # Notify manager of disconnection
        if self.manager:
            self.manager._on_client_disconnected(self.client_id)
    
    def nextValidId(self, orderId):
        """
        Receive next valid order ID.
        
        Args:
            orderId (int): Next valid order ID from TWS
        """
        self.logger.info(f"Next valid order ID: {orderId}")
        self.last_activity = time.time()
    
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors and messages.
        
        Args:
            reqId (int): Request ID
            errorCode (int): Error code
            errorString (str): Error description
            advancedOrderRejectJson (str): Additional error details
        """
        self.connection_stats['errors_received'] += 1
        self.last_activity = time.time()
        
        self.logger.error(f"Error {reqId} {errorCode} {errorString}")
        
        # Remove from active requests if it's a request-specific error
        if reqId in self.active_requests:
            del self.active_requests[reqId]
    
    def get_next_request_id(self) -> int:
        """
        Get the next available request ID for this application.
        
        Returns:
            int: Unique request ID for this application
        """
        req_id = self.next_req_id
        self.next_req_id += 1
        return req_id
    
    def track_request(self, req_id: int, request_type: str, description: str = ""):
        """
        Track an active request.
        
        Args:
            req_id (int): Request ID
            request_type (str): Type of request
            description (str): Optional description
        """
        self.active_requests[req_id] = {
            'type': request_type,
            'description': description,
            'timestamp': time.time()
        }
        
        self.connection_stats['requests_sent'] += 1
        self.last_activity = time.time()
    
    def complete_request(self, req_id: int):
        """
        Mark a request as completed.
        
        Args:
            req_id (int): Request ID to complete
        """
        if req_id in self.active_requests:
            del self.active_requests[req_id]
        
        self.connection_stats['responses_received'] += 1
        self.last_activity = time.time()
    
    def get_application_status(self) -> dict:
        """
        Get comprehensive application status.
        
        Returns:
            dict: Application status and statistics
        """
        status = {
            'app_name': self.app_name,
            'app_type': self.app_type.value,
            'client_id': self.client_id,
            'connected': self.connected,
            'connection_time': self.connection_time,
            'last_activity': self.last_activity,
            'active_requests_count': len(self.active_requests),
            'stats': self.connection_stats.copy()
        }
        
        # Calculate current session uptime
        if self.connected and self.connection_time:
            status['current_uptime'] = time.time() - self.connection_time
        
        return status


class MultiClientManager:
    """
    Manager for multiple TWS API client applications.
    
    This class coordinates multiple API applications connecting to a single
    TWS session, handling client ID allocation, connection management,
    and inter-application coordination.
    
    Attributes:
        host (str): TWS host address
        port (int): TWS port number
        clients (dict): Dictionary of managed client applications
        available_client_ids (list): Available client IDs for allocation
        connection_callbacks (dict): Callbacks for connection events
    """
    
    def __init__(self, host: str = "127.0.0.1", port: int = 7497):
        """
        Initialize the multi-client manager.
        
        Args:
            host (str): TWS host address
            port (int): TWS port number
        """
        self.host = host
        self.port = port
        
        # Client management
        self.clients: Dict[int, ManagedIBApp] = {}
        self.available_client_ids = list(range(32))  # Client IDs 0-31
        
        # Event callbacks
        self.connection_callbacks: Dict[str, List[Callable]] = {
            'client_connected': [],
            'client_disconnected': [],
            'all_clients_connected': [],
            'client_error': []
        }
        
        # Statistics
        self.manager_stats = {
            'total_clients_created': 0,
            'peak_concurrent_clients': 0,
            'total_connection_time': 0
        }
        
        self.logger = logging.getLogger("MultiClientManager")
    
    def create_client(self, app_name: str, app_type: ApplicationType, 
                     client_id: Optional[int] = None) -> Optional[ManagedIBApp]:
        """
        Create a new managed client application.
        
        Args:
            app_name (str): Human-readable application name
            app_type (ApplicationType): Type/purpose of the application
            client_id (int, optional): Specific client ID to use
            
        Returns:
            ManagedIBApp: Created application instance, or None if failed
        """
        # Allocate client ID
        if client_id is not None:
            if client_id in self.clients or client_id not in self.available_client_ids:
                self.logger.error(f"Client ID {client_id} not available")
                return None
            allocated_id = client_id
            self.available_client_ids.remove(client_id)
        else:
            if not self.available_client_ids:
                self.logger.error("No available client IDs")
                return None
            allocated_id = self.available_client_ids.pop(0)
        
        try:
            # Create the application
            app = ManagedIBApp(app_name, app_type, allocated_id)
            app.set_manager(self)
            
            # Connect to TWS
            self.logger.info(f"Connecting {app_name} (ID: {allocated_id}) to {self.host}:{self.port}")
            app.connect(self.host, self.port, allocated_id)
            
            # Start message processing thread
            processing_thread = threading.Thread(target=app.run, daemon=True)
            processing_thread.start()
            
            # Add to managed clients
            self.clients[allocated_id] = app
            self.manager_stats['total_clients_created'] += 1
            
            # Update peak concurrent clients
            current_count = len(self.clients)
            if current_count > self.manager_stats['peak_concurrent_clients']:
                self.manager_stats['peak_concurrent_clients'] = current_count
            
            self.logger.info(f"Created client '{app_name}' with ID {allocated_id}")
            return app
            
        except Exception as e:
            # Return client ID to available pool on failure
            self.available_client_ids.append(allocated_id)
            self.available_client_ids.sort()
            self.logger.error(f"Failed to create client '{app_name}': {e}")
            return None
    
    def disconnect_client(self, client_id: int):
        """
        Disconnect a specific client application.
        
        Args:
            client_id (int): Client ID to disconnect
        """
        if client_id in self.clients:
            app = self.clients[client_id]
            app.disconnect()
            
            # Remove from managed clients
            del self.clients[client_id]
            
            # Return client ID to available pool
            self.available_client_ids.append(client_id)
            self.available_client_ids.sort()
            
            self.logger.info(f"Disconnected client {client_id} ({app.app_name})")
    
    def disconnect_all_clients(self):
        """Disconnect all managed client applications."""
        client_ids = list(self.clients.keys())
        for client_id in client_ids:
            self.disconnect_client(client_id)
        
        self.logger.info("All clients disconnected")
    
    def get_client(self, client_id: int) -> Optional[ManagedIBApp]:
        """
        Get a specific client application.
        
        Args:
            client_id (int): Client ID to retrieve
            
        Returns:
            ManagedIBApp: Client application or None if not found
        """
        return self.clients.get(client_id)
    
    def get_clients_by_type(self, app_type: ApplicationType) -> List[ManagedIBApp]:
        """
        Get all clients of a specific type.
        
        Args:
            app_type (ApplicationType): Application type to filter by
            
        Returns:
            list: List of matching client applications
        """
        return [app for app in self.clients.values() if app.app_type == app_type]
    
    def list_clients(self) -> Dict[int, dict]:
        """
        List all managed clients with their status.
        
        Returns:
            dict: Dictionary of client information by client ID
        """
        return {
            client_id: app.get_application_status()
            for client_id, app in self.clients.items()
        }
    
    def add_connection_callback(self, event_type: str, callback: Callable):
        """
        Add a callback for connection events.
        
        Args:
            event_type (str): Type of event ('client_connected', 'client_disconnected', etc.)
            callback (Callable): Callback function to add
        """
        if event_type in self.connection_callbacks:
            self.connection_callbacks[event_type].append(callback)
    
    def _on_client_connected(self, client_id: int):
        """Handle client connection event."""
        for callback in self.connection_callbacks['client_connected']:
            try:
                callback(client_id, self.clients[client_id])
            except Exception as e:
                self.logger.error(f"Connection callback error: {e}")
    
    def _on_client_disconnected(self, client_id: int):
        """Handle client disconnection event."""
        for callback in self.connection_callbacks['client_disconnected']:
            try:
                callback(client_id)
            except Exception as e:
                self.logger.error(f"Disconnection callback error: {e}")
    
    def get_manager_statistics(self) -> dict:
        """
        Get comprehensive manager statistics.
        
        Returns:
            dict: Manager statistics and metrics
        """
        stats = self.manager_stats.copy()
        stats.update({
            'current_clients': len(self.clients),
            'available_client_ids': len(self.available_client_ids),
            'connected_clients': len([app for app in self.clients.values() if app.connected]),
            'client_types': {}
        })
        
        # Count clients by type
        for app in self.clients.values():
            app_type = app.app_type.value
            stats['client_types'][app_type] = stats['client_types'].get(app_type, 0) + 1
        
        return stats


def demonstrate_multi_client_management():
    """
    Demonstrate multi-client management capabilities.
    """
    print("=== Multi-Client Management Demo ===")
    
    # Create the multi-client manager
    manager = MultiClientManager("127.0.0.1", 7497)
    
    # Add connection event callbacks
    def on_client_connected(client_id, app):
        print(f"Callback: Client {client_id} ({app.app_name}) connected")
    
    def on_client_disconnected(client_id):
        print(f"Callback: Client {client_id} disconnected")
    
    manager.add_connection_callback('client_connected', on_client_connected)
    manager.add_connection_callback('client_disconnected', on_client_disconnected)
    
    try:
        # Create multiple specialized clients
        market_data_client = manager.create_client(
            "MarketDataService", 
            ApplicationType.MARKET_DATA
        )
        
        order_client = manager.create_client(
            "OrderManagement", 
            ApplicationType.ORDER_MANAGEMENT
        )
        
        account_client = manager.create_client(
            "AccountMonitor", 
            ApplicationType.ACCOUNT_MONITORING
        )
        
        print(f"\nCreated {len(manager.clients)} client applications")
        
        # Wait for connections to establish
        time.sleep(5)
        
        # Show client status
        print("\n--- Client Status ---")
        clients = manager.list_clients()
        for client_id, status in clients.items():
            print(f"Client {client_id}: {status['app_name']} ({status['app_type']}) - "
                  f"Connected: {status['connected']}")
        
        # Show manager statistics
        print("\n--- Manager Statistics ---")
        stats = manager.get_manager_statistics()
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # Demonstrate client-specific operations
        if market_data_client and market_data_client.connected:
            print(f"\nMarket data client ready for data requests")
            # market_data_client.reqMktData(...)  # Would make actual requests here
        
        if order_client and order_client.connected:
            print(f"Order client ready for order management")
            # order_client.placeOrder(...)  # Would place orders here
        
        # Keep running briefly
        time.sleep(5)
        
    finally:
        # Cleanup
        manager.disconnect_all_clients()
        print("All clients disconnected")


if __name__ == "__main__":
    """
    Main execution block for multi-application demonstrations.
    """
    print("TWS API Multiple Application Management Examples")
    print("=" * 50)
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run the demonstration
    demonstrate_multi_client_management()
