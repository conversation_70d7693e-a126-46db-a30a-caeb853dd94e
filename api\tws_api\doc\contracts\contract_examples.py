"""
Contract Examples for Different Instrument Types

This module provides comprehensive examples of contract creation for
various types of financial instruments supported by the TWS API.
Each example demonstrates the specific fields and requirements for
different security types.

Key Topics Covered:
- Stock contracts (domestic and international)
- Option contracts (equity and index options)
- Futures contracts (commodities, indices, currencies)
- Forex contracts (major and minor pairs)
- Bond contracts (government and corporate)
- Index contracts and CFDs
- Cryptocurrency contracts
- Warrant and structured product contracts

These examples serve as templates for creating contracts in real applications
and demonstrate best practices for different instrument types.
"""

from ibapi.contract import Contract
from typing import List, Dict


class ContractExamples:
    """
    Collection of contract examples for different instrument types.
    
    This class provides static methods that return properly configured
    contract objects for various financial instruments, serving as
    templates for real-world usage.
    """
    
    @staticmethod
    def us_stocks() -> List[Dict[str, Contract]]:
        """
        Examples of US stock contracts.
        
        Returns:
            list: List of dictionaries with stock contract examples
        """
        examples = []
        
        # Apple Inc. - NASDAQ
        aapl = Contract()
        aapl.symbol = "AAPL"
        aapl.secType = "STK"
        aapl.exchange = "SMART"
        aapl.currency = "USD"
        aapl.primaryExchange = "NASDAQ"
        examples.append({"name": "Apple Inc. (AAPL)", "contract": aapl})
        
        # Microsoft Corporation - NASDAQ
        msft = Contract()
        msft.symbol = "MSFT"
        msft.secType = "STK"
        msft.exchange = "SMART"
        msft.currency = "USD"
        msft.primaryExchange = "NASDAQ"
        examples.append({"name": "Microsoft Corporation (MSFT)", "contract": msft})
        
        # Tesla Inc. - NASDAQ
        tsla = Contract()
        tsla.symbol = "TSLA"
        tsla.secType = "STK"
        tsla.exchange = "SMART"
        tsla.currency = "USD"
        tsla.primaryExchange = "NASDAQ"
        examples.append({"name": "Tesla Inc. (TSLA)", "contract": tsla})
        
        # Berkshire Hathaway Class A - NYSE
        brka = Contract()
        brka.symbol = "BRK A"
        brka.secType = "STK"
        brka.exchange = "SMART"
        brka.currency = "USD"
        brka.primaryExchange = "NYSE"
        examples.append({"name": "Berkshire Hathaway Class A (BRK A)", "contract": brka})
        
        return examples
    
    @staticmethod
    def international_stocks() -> List[Dict[str, Contract]]:
        """
        Examples of international stock contracts.
        
        Returns:
            list: List of dictionaries with international stock examples
        """
        examples = []
        
        # Toyota Motor Corporation - Tokyo Stock Exchange
        toyota = Contract()
        toyota.symbol = "7203"
        toyota.secType = "STK"
        toyota.exchange = "TSE"
        toyota.currency = "JPY"
        examples.append({"name": "Toyota Motor Corp (7203.TSE)", "contract": toyota})
        
        # ASML Holding - Euronext Amsterdam
        asml = Contract()
        asml.symbol = "ASML"
        asml.secType = "STK"
        asml.exchange = "AEB"
        asml.currency = "EUR"
        examples.append({"name": "ASML Holding (ASML.AEB)", "contract": asml})
        
        # Shopify Inc. - Toronto Stock Exchange
        shop = Contract()
        shop.symbol = "SHOP"
        shop.secType = "STK"
        shop.exchange = "TSE"
        shop.currency = "CAD"
        examples.append({"name": "Shopify Inc. (SHOP.TSE)", "contract": shop})
        
        return examples
    
    @staticmethod
    def equity_options() -> List[Dict[str, Contract]]:
        """
        Examples of equity option contracts.
        
        Returns:
            list: List of dictionaries with equity option examples
        """
        examples = []
        
        # Apple Call Option
        aapl_call = Contract()
        aapl_call.symbol = "AAPL"
        aapl_call.secType = "OPT"
        aapl_call.exchange = "SMART"
        aapl_call.currency = "USD"
        aapl_call.lastTradeDateOrContractMonth = "20241220"
        aapl_call.strike = 150.0
        aapl_call.right = "C"
        aapl_call.multiplier = "100"
        examples.append({"name": "AAPL Dec 2024 $150 Call", "contract": aapl_call})
        
        # Tesla Put Option
        tsla_put = Contract()
        tsla_put.symbol = "TSLA"
        tsla_put.secType = "OPT"
        tsla_put.exchange = "SMART"
        tsla_put.currency = "USD"
        tsla_put.lastTradeDateOrContractMonth = "20241115"
        tsla_put.strike = 200.0
        tsla_put.right = "P"
        tsla_put.multiplier = "100"
        examples.append({"name": "TSLA Nov 2024 $200 Put", "contract": tsla_put})
        
        # Weekly SPY Option
        spy_weekly = Contract()
        spy_weekly.symbol = "SPY"
        spy_weekly.secType = "OPT"
        spy_weekly.exchange = "SMART"
        spy_weekly.currency = "USD"
        spy_weekly.lastTradeDateOrContractMonth = "20241101"
        spy_weekly.strike = 450.0
        spy_weekly.right = "C"
        spy_weekly.multiplier = "100"
        spy_weekly.tradingClass = "SPY"
        examples.append({"name": "SPY Weekly Nov 1 2024 $450 Call", "contract": spy_weekly})
        
        return examples
    
    @staticmethod
    def index_options() -> List[Dict[str, Contract]]:
        """
        Examples of index option contracts.
        
        Returns:
            list: List of dictionaries with index option examples
        """
        examples = []
        
        # SPX Index Option
        spx_call = Contract()
        spx_call.symbol = "SPX"
        spx_call.secType = "OPT"
        spx_call.exchange = "SMART"
        spx_call.currency = "USD"
        spx_call.lastTradeDateOrContractMonth = "20241220"
        spx_call.strike = 4500.0
        spx_call.right = "C"
        spx_call.multiplier = "100"
        examples.append({"name": "SPX Dec 2024 $4500 Call", "contract": spx_call})
        
        # NDX Index Option
        ndx_put = Contract()
        ndx_put.symbol = "NDX"
        ndx_put.secType = "OPT"
        ndx_put.exchange = "SMART"
        ndx_put.currency = "USD"
        ndx_put.lastTradeDateOrContractMonth = "20241115"
        ndx_put.strike = 15000.0
        ndx_put.right = "P"
        ndx_put.multiplier = "100"
        examples.append({"name": "NDX Nov 2024 $15000 Put", "contract": ndx_put})
        
        return examples
    
    @staticmethod
    def futures_contracts() -> List[Dict[str, Contract]]:
        """
        Examples of futures contracts.
        
        Returns:
            list: List of dictionaries with futures contract examples
        """
        examples = []
        
        # E-mini S&P 500 Future
        es = Contract()
        es.symbol = "ES"
        es.secType = "FUT"
        es.exchange = "CME"
        es.currency = "USD"
        es.lastTradeDateOrContractMonth = "202412"
        examples.append({"name": "E-mini S&P 500 Dec 2024", "contract": es})
        
        # Crude Oil Future
        cl = Contract()
        cl.symbol = "CL"
        cl.secType = "FUT"
        cl.exchange = "NYMEX"
        cl.currency = "USD"
        cl.lastTradeDateOrContractMonth = "202412"
        examples.append({"name": "Crude Oil Dec 2024", "contract": cl})
        
        # Euro FX Future
        ec = Contract()
        ec.symbol = "EC"
        ec.secType = "FUT"
        ec.exchange = "CME"
        ec.currency = "USD"
        ec.lastTradeDateOrContractMonth = "202412"
        examples.append({"name": "Euro FX Dec 2024", "contract": ec})
        
        # Gold Future
        gc = Contract()
        gc.symbol = "GC"
        gc.secType = "FUT"
        gc.exchange = "COMEX"
        gc.currency = "USD"
        gc.lastTradeDateOrContractMonth = "202412"
        examples.append({"name": "Gold Dec 2024", "contract": gc})
        
        return examples
    
    @staticmethod
    def forex_contracts() -> List[Dict[str, Contract]]:
        """
        Examples of forex contracts.
        
        Returns:
            list: List of dictionaries with forex contract examples
        """
        examples = []
        
        # EUR/USD
        eurusd = Contract()
        eurusd.symbol = "EUR"
        eurusd.secType = "CASH"
        eurusd.exchange = "IDEALPRO"
        eurusd.currency = "USD"
        examples.append({"name": "EUR/USD", "contract": eurusd})
        
        # GBP/USD
        gbpusd = Contract()
        gbpusd.symbol = "GBP"
        gbpusd.secType = "CASH"
        gbpusd.exchange = "IDEALPRO"
        gbpusd.currency = "USD"
        examples.append({"name": "GBP/USD", "contract": gbpusd})
        
        # USD/JPY
        usdjpy = Contract()
        usdjpy.symbol = "USD"
        usdjpy.secType = "CASH"
        usdjpy.exchange = "IDEALPRO"
        usdjpy.currency = "JPY"
        examples.append({"name": "USD/JPY", "contract": usdjpy})
        
        # AUD/CAD
        audcad = Contract()
        audcad.symbol = "AUD"
        audcad.secType = "CASH"
        audcad.exchange = "IDEALPRO"
        audcad.currency = "CAD"
        examples.append({"name": "AUD/CAD", "contract": audcad})
        
        return examples
    
    @staticmethod
    def index_contracts() -> List[Dict[str, Contract]]:
        """
        Examples of index contracts.
        
        Returns:
            list: List of dictionaries with index contract examples
        """
        examples = []
        
        # S&P 500 Index
        spx = Contract()
        spx.symbol = "SPX"
        spx.secType = "IND"
        spx.exchange = "CBOE"
        spx.currency = "USD"
        examples.append({"name": "S&P 500 Index", "contract": spx})
        
        # NASDAQ 100 Index
        ndx = Contract()
        ndx.symbol = "NDX"
        ndx.secType = "IND"
        ndx.exchange = "NASDAQ"
        ndx.currency = "USD"
        examples.append({"name": "NASDAQ 100 Index", "contract": ndx})
        
        # VIX Index
        vix = Contract()
        vix.symbol = "VIX"
        vix.secType = "IND"
        vix.exchange = "CBOE"
        vix.currency = "USD"
        examples.append({"name": "VIX Volatility Index", "contract": vix})
        
        return examples
    
    @staticmethod
    def bond_contracts() -> List[Dict[str, Contract]]:
        """
        Examples of bond contracts.
        
        Returns:
            list: List of dictionaries with bond contract examples
        """
        examples = []
        
        # US Treasury Bond
        us_bond = Contract()
        us_bond.symbol = "912828XG0"  # Example CUSIP
        us_bond.secType = "BOND"
        us_bond.exchange = "SMART"
        us_bond.currency = "USD"
        examples.append({"name": "US Treasury Bond", "contract": us_bond})
        
        return examples
    
    @staticmethod
    def cfd_contracts() -> List[Dict[str, Contract]]:
        """
        Examples of CFD contracts.
        
        Returns:
            list: List of dictionaries with CFD contract examples
        """
        examples = []
        
        # AAPL CFD
        aapl_cfd = Contract()
        aapl_cfd.symbol = "AAPL"
        aapl_cfd.secType = "CFD"
        aapl_cfd.exchange = "SMART"
        aapl_cfd.currency = "USD"
        examples.append({"name": "Apple CFD", "contract": aapl_cfd})
        
        # SPY CFD
        spy_cfd = Contract()
        spy_cfd.symbol = "SPY"
        spy_cfd.secType = "CFD"
        spy_cfd.exchange = "SMART"
        spy_cfd.currency = "USD"
        examples.append({"name": "SPDR S&P 500 ETF CFD", "contract": spy_cfd})
        
        return examples


def demonstrate_contract_examples():
    """
    Demonstrate various contract examples and their usage.
    """
    print("=== Contract Examples Demo ===")
    
    examples = ContractExamples()
    
    # Display all contract categories
    categories = [
        ("US Stocks", examples.us_stocks()),
        ("International Stocks", examples.international_stocks()),
        ("Equity Options", examples.equity_options()),
        ("Index Options", examples.index_options()),
        ("Futures", examples.futures_contracts()),
        ("Forex", examples.forex_contracts()),
        ("Indices", examples.index_contracts()),
        ("Bonds", examples.bond_contracts()),
        ("CFDs", examples.cfd_contracts())
    ]
    
    for category_name, contract_list in categories:
        print(f"\n--- {category_name} ---")
        for item in contract_list:
            name = item["name"]
            contract = item["contract"]
            
            # Create contract description
            desc_parts = []
            if hasattr(contract, 'symbol') and contract.symbol:
                desc_parts.append(f"Symbol: {contract.symbol}")
            if hasattr(contract, 'secType') and contract.secType:
                desc_parts.append(f"Type: {contract.secType}")
            if hasattr(contract, 'exchange') and contract.exchange:
                desc_parts.append(f"Exchange: {contract.exchange}")
            if hasattr(contract, 'currency') and contract.currency:
                desc_parts.append(f"Currency: {contract.currency}")
            
            # Add derivative-specific fields
            if contract.secType == "OPT":
                if hasattr(contract, 'lastTradeDateOrContractMonth') and contract.lastTradeDateOrContractMonth:
                    desc_parts.append(f"Expiry: {contract.lastTradeDateOrContractMonth}")
                if hasattr(contract, 'strike') and contract.strike:
                    desc_parts.append(f"Strike: {contract.strike}")
                if hasattr(contract, 'right') and contract.right:
                    desc_parts.append(f"Right: {contract.right}")
            
            elif contract.secType == "FUT":
                if hasattr(contract, 'lastTradeDateOrContractMonth') and contract.lastTradeDateOrContractMonth:
                    desc_parts.append(f"Expiry: {contract.lastTradeDateOrContractMonth}")
            
            description = ", ".join(desc_parts)
            print(f"  {name}")
            print(f"    {description}")
    
    # Show contract field summary
    print(f"\n--- Contract Field Summary ---")
    print("Required fields for different security types:")
    print("  STK: symbol, secType, exchange, currency, [primaryExchange]")
    print("  OPT: symbol, secType, exchange, currency, expiry, strike, right, [multiplier]")
    print("  FUT: symbol, secType, exchange, currency, expiry, [multiplier]")
    print("  CASH: symbol, secType, exchange, currency")
    print("  IND: symbol, secType, exchange, currency")
    print("  BOND: symbol, secType, exchange, currency")
    print("  CFD: symbol, secType, exchange, currency")


if __name__ == "__main__":
    """
    Main execution block for contract examples demonstrations.
    """
    print("TWS API Contract Examples")
    print("=" * 30)
    
    # Run the demonstration
    demonstrate_contract_examples()
