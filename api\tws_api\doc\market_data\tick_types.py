"""
Tick Types and Market Data Fields

This module provides comprehensive documentation and examples for understanding
different tick types and market data fields in the TWS API. Tick types define
the specific type of market data being received and their meanings.

Key Topics Covered:
- Understanding all available tick types and their meanings
- Processing different categories of market data
- Working with price, size, and string tick types
- Understanding generic ticks and additional data fields
- Handling option-specific and futures-specific tick types
- Best practices for tick type processing and filtering

Understanding tick types is essential for properly interpreting market data
and building robust trading applications that can handle all types of
market information.
"""

import logging
from typing import Dict, List, Optional, Set
from enum import Enum


class TickTypeCategory(Enum):
    """Categories of tick types for organization and filtering."""
    PRICE = "price"
    SIZE = "size"
    STRING = "string"
    GENERIC = "generic"
    OPTION = "option"
    FUTURES = "futures"
    DELAYED = "delayed"
    VOLUME = "volume"
    TIME = "time"
    EXCHANGE = "exchange"


class TickTypeManager:
    """
    Comprehensive manager for tick types and their properties.
    
    This class provides detailed information about all tick types,
    their categories, meanings, and usage patterns.
    
    Attributes:
        tick_types (dict): Complete mapping of tick type IDs to information
        categories (dict): Tick types organized by category
        logger: Logger instance for debugging
    """
    
    def __init__(self):
        """Initialize the tick type manager with comprehensive tick type data."""
        self.logger = logging.getLogger(__name__)
        
        # Complete tick type definitions with detailed information
        self.tick_types = {
            # Basic Price Ticks
            0: {
                'name': 'BID_SIZE',
                'description': 'Number of shares or contracts offered at the bid price',
                'category': TickTypeCategory.SIZE,
                'data_type': 'int',
                'units': 'shares/contracts',
                'frequency': 'high'
            },
            1: {
                'name': 'BID',
                'description': 'Highest price a buyer is willing to pay',
                'category': TickTypeCategory.PRICE,
                'data_type': 'float',
                'units': 'currency',
                'frequency': 'high'
            },
            2: {
                'name': 'ASK',
                'description': 'Lowest price a seller is willing to accept',
                'category': TickTypeCategory.PRICE,
                'data_type': 'float',
                'units': 'currency',
                'frequency': 'high'
            },
            3: {
                'name': 'ASK_SIZE',
                'description': 'Number of shares or contracts offered at the ask price',
                'category': TickTypeCategory.SIZE,
                'data_type': 'int',
                'units': 'shares/contracts',
                'frequency': 'high'
            },
            4: {
                'name': 'LAST',
                'description': 'Price of the last trade',
                'category': TickTypeCategory.PRICE,
                'data_type': 'float',
                'units': 'currency',
                'frequency': 'high'
            },
            5: {
                'name': 'LAST_SIZE',
                'description': 'Number of shares or contracts in the last trade',
                'category': TickTypeCategory.SIZE,
                'data_type': 'int',
                'units': 'shares/contracts',
                'frequency': 'high'
            },
            6: {
                'name': 'HIGH',
                'description': 'Highest price traded during the current trading day',
                'category': TickTypeCategory.PRICE,
                'data_type': 'float',
                'units': 'currency',
                'frequency': 'medium'
            },
            7: {
                'name': 'LOW',
                'description': 'Lowest price traded during the current trading day',
                'category': TickTypeCategory.PRICE,
                'data_type': 'float',
                'units': 'currency',
                'frequency': 'medium'
            },
            8: {
                'name': 'VOLUME',
                'description': 'Total number of shares or contracts traded today',
                'category': TickTypeCategory.VOLUME,
                'data_type': 'int',
                'units': 'shares/contracts',
                'frequency': 'high'
            },
            9: {
                'name': 'CLOSE',
                'description': 'Previous day\'s closing price',
                'category': TickTypeCategory.PRICE,
                'data_type': 'float',
                'units': 'currency',
                'frequency': 'low'
            },
            14: {
                'name': 'OPEN',
                'description': 'Opening price for the current trading day',
                'category': TickTypeCategory.PRICE,
                'data_type': 'float',
                'units': 'currency',
                'frequency': 'low'
            },
            
            # Weekly and Historical Data
            15: {
                'name': 'LOW_13_WEEK',
                'description': '13-week low price',
                'category': TickTypeCategory.PRICE,
                'data_type': 'float',
                'units': 'currency',
                'frequency': 'low'
            },
            16: {
                'name': 'HIGH_13_WEEK',
                'description': '13-week high price',
                'category': TickTypeCategory.PRICE,
                'data_type': 'float',
                'units': 'currency',
                'frequency': 'low'
            },
            17: {
                'name': 'LOW_26_WEEK',
                'description': '26-week low price',
                'category': TickTypeCategory.PRICE,
                'data_type': 'float',
                'units': 'currency',
                'frequency': 'low'
            },
            18: {
                'name': 'HIGH_26_WEEK',
                'description': '26-week high price',
                'category': TickTypeCategory.PRICE,
                'data_type': 'float',
                'units': 'currency',
                'frequency': 'low'
            },
            19: {
                'name': 'LOW_52_WEEK',
                'description': '52-week low price',
                'category': TickTypeCategory.PRICE,
                'data_type': 'float',
                'units': 'currency',
                'frequency': 'low'
            },
            20: {
                'name': 'HIGH_52_WEEK',
                'description': '52-week high price',
                'category': TickTypeCategory.PRICE,
                'data_type': 'float',
                'units': 'currency',
                'frequency': 'low'
            },
            
            # Volume and Statistics
            21: {
                'name': 'AVG_VOLUME',
                'description': 'Average daily trading volume',
                'category': TickTypeCategory.VOLUME,
                'data_type': 'int',
                'units': 'shares/contracts',
                'frequency': 'low'
            },
            22: {
                'name': 'OPEN_INTEREST',
                'description': 'Total number of outstanding derivative contracts',
                'category': TickTypeCategory.VOLUME,
                'data_type': 'int',
                'units': 'contracts',
                'frequency': 'low'
            },
            
            # Option-Specific Ticks
            23: {
                'name': 'OPTION_HISTORICAL_VOL',
                'description': 'Historical volatility for options',
                'category': TickTypeCategory.OPTION,
                'data_type': 'float',
                'units': 'percentage',
                'frequency': 'low'
            },
            24: {
                'name': 'OPTION_IMPLIED_VOL',
                'description': 'Implied volatility for options',
                'category': TickTypeCategory.OPTION,
                'data_type': 'float',
                'units': 'percentage',
                'frequency': 'medium'
            },
            27: {
                'name': 'OPTION_CALL_OPEN_INTEREST',
                'description': 'Open interest for call options',
                'category': TickTypeCategory.OPTION,
                'data_type': 'int',
                'units': 'contracts',
                'frequency': 'low'
            },
            28: {
                'name': 'OPTION_PUT_OPEN_INTEREST',
                'description': 'Open interest for put options',
                'category': TickTypeCategory.OPTION,
                'data_type': 'int',
                'units': 'contracts',
                'frequency': 'low'
            },
            29: {
                'name': 'OPTION_CALL_VOLUME',
                'description': 'Trading volume for call options',
                'category': TickTypeCategory.OPTION,
                'data_type': 'int',
                'units': 'contracts',
                'frequency': 'medium'
            },
            30: {
                'name': 'OPTION_PUT_VOLUME',
                'description': 'Trading volume for put options',
                'category': TickTypeCategory.OPTION,
                'data_type': 'int',
                'units': 'contracts',
                'frequency': 'medium'
            },
            
            # Exchange Information
            32: {
                'name': 'BID_EXCH',
                'description': 'Exchange providing the best bid',
                'category': TickTypeCategory.EXCHANGE,
                'data_type': 'string',
                'units': 'exchange_code',
                'frequency': 'high'
            },
            33: {
                'name': 'ASK_EXCH',
                'description': 'Exchange providing the best ask',
                'category': TickTypeCategory.EXCHANGE,
                'data_type': 'string',
                'units': 'exchange_code',
                'frequency': 'high'
            },
            84: {
                'name': 'LAST_EXCH',
                'description': 'Exchange where the last trade occurred',
                'category': TickTypeCategory.EXCHANGE,
                'data_type': 'string',
                'units': 'exchange_code',
                'frequency': 'high'
            },
            
            # Time-Related Ticks
            45: {
                'name': 'LAST_TIMESTAMP',
                'description': 'Timestamp of the last trade',
                'category': TickTypeCategory.TIME,
                'data_type': 'int',
                'units': 'unix_timestamp',
                'frequency': 'high'
            },
            85: {
                'name': 'LAST_REG_TIME',
                'description': 'Time of last trade during regular trading hours',
                'category': TickTypeCategory.TIME,
                'data_type': 'string',
                'units': 'time_string',
                'frequency': 'medium'
            },
            
            # Real-Time Volume and Trade Data
            48: {
                'name': 'RT_VOLUME',
                'description': 'Real-time volume data with last trade info',
                'category': TickTypeCategory.STRING,
                'data_type': 'string',
                'units': 'formatted_string',
                'frequency': 'high'
            },
            54: {
                'name': 'TRADE_COUNT',
                'description': 'Number of trades executed today',
                'category': TickTypeCategory.VOLUME,
                'data_type': 'int',
                'units': 'count',
                'frequency': 'medium'
            },
            55: {
                'name': 'TRADE_RATE',
                'description': 'Average number of trades per minute',
                'category': TickTypeCategory.VOLUME,
                'data_type': 'float',
                'units': 'trades_per_minute',
                'frequency': 'medium'
            },
            56: {
                'name': 'VOLUME_RATE',
                'description': 'Average volume per minute',
                'category': TickTypeCategory.VOLUME,
                'data_type': 'float',
                'units': 'volume_per_minute',
                'frequency': 'medium'
            },
            
            # Delayed Data Ticks
            66: {
                'name': 'DELAYED_BID',
                'description': 'Delayed bid price (15-20 minute delay)',
                'category': TickTypeCategory.DELAYED,
                'data_type': 'float',
                'units': 'currency',
                'frequency': 'high'
            },
            67: {
                'name': 'DELAYED_ASK',
                'description': 'Delayed ask price (15-20 minute delay)',
                'category': TickTypeCategory.DELAYED,
                'data_type': 'float',
                'units': 'currency',
                'frequency': 'high'
            },
            68: {
                'name': 'DELAYED_LAST',
                'description': 'Delayed last trade price (15-20 minute delay)',
                'category': TickTypeCategory.DELAYED,
                'data_type': 'float',
                'units': 'currency',
                'frequency': 'high'
            },
            69: {
                'name': 'DELAYED_BID_SIZE',
                'description': 'Delayed bid size (15-20 minute delay)',
                'category': TickTypeCategory.DELAYED,
                'data_type': 'int',
                'units': 'shares/contracts',
                'frequency': 'high'
            },
            70: {
                'name': 'DELAYED_ASK_SIZE',
                'description': 'Delayed ask size (15-20 minute delay)',
                'category': TickTypeCategory.DELAYED,
                'data_type': 'int',
                'units': 'shares/contracts',
                'frequency': 'high'
            },
            71: {
                'name': 'DELAYED_LAST_SIZE',
                'description': 'Delayed last trade size (15-20 minute delay)',
                'category': TickTypeCategory.DELAYED,
                'data_type': 'int',
                'units': 'shares/contracts',
                'frequency': 'high'
            },
            72: {
                'name': 'DELAYED_HIGH',
                'description': 'Delayed daily high price (15-20 minute delay)',
                'category': TickTypeCategory.DELAYED,
                'data_type': 'float',
                'units': 'currency',
                'frequency': 'medium'
            },
            73: {
                'name': 'DELAYED_LOW',
                'description': 'Delayed daily low price (15-20 minute delay)',
                'category': TickTypeCategory.DELAYED,
                'data_type': 'float',
                'units': 'currency',
                'frequency': 'medium'
            },
            74: {
                'name': 'DELAYED_VOLUME',
                'description': 'Delayed daily volume (15-20 minute delay)',
                'category': TickTypeCategory.DELAYED,
                'data_type': 'int',
                'units': 'shares/contracts',
                'frequency': 'medium'
            },
            75: {
                'name': 'DELAYED_CLOSE',
                'description': 'Delayed previous close price (15-20 minute delay)',
                'category': TickTypeCategory.DELAYED,
                'data_type': 'float',
                'units': 'currency',
                'frequency': 'low'
            },
            76: {
                'name': 'DELAYED_OPEN',
                'description': 'Delayed opening price (15-20 minute delay)',
                'category': TickTypeCategory.DELAYED,
                'data_type': 'float',
                'units': 'currency',
                'frequency': 'low'
            },
            
            # Futures-Specific
            86: {
                'name': 'FUTURES_OPEN_INTEREST',
                'description': 'Open interest for futures contracts',
                'category': TickTypeCategory.FUTURES,
                'data_type': 'int',
                'units': 'contracts',
                'frequency': 'low'
            },
            
            # Additional Volume Metrics
            63: {
                'name': 'SHORT_TERM_VOLUME_3_MIN',
                'description': 'Volume over the last 3 minutes',
                'category': TickTypeCategory.VOLUME,
                'data_type': 'int',
                'units': 'shares/contracts',
                'frequency': 'medium'
            },
            64: {
                'name': 'SHORT_TERM_VOLUME_5_MIN',
                'description': 'Volume over the last 5 minutes',
                'category': TickTypeCategory.VOLUME,
                'data_type': 'int',
                'units': 'shares/contracts',
                'frequency': 'medium'
            },
            65: {
                'name': 'SHORT_TERM_VOLUME_10_MIN',
                'description': 'Volume over the last 10 minutes',
                'category': TickTypeCategory.VOLUME,
                'data_type': 'int',
                'units': 'shares/contracts',
                'frequency': 'medium'
            }
        }
        
        # Organize tick types by category
        self.categories = {}
        for tick_id, tick_info in self.tick_types.items():
            category = tick_info['category']
            if category not in self.categories:
                self.categories[category] = {}
            self.categories[category][tick_id] = tick_info
    
    def get_tick_info(self, tick_type: int) -> Optional[Dict]:
        """
        Get detailed information about a specific tick type.
        
        Args:
            tick_type (int): Tick type ID
            
        Returns:
            dict: Tick type information or None if not found
        """
        return self.tick_types.get(tick_type)
    
    def get_tick_name(self, tick_type: int) -> str:
        """
        Get the name of a tick type.
        
        Args:
            tick_type (int): Tick type ID
            
        Returns:
            str: Tick type name or "UNKNOWN_X" if not found
        """
        tick_info = self.get_tick_info(tick_type)
        return tick_info['name'] if tick_info else f"UNKNOWN_{tick_type}"
    
    def get_tick_description(self, tick_type: int) -> str:
        """
        Get the description of a tick type.
        
        Args:
            tick_type (int): Tick type ID
            
        Returns:
            str: Tick type description
        """
        tick_info = self.get_tick_info(tick_type)
        return tick_info['description'] if tick_info else f"Unknown tick type {tick_type}"
    
    def get_ticks_by_category(self, category: TickTypeCategory) -> Dict[int, Dict]:
        """
        Get all tick types in a specific category.
        
        Args:
            category (TickTypeCategory): Category to filter by
            
        Returns:
            dict: Tick types in the category
        """
        return self.categories.get(category, {})
    
    def get_price_ticks(self) -> Dict[int, Dict]:
        """Get all price-related tick types."""
        return self.get_ticks_by_category(TickTypeCategory.PRICE)
    
    def get_size_ticks(self) -> Dict[int, Dict]:
        """Get all size-related tick types."""
        return self.get_ticks_by_category(TickTypeCategory.SIZE)
    
    def get_delayed_ticks(self) -> Dict[int, Dict]:
        """Get all delayed data tick types."""
        return self.get_ticks_by_category(TickTypeCategory.DELAYED)
    
    def get_option_ticks(self) -> Dict[int, Dict]:
        """Get all option-specific tick types."""
        return self.get_ticks_by_category(TickTypeCategory.OPTION)
    
    def get_volume_ticks(self) -> Dict[int, Dict]:
        """Get all volume-related tick types."""
        return self.get_ticks_by_category(TickTypeCategory.VOLUME)
    
    def is_price_tick(self, tick_type: int) -> bool:
        """Check if a tick type is price-related."""
        tick_info = self.get_tick_info(tick_type)
        return tick_info and tick_info['category'] == TickTypeCategory.PRICE
    
    def is_size_tick(self, tick_type: int) -> bool:
        """Check if a tick type is size-related."""
        tick_info = self.get_tick_info(tick_type)
        return tick_info and tick_info['category'] == TickTypeCategory.SIZE
    
    def is_delayed_tick(self, tick_type: int) -> bool:
        """Check if a tick type is delayed data."""
        tick_info = self.get_tick_info(tick_type)
        return tick_info and tick_info['category'] == TickTypeCategory.DELAYED
    
    def get_high_frequency_ticks(self) -> List[int]:
        """Get tick types that update frequently."""
        return [
            tick_id for tick_id, tick_info in self.tick_types.items()
            if tick_info['frequency'] == 'high'
        ]
    
    def get_basic_quote_ticks(self) -> List[int]:
        """Get the basic tick types needed for a standard quote."""
        return [1, 2, 3, 4, 5, 8]  # BID, ASK, ASK_SIZE, LAST, LAST_SIZE, VOLUME
    
    def format_tick_value(self, tick_type: int, value) -> str:
        """
        Format a tick value according to its type and units.
        
        Args:
            tick_type (int): Tick type ID
            value: Raw tick value
            
        Returns:
            str: Formatted tick value
        """
        tick_info = self.get_tick_info(tick_type)
        if not tick_info:
            return str(value)
        
        data_type = tick_info['data_type']
        units = tick_info['units']
        
        if data_type == 'float' and units == 'currency':
            return f"${value:.2f}"
        elif data_type == 'float' and units == 'percentage':
            return f"{value:.2f}%"
        elif data_type == 'int' and 'volume' in units.lower():
            return f"{value:,}"
        elif units == 'unix_timestamp':
            import time
            return time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(value))
        else:
            return str(value)
    
    def print_tick_reference(self):
        """Print a comprehensive reference of all tick types."""
        print("=== TWS API Tick Types Reference ===\n")
        
        for category in TickTypeCategory:
            ticks = self.get_ticks_by_category(category)
            if ticks:
                print(f"--- {category.value.upper()} TICKS ---")
                for tick_id, tick_info in sorted(ticks.items()):
                    print(f"{tick_id:2d}: {tick_info['name']}")
                    print(f"    {tick_info['description']}")
                    print(f"    Type: {tick_info['data_type']}, "
                          f"Units: {tick_info['units']}, "
                          f"Frequency: {tick_info['frequency']}")
                print()


def demonstrate_tick_types():
    """
    Demonstrate tick type functionality and reference information.
    """
    print("=== Tick Types Demo ===")
    
    manager = TickTypeManager()
    
    # Show basic quote tick types
    print("Basic Quote Tick Types:")
    basic_ticks = manager.get_basic_quote_ticks()
    for tick_id in basic_ticks:
        tick_info = manager.get_tick_info(tick_id)
        print(f"  {tick_id}: {tick_info['name']} - {tick_info['description']}")
    
    print("\n" + "="*50)
    
    # Show tick types by category
    print("\nTick Types by Category:")
    for category in TickTypeCategory:
        ticks = manager.get_ticks_by_category(category)
        print(f"\n{category.value.upper()} ({len(ticks)} ticks):")
        for tick_id in sorted(ticks.keys())[:5]:  # Show first 5
            name = manager.get_tick_name(tick_id)
            print(f"  {tick_id}: {name}")
        if len(ticks) > 5:
            print(f"  ... and {len(ticks) - 5} more")
    
    print("\n" + "="*50)
    
    # Demonstrate tick value formatting
    print("\nTick Value Formatting Examples:")
    examples = [
        (1, 150.25),      # BID price
        (3, 1000),        # ASK_SIZE
        (8, 1234567),     # VOLUME
        (24, 0.25),       # OPTION_IMPLIED_VOL
        (45, 1640995200)  # LAST_TIMESTAMP
    ]
    
    for tick_type, value in examples:
        name = manager.get_tick_name(tick_type)
        formatted = manager.format_tick_value(tick_type, value)
        print(f"  {name}: {value} -> {formatted}")
    
    print("\n" + "="*50)
    
    # Show high frequency ticks
    high_freq_ticks = manager.get_high_frequency_ticks()
    print(f"\nHigh Frequency Ticks ({len(high_freq_ticks)} total):")
    for tick_id in high_freq_ticks[:10]:  # Show first 10
        name = manager.get_tick_name(tick_id)
        print(f"  {tick_id}: {name}")
    
    print("\n" + "="*50)
    print("\nFor complete tick type reference, call manager.print_tick_reference()")


if __name__ == "__main__":
    """
    Main execution block for tick types demonstrations.
    """
    print("TWS API Tick Types Reference")
    print("=" * 35)
    
    # Run the demonstration
    demonstrate_tick_types()
    
    # Optionally print full reference
    print("\n" + "="*60)
    response = input("Print complete tick type reference? (y/n): ")
    if response.lower() == 'y':
        manager = TickTypeManager()
        manager.print_tick_reference()
