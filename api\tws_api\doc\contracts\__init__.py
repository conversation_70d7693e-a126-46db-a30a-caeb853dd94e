"""
TWS API Contracts (Financial Instruments) Documentation

This module provides comprehensive documentation and examples for working
with financial instrument contracts in the Interactive Brokers TWS API.

The contracts module covers:
- contract_creation: Creating and defining contract objects
- contract_details: Requesting and receiving detailed contract information
- contract_examples: Examples of different contract types (stocks, options, futures, etc.)
- contract_validation: Validating and resolving contract ambiguities
- contract_search: Searching for contracts and handling multiple matches

Key Concepts:
- Contract Object: Core structure used throughout the TWS API to define financial instruments
- Contract Details: Complete information about contracts including trading hours, specifications
- Security Types: Different types of financial instruments (STK, OPT, FUT, CASH, etc.)
- Contract Identification: Using conId, symbol, exchange, and other fields to uniquely identify contracts
- Contract Ambiguity: Handling cases where multiple contracts match the same description

Usage:
    from doc.contracts import contract_creation
    from doc.contracts import contract_details
    from doc.contracts import contract_examples
    # ... import other modules as needed

Important Notes:
- Contract objects are used for market data, orders, executions, and other API functions
- Minimum viable contract requires conId + exchange OR symbol + secType + exchange + currency
- Derivatives require additional fields like expiration, strike, right, etc.
- Contract details provide comprehensive information including trading schedules
"""

# Import all contract modules for easy access
from . import contract_creation
from . import contract_details
from . import contract_examples
from . import contract_validation
from . import contract_search

__all__ = [
    'contract_creation',
    'contract_details',
    'contract_examples',
    'contract_validation',
    'contract_search'
]
