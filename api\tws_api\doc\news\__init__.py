"""
TWS API News and Market Information Documentation

This module provides comprehensive documentation and examples for retrieving
news and market information from the Interactive Brokers TWS API. News data
includes real-time news feeds, historical news, and market-related information.

The news module covers:
- news_feeds: Real-time news feed subscriptions and processing
- news_articles: Retrieving and processing individual news articles
- news_providers: Managing news provider subscriptions and preferences
- historical_news: Accessing historical news data and archives
- news_filtering: Filtering news by symbols, categories, and criteria
- news_bulletins: System bulletins and important announcements

Key Concepts:
- News Providers: Different sources of news data (Reuters, Dow Jones, etc.)
- News Articles: Individual news items with headlines, content, and metadata
- News Tickers: Real-time news updates for specific symbols
- News Bulletins: System-wide announcements and important messages
- News Categories: Classification of news by type and relevance
- News Filtering: Customizing news feeds based on criteria

Usage:
    from api.tws_api.doc.news import news_feeds
    from api.tws_api.doc.news import news_articles
    from api.tws_api.doc.news import news_providers
    # ... import other modules as needed

Important Notes:
- News data requires appropriate market data subscriptions
- Different news providers may have different licensing requirements
- News content may be delayed based on subscription level
- Historical news access may be limited by provider policies
- News filtering helps manage information flow and relevance
"""

# Import all news modules for easy access
from . import news_feeds
from . import news_articles
from . import news_providers
from . import historical_news
from . import news_filtering
from . import news_bulletins

__all__ = [
    'news_feeds',
    'news_articles',
    'news_providers',
    'historical_news',
    'news_filtering',
    'news_bulletins'
]
