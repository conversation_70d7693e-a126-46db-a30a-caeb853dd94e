"""
TWS API Error Handling and Troubleshooting Documentation

This module provides comprehensive documentation and examples for handling
errors and troubleshooting issues in the Interactive Brokers TWS API.
Proper error handling is crucial for building robust trading applications.

The error_handling module covers:
- error_codes: Understanding TWS error codes and their meanings
- error_recovery: Implementing error recovery strategies
- connection_errors: Handling connection-related issues
- order_errors: Managing order placement and execution errors
- data_errors: Dealing with market data and subscription errors
- logging_debugging: Comprehensive logging and debugging techniques

Key Concepts:
- Error Codes: Numeric codes that identify specific error conditions
- Error Messages: Descriptive text explaining the error
- Error Categories: Classification of errors by type and severity
- Recovery Strategies: Automated and manual error recovery approaches
- Logging: Comprehensive error tracking and debugging information
- Validation: Preventing errors through input validation

Usage:
    from api.tws_api.doc.error_handling import error_codes
    from api.tws_api.doc.error_handling import error_recovery
    from api.tws_api.doc.error_handling import connection_errors
    # ... import other modules as needed

Important Notes:
- Not all errors are fatal - many can be recovered from automatically
- Error codes provide specific information about the nature of problems
- Proper logging is essential for debugging and monitoring
- Some errors indicate configuration or permission issues
- Error handling strategies should be tailored to application requirements
"""

# Import all error handling modules for easy access
from . import error_codes
from . import error_recovery
from . import connection_errors
from . import order_errors
from . import data_errors
from . import logging_debugging

__all__ = [
    'error_codes',
    'error_recovery',
    'connection_errors',
    'order_errors',
    'data_errors',
    'logging_debugging'
]
