"""
Delayed Market Data Handling

This module demonstrates how to handle delayed market data from the TWS API.
Delayed data is available for users without real-time market data subscriptions
and provides market information with a 15-20 minute delay.

Key Topics Covered:
- Understanding delayed vs real-time data
- Handling delayed market data subscriptions
- Processing delayed tick updates
- Managing data quality indicators
- Switching between delayed and real-time data
- Best practices for delayed data applications

Delayed market data is useful for applications that don't require real-time
information and helps reduce subscription costs while still providing
valuable market insights.
"""

import threading
import time
import logging
from typing import Dict, List, Optional, Callable
from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract


class DelayedDataApp(EClient, EWrapper):
    """
    TWS API application for delayed market data handling.
    
    This class demonstrates how to request, receive, and manage delayed
    market data with proper data quality tracking and processing.
    
    Attributes:
        delayed_data (dict): Storage for delayed market data by request ID
        data_quality (dict): Data quality tracking for subscriptions
        active_subscriptions (dict): Active delayed data subscriptions
        delayed_callbacks (dict): Callbacks for delayed data events
    """
    
    def __init__(self):
        """Initialize the delayed data application."""
        EClient.__init__(self, self)
        
        # Data storage
        self.delayed_data: Dict[int, Dict] = {}
        self.data_quality: Dict[int, str] = {}
        self.active_subscriptions: Dict[int, Contract] = {}
        
        # Callback management
        self.delayed_callbacks = {
            'delayed_tick': [],
            'data_quality_change': [],
            'delayed_data_available': []
        }
        
        # Data type mappings
        self.data_type_names = {
            1: 'Real-time',
            2: 'Frozen',
            3: 'Delayed',
            4: 'Delayed-Frozen'
        }
        
        # Delayed tick type mappings
        self.delayed_tick_types = {
            66: 'DELAYED_BID',
            67: 'DELAYED_ASK', 
            68: 'DELAYED_LAST',
            69: 'DELAYED_BID_SIZE',
            70: 'DELAYED_ASK_SIZE',
            71: 'DELAYED_LAST_SIZE',
            72: 'DELAYED_HIGH',
            73: 'DELAYED_LOW',
            74: 'DELAYED_VOLUME',
            75: 'DELAYED_CLOSE',
            76: 'DELAYED_OPEN',
            88: 'DELAYED_LAST_TIMESTAMP'
        }
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def connectAck(self):
        """Handle successful connection."""
        self.logger.info("Connected to TWS - Delayed Data service ready")
    
    def nextValidId(self, orderId):
        """
        Receive next valid order ID.
        
        Args:
            orderId (int): Next valid order ID
        """
        self.logger.info(f"Connection ready. Next valid order ID: {orderId}")
    
    def tickPrice(self, reqId: int, tickType: int, price: float, attrib):
        """
        Receive price tick updates (including delayed ticks).
        
        Args:
            reqId (int): Request ID for the market data subscription
            tickType (int): Type of price tick
            price (float): Price value
            attrib: Tick attributes
        """
        # Initialize data structure if needed
        if reqId not in self.delayed_data:
            self.delayed_data[reqId] = {}
        
        tick_name = self.delayed_tick_types.get(tickType, f"TICK_{tickType}")
        is_delayed_tick = tickType in self.delayed_tick_types
        
        # Store the price tick
        self.delayed_data[reqId][tick_name] = {
            'value': price,
            'timestamp': time.time(),
            'type': 'price',
            'is_delayed': is_delayed_tick,
            'tick_type': tickType
        }
        
        if is_delayed_tick:
            self.logger.debug(f"Delayed Price Tick - ReqId: {reqId}, {tick_name}: {price}")
        
        # Trigger callbacks for delayed ticks
        if is_delayed_tick:
            for callback in self.delayed_callbacks['delayed_tick']:
                try:
                    callback(reqId, tickType, price, 'price')
                except Exception as e:
                    self.logger.error(f"Delayed tick callback error: {e}")
    
    def tickSize(self, reqId: int, tickType: int, size: int):
        """
        Receive size tick updates (including delayed ticks).
        
        Args:
            reqId (int): Request ID for the market data subscription
            tickType (int): Type of size tick
            size (int): Size value
        """
        # Initialize data structure if needed
        if reqId not in self.delayed_data:
            self.delayed_data[reqId] = {}
        
        tick_name = self.delayed_tick_types.get(tickType, f"TICK_{tickType}")
        is_delayed_tick = tickType in self.delayed_tick_types
        
        # Store the size tick
        self.delayed_data[reqId][tick_name] = {
            'value': size,
            'timestamp': time.time(),
            'type': 'size',
            'is_delayed': is_delayed_tick,
            'tick_type': tickType
        }
        
        if is_delayed_tick:
            self.logger.debug(f"Delayed Size Tick - ReqId: {reqId}, {tick_name}: {size}")
        
        # Trigger callbacks for delayed ticks
        if is_delayed_tick:
            for callback in self.delayed_callbacks['delayed_tick']:
                try:
                    callback(reqId, tickType, size, 'size')
                except Exception as e:
                    self.logger.error(f"Delayed tick callback error: {e}")
    
    def marketDataType(self, reqId: int, marketDataType: int):
        """
        Receive market data type updates.
        
        Args:
            reqId (int): Request ID
            marketDataType (int): Market data type
        """
        data_type_name = self.data_type_names.get(marketDataType, f"Unknown_{marketDataType}")
        self.data_quality[reqId] = data_type_name
        
        self.logger.info(f"Data Quality - ReqId: {reqId}, Type: {data_type_name}")
        
        # Check if delayed data is now available
        if marketDataType in [3, 4]:  # Delayed or Delayed-Frozen
            for callback in self.delayed_callbacks['delayed_data_available']:
                try:
                    callback(reqId, data_type_name)
                except Exception as e:
                    self.logger.error(f"Delayed data available callback error: {e}")
        
        # Trigger data quality change callbacks
        for callback in self.delayed_callbacks['data_quality_change']:
            try:
                callback(reqId, marketDataType, data_type_name)
            except Exception as e:
                self.logger.error(f"Data quality change callback error: {e}")
    
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors related to delayed data requests.
        
        Args:
            reqId (int): Request ID
            errorCode (int): Error code
            errorString (str): Error description
            advancedOrderRejectJson (str): Additional error details
        """
        self.logger.error(f"Error {reqId} {errorCode} {errorString}")
        
        # Handle delayed data specific errors
        if reqId in self.active_subscriptions:
            if errorCode == 354:  # No market data permissions
                self.logger.info(f"No real-time permissions for {reqId}, may receive delayed data")
            elif errorCode == 200:  # No security definition
                self.logger.error(f"Delayed data subscription {reqId} failed: {errorString}")
    
    def request_delayed_data(self, req_id: int, contract: Contract, 
                           snapshot: bool = False) -> bool:
        """
        Request delayed market data for a contract.
        
        Args:
            req_id (int): Unique request identifier
            contract (Contract): Contract to get delayed data for
            snapshot (bool): True for snapshot, False for streaming
            
        Returns:
            bool: True if request was sent successfully
        """
        if req_id in self.active_subscriptions:
            self.logger.error(f"Request ID {req_id} already has an active subscription")
            return False
        
        try:
            # Request market data - TWS will automatically provide delayed data
            # if real-time permissions are not available
            self.reqMktData(req_id, contract, "", snapshot, False, [])
            
            self.active_subscriptions[req_id] = contract
            
            self.logger.info(f"Requested market data (delayed if no permissions) - "
                           f"ReqId: {req_id}, Symbol: {contract.symbol}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to request delayed data: {e}")
            return False
    
    def switch_to_delayed_data(self, req_id: int) -> bool:
        """
        Switch to delayed data for an existing subscription.
        
        Args:
            req_id (int): Request ID to switch to delayed data
            
        Returns:
            bool: True if switch request was sent successfully
        """
        if req_id not in self.active_subscriptions:
            self.logger.error(f"No active subscription found for request ID {req_id}")
            return False
        
        try:
            # Request delayed market data type
            self.reqMarketDataType(3)  # 3 = Delayed
            
            self.logger.info(f"Switched to delayed data mode for request {req_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to switch to delayed data: {e}")
            return False
    
    def switch_to_realtime_data(self, req_id: int) -> bool:
        """
        Switch to real-time data for an existing subscription.
        
        Args:
            req_id (int): Request ID to switch to real-time data
            
        Returns:
            bool: True if switch request was sent successfully
        """
        if req_id not in self.active_subscriptions:
            self.logger.error(f"No active subscription found for request ID {req_id}")
            return False
        
        try:
            # Request real-time market data type
            self.reqMarketDataType(1)  # 1 = Real-time
            
            self.logger.info(f"Switched to real-time data mode for request {req_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to switch to real-time data: {e}")
            return False
    
    def cancel_delayed_data(self, req_id: int) -> bool:
        """
        Cancel a delayed data subscription.
        
        Args:
            req_id (int): Request ID to cancel
            
        Returns:
            bool: True if cancellation was sent successfully
        """
        if req_id not in self.active_subscriptions:
            self.logger.warning(f"No active subscription found for request ID {req_id}")
            return False
        
        try:
            self.cancelMktData(req_id)
            del self.active_subscriptions[req_id]
            
            self.logger.info(f"Canceled delayed data subscription - ReqId: {req_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cancel delayed data: {e}")
            return False
    
    def get_delayed_data(self, req_id: int) -> Dict:
        """
        Get delayed market data for a specific request.
        
        Args:
            req_id (int): Request identifier
            
        Returns:
            dict: Delayed market data
        """
        return self.delayed_data.get(req_id, {})
    
    def get_delayed_quote(self, req_id: int) -> Dict:
        """
        Get current delayed quote for a subscription.
        
        Args:
            req_id (int): Request ID
            
        Returns:
            dict: Current delayed quote information
        """
        data = self.get_delayed_data(req_id)
        
        quote = {
            'delayed_bid': data.get('DELAYED_BID', {}).get('value'),
            'delayed_ask': data.get('DELAYED_ASK', {}).get('value'),
            'delayed_last': data.get('DELAYED_LAST', {}).get('value'),
            'delayed_bid_size': data.get('DELAYED_BID_SIZE', {}).get('value'),
            'delayed_ask_size': data.get('DELAYED_ASK_SIZE', {}).get('value'),
            'delayed_last_size': data.get('DELAYED_LAST_SIZE', {}).get('value'),
            'delayed_high': data.get('DELAYED_HIGH', {}).get('value'),
            'delayed_low': data.get('DELAYED_LOW', {}).get('value'),
            'delayed_volume': data.get('DELAYED_VOLUME', {}).get('value'),
            'delayed_open': data.get('DELAYED_OPEN', {}).get('value'),
            'delayed_close': data.get('DELAYED_CLOSE', {}).get('value'),
            'data_quality': self.data_quality.get(req_id, 'Unknown')
        }
        
        return quote
    
    def is_delayed_data(self, req_id: int) -> bool:
        """
        Check if the data for a request is delayed.
        
        Args:
            req_id (int): Request ID
            
        Returns:
            bool: True if data is delayed
        """
        data_quality = self.data_quality.get(req_id, '')
        return 'Delayed' in data_quality
    
    def get_data_delay_info(self, req_id: int) -> Dict:
        """
        Get information about data delay for a subscription.
        
        Args:
            req_id (int): Request ID
            
        Returns:
            dict: Data delay information
        """
        data_quality = self.data_quality.get(req_id, 'Unknown')
        contract = self.active_subscriptions.get(req_id)
        
        info = {
            'request_id': req_id,
            'symbol': contract.symbol if contract else 'Unknown',
            'data_quality': data_quality,
            'is_delayed': self.is_delayed_data(req_id),
            'estimated_delay_minutes': 15 if self.is_delayed_data(req_id) else 0,
            'has_delayed_ticks': any(
                tick_data.get('is_delayed', False) 
                for tick_data in self.delayed_data.get(req_id, {}).values()
            )
        }
        
        return info
    
    def add_delayed_callback(self, event_type: str, callback: Callable):
        """
        Add a callback for delayed data events.
        
        Args:
            event_type (str): Type of event
            callback: Callback function to add
        """
        if event_type in self.delayed_callbacks:
            self.delayed_callbacks[event_type].append(callback)


def demonstrate_delayed_data():
    """
    Demonstrate delayed data functionality with practical examples.
    """
    print("=== Delayed Market Data Demo ===")
    
    app = DelayedDataApp()
    
    # Add callbacks for demonstration
    def on_delayed_tick(req_id, tick_type, value, value_type):
        tick_name = app.delayed_tick_types.get(tick_type, f"TICK_{tick_type}")
        print(f"Delayed {value_type.title()}: {tick_name} = {value}")
    
    def on_data_quality_change(req_id, data_type, data_type_name):
        contract = app.active_subscriptions.get(req_id)
        symbol = contract.symbol if contract else f"ReqId_{req_id}"
        print(f"Data Quality Change - {symbol}: {data_type_name}")
    
    def on_delayed_data_available(req_id, data_type_name):
        contract = app.active_subscriptions.get(req_id)
        symbol = contract.symbol if contract else f"ReqId_{req_id}"
        print(f"Delayed data now available for {symbol}: {data_type_name}")
    
    app.add_delayed_callback('delayed_tick', on_delayed_tick)
    app.add_delayed_callback('data_quality_change', on_data_quality_change)
    app.add_delayed_callback('delayed_data_available', on_delayed_data_available)
    
    try:
        # Connect to TWS
        app.connect("127.0.0.1", 7497, 0)
        
        # Start processing thread
        api_thread = threading.Thread(target=app.run, daemon=True)
        api_thread.start()
        
        # Wait for connection
        time.sleep(3)
        
        if app.isConnected():
            print("Connected - requesting delayed market data")
            
            # Switch to delayed data mode
            app.reqMarketDataType(3)  # Request delayed data
            time.sleep(1)
            
            # Create sample contracts
            from api.tws_api.doc.contracts.contract_creation import ContractBuilder
            builder = ContractBuilder()
            
            # Request delayed data for popular stocks
            aapl_contract = builder.create_stock_contract("AAPL")
            if app.request_delayed_data(9001, aapl_contract):
                print("AAPL delayed data requested")
            
            msft_contract = builder.create_stock_contract("MSFT")
            if app.request_delayed_data(9002, msft_contract):
                print("MSFT delayed data requested")
            
            # Monitor for delayed data updates
            print("Monitoring delayed data for 20 seconds...")
            time.sleep(20)
            
            # Display delayed quotes
            print("\n--- Delayed Quotes ---")
            for req_id in [9001, 9002]:
                quote = app.get_delayed_quote(req_id)
                delay_info = app.get_data_delay_info(req_id)
                
                print(f"\n{delay_info['symbol']} ({delay_info['data_quality']}):")
                print(f"  Delayed Bid: {quote['delayed_bid']} x {quote['delayed_bid_size']}")
                print(f"  Delayed Ask: {quote['delayed_ask']} x {quote['delayed_ask_size']}")
                print(f"  Delayed Last: {quote['delayed_last']} x {quote['delayed_last_size']}")
                print(f"  Delayed High: {quote['delayed_high']}")
                print(f"  Delayed Low: {quote['delayed_low']}")
                print(f"  Delayed Volume: {quote['delayed_volume']}")
                print(f"  Estimated Delay: {delay_info['estimated_delay_minutes']} minutes")
            
            # Demonstrate switching data types
            print("\n--- Testing Data Type Switching ---")
            print("Attempting to switch to real-time data...")
            app.switch_to_realtime_data(9001)
            time.sleep(5)
            
            print("Switching back to delayed data...")
            app.switch_to_delayed_data(9001)
            time.sleep(5)
            
            # Cancel subscriptions
            for req_id in list(app.active_subscriptions.keys()):
                app.cancel_delayed_data(req_id)
            print("Delayed data subscriptions canceled")
        
        else:
            print("Failed to connect to TWS")
    
    except KeyboardInterrupt:
        print("\nShutting down...")
    
    finally:
        # Cancel all active subscriptions
        for req_id in list(app.active_subscriptions.keys()):
            app.cancel_delayed_data(req_id)
        
        app.disconnect()


if __name__ == "__main__":
    """
    Main execution block for delayed data demonstrations.
    """
    print("TWS API Delayed Market Data Examples")
    print("=" * 45)
    
    # Run the demonstration
    demonstrate_delayed_data()
