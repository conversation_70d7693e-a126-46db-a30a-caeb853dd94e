"""
TWS API Financial Advisors (FA) Documentation

This module provides comprehensive documentation and examples for Financial
Advisor functionality in the Interactive Brokers TWS API. FA features enable
portfolio managers to manage multiple client accounts efficiently.

The financial_advisors module covers:
- fa_accounts: Managing FA account structures and hierarchies
- allocation_methods: Different allocation methods and strategies
- fa_groups: Creating and managing account groups
- fa_profiles: Setting up allocation profiles
- fa_orders: Placing orders with FA allocation
- fa_reporting: FA-specific reporting and account data

Key Concepts:
- Financial Advisor (FA): A master account that manages multiple sub-accounts
- Account Groups: Collections of accounts for allocation purposes
- Allocation Profiles: Predefined allocation strategies
- Allocation Methods: How orders are distributed across accounts
- Sub-accounts: Individual client accounts under FA management
- Model Portfolios: Template portfolios for account management

Usage:
    from api.tws_api.doc.financial_advisors import fa_accounts
    from api.tws_api.doc.financial_advisors import allocation_methods
    from api.tws_api.doc.financial_advisors import fa_groups
    # ... import other modules as needed

Important Notes:
- FA functionality requires appropriate account permissions
- FA features are only available to qualified Financial Advisors
- Allocation methods affect how orders are distributed
- FA reporting provides consolidated and individual account views
- Model accounts enable template-based portfolio management
"""

# Import all financial advisor modules for easy access
from . import fa_accounts
from . import allocation_methods
from . import fa_groups
from . import fa_profiles
from . import fa_orders
from . import fa_reporting

__all__ = [
    'fa_accounts',
    'allocation_methods',
    'fa_groups',
    'fa_profiles',
    'fa_orders',
    'fa_reporting'
]
