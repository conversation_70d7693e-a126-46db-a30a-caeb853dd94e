"""
Financial Advisor Account Management

This module demonstrates how to manage Financial Advisor (FA) accounts
and their configurations using the TWS API. FA accounts enable portfolio
managers to efficiently manage multiple client accounts.

Key Topics Covered:
- Requesting FA account configurations
- Understanding FA account structures and hierarchies
- Managing account groups and allocation profiles
- Working with FA account data and settings
- Updating FA configurations programmatically
- Best practices for FA account management

Financial Advisor functionality is essential for investment managers
who need to manage multiple client accounts with sophisticated
allocation and reporting capabilities.
"""

import threading
import time
import logging
import xml.etree.ElementTree as ET
from typing import Dict, List, Optional, Callable
from ibapi.client import EClient
from ibapi.wrapper import EWrapper


class FAAccountsApp(EClient, EWrapper):
    """
    TWS API application for Financial Advisor account management.
    
    This class demonstrates how to request, receive, and manage FA
    account configurations with comprehensive FA functionality.
    
    Attributes:
        fa_data (dict): Storage for FA configuration data
        fa_callbacks (dict): Callbacks for FA events
        fa_data_types (dict): FA data type mappings
    """
    
    def __init__(self):
        """Initialize the FA accounts application."""
        EClient.__init__(self, self)
        
        # Data storage
        self.fa_data: Dict[str, str] = {}
        
        # FA data types
        self.fa_data_types = {
            1: 'GROUPS',
            2: 'PROFILES', 
            3: 'ALIASES'
        }
        
        # Callback management
        self.fa_callbacks = {
            'receive_fa': [],
            'managed_accounts': []
        }
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def connectAck(self):
        """Handle successful connection."""
        self.logger.info("Connected to TWS - FA Accounts service ready")
    
    def nextValidId(self, orderId):
        """
        Receive next valid order ID.
        
        Args:
            orderId (int): Next valid order ID
        """
        self.logger.info(f"Connection ready. Next valid order ID: {orderId}")
    
    def receiveFA(self, faData: int, cxml: str):
        """
        Receive Financial Advisor configuration data.
        
        Args:
            faData (int): FA data type (1=Groups, 2=Profiles, 3=Aliases)
            cxml (str): XML configuration data
        """
        data_type_name = self.fa_data_types.get(faData, f"Unknown_{faData}")
        
        # Store the FA data
        self.fa_data[data_type_name] = cxml
        
        self.logger.info(f"Received FA Data - Type: {data_type_name}, "
                        f"Size: {len(cxml)} characters")
        
        # Parse and log basic information
        try:
            self._parse_fa_data(data_type_name, cxml)
        except Exception as e:
            self.logger.error(f"Error parsing FA data: {e}")
        
        # Trigger callbacks
        for callback in self.fa_callbacks['receive_fa']:
            try:
                callback(faData, cxml)
            except Exception as e:
                self.logger.error(f"FA data callback error: {e}")
    
    def managedAccounts(self, accountsList: str):
        """
        Receive managed accounts list.
        
        Args:
            accountsList (str): Comma-separated list of managed accounts
        """
        accounts = [acc.strip() for acc in accountsList.split(',') if acc.strip()]
        
        self.logger.info(f"Managed Accounts: {len(accounts)} accounts")
        for i, account in enumerate(accounts, 1):
            self.logger.info(f"  {i}: {account}")
        
        # Trigger callbacks
        for callback in self.fa_callbacks['managed_accounts']:
            try:
                callback(accountsList)
            except Exception as e:
                self.logger.error(f"Managed accounts callback error: {e}")
    
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors related to FA requests.
        
        Args:
            reqId (int): Request ID
            errorCode (int): Error code
            errorString (str): Error description
            advancedOrderRejectJson (str): Additional error details
        """
        self.logger.error(f"Error {reqId} {errorCode} {errorString}")
    
    def _parse_fa_data(self, data_type: str, xml_data: str):
        """
        Parse FA XML data and log summary information.
        
        Args:
            data_type (str): Type of FA data
            xml_data (str): XML data to parse
        """
        try:
            root = ET.fromstring(xml_data)
            
            if data_type == 'GROUPS':
                groups = root.findall('.//Group')
                self.logger.info(f"  Found {len(groups)} account groups")
                for group in groups[:3]:  # Show first 3
                    name = group.get('name', 'Unknown')
                    accounts = group.findall('.//Account')
                    self.logger.info(f"    Group '{name}': {len(accounts)} accounts")
            
            elif data_type == 'PROFILES':
                profiles = root.findall('.//AllocationProfile')
                self.logger.info(f"  Found {len(profiles)} allocation profiles")
                for profile in profiles[:3]:  # Show first 3
                    name = profile.get('name', 'Unknown')
                    allocations = profile.findall('.//Allocation')
                    self.logger.info(f"    Profile '{name}': {len(allocations)} allocations")
            
            elif data_type == 'ALIASES':
                aliases = root.findall('.//Alias')
                self.logger.info(f"  Found {len(aliases)} account aliases")
                for alias in aliases[:3]:  # Show first 3
                    account = alias.get('account', 'Unknown')
                    alias_name = alias.get('alias', 'Unknown')
                    self.logger.info(f"    {account} -> {alias_name}")
        
        except ET.ParseError as e:
            self.logger.error(f"XML parsing error: {e}")
        except Exception as e:
            self.logger.error(f"Error parsing FA data: {e}")
    
    def request_fa_data(self, fa_data_type: int) -> bool:
        """
        Request Financial Advisor configuration data.
        
        Args:
            fa_data_type (int): FA data type (1=Groups, 2=Profiles, 3=Aliases)
            
        Returns:
            bool: True if request was sent successfully
        """
        if fa_data_type not in self.fa_data_types:
            self.logger.error(f"Invalid FA data type: {fa_data_type}")
            return False
        
        try:
            self.requestFA(fa_data_type)
            
            data_type_name = self.fa_data_types[fa_data_type]
            self.logger.info(f"Requested FA data: {data_type_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to request FA data: {e}")
            return False
    
    def request_all_fa_data(self) -> bool:
        """
        Request all FA configuration data types.
        
        Returns:
            bool: True if all requests were sent successfully
        """
        success = True
        for fa_data_type in self.fa_data_types.keys():
            if not self.request_fa_data(fa_data_type):
                success = False
            time.sleep(0.5)  # Small delay between requests
        
        return success
    
    def replace_fa_data(self, fa_data_type: int, xml_data: str) -> bool:
        """
        Replace FA configuration data.
        
        Args:
            fa_data_type (int): FA data type to replace
            xml_data (str): New XML configuration data
            
        Returns:
            bool: True if replacement was sent successfully
        """
        if fa_data_type not in self.fa_data_types:
            self.logger.error(f"Invalid FA data type: {fa_data_type}")
            return False
        
        try:
            self.replaceFA(fa_data_type, xml_data)
            
            data_type_name = self.fa_data_types[fa_data_type]
            self.logger.info(f"Replaced FA data: {data_type_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to replace FA data: {e}")
            return False
    
    def get_fa_data(self, data_type: str) -> Optional[str]:
        """
        Get stored FA data by type.
        
        Args:
            data_type (str): FA data type name
            
        Returns:
            str: FA data XML or None if not found
        """
        return self.fa_data.get(data_type)
    
    def parse_fa_groups(self) -> List[Dict]:
        """
        Parse FA groups data into structured format.
        
        Returns:
            list: List of group information dictionaries
        """
        groups_xml = self.get_fa_data('GROUPS')
        if not groups_xml:
            return []
        
        groups = []
        try:
            root = ET.fromstring(groups_xml)
            for group_elem in root.findall('.//Group'):
                group_info = {
                    'name': group_elem.get('name', ''),
                    'default_method': group_elem.get('defaultMethod', ''),
                    'accounts': []
                }
                
                for account_elem in group_elem.findall('.//Account'):
                    account_info = {
                        'id': account_elem.get('id', ''),
                        'amount': account_elem.get('amount', ''),
                        'percentage': account_elem.get('percentage', '')
                    }
                    group_info['accounts'].append(account_info)
                
                groups.append(group_info)
        
        except ET.ParseError as e:
            self.logger.error(f"Error parsing FA groups: {e}")
        
        return groups
    
    def parse_fa_profiles(self) -> List[Dict]:
        """
        Parse FA allocation profiles into structured format.
        
        Returns:
            list: List of profile information dictionaries
        """
        profiles_xml = self.get_fa_data('PROFILES')
        if not profiles_xml:
            return []
        
        profiles = []
        try:
            root = ET.fromstring(profiles_xml)
            for profile_elem in root.findall('.//AllocationProfile'):
                profile_info = {
                    'name': profile_elem.get('name', ''),
                    'type': profile_elem.get('type', ''),
                    'allocations': []
                }
                
                for alloc_elem in profile_elem.findall('.//Allocation'):
                    allocation_info = {
                        'account': alloc_elem.get('account', ''),
                        'amount': alloc_elem.get('amount', ''),
                        'percentage': alloc_elem.get('percentage', '')
                    }
                    profile_info['allocations'].append(allocation_info)
                
                profiles.append(profile_info)
        
        except ET.ParseError as e:
            self.logger.error(f"Error parsing FA profiles: {e}")
        
        return profiles
    
    def get_fa_summary(self) -> Dict:
        """
        Get summary information about FA configuration.
        
        Returns:
            dict: FA configuration summary
        """
        summary = {
            'has_groups': 'GROUPS' in self.fa_data,
            'has_profiles': 'PROFILES' in self.fa_data,
            'has_aliases': 'ALIASES' in self.fa_data,
            'groups_count': 0,
            'profiles_count': 0,
            'aliases_count': 0
        }
        
        # Count groups
        groups = self.parse_fa_groups()
        summary['groups_count'] = len(groups)
        
        # Count profiles
        profiles = self.parse_fa_profiles()
        summary['profiles_count'] = len(profiles)
        
        # Count aliases
        aliases_xml = self.get_fa_data('ALIASES')
        if aliases_xml:
            try:
                root = ET.fromstring(aliases_xml)
                aliases = root.findall('.//Alias')
                summary['aliases_count'] = len(aliases)
            except ET.ParseError:
                pass
        
        return summary
    
    def add_fa_callback(self, event_type: str, callback: Callable):
        """
        Add a callback for FA events.
        
        Args:
            event_type (str): Type of event
            callback: Callback function to add
        """
        if event_type in self.fa_callbacks:
            self.fa_callbacks[event_type].append(callback)


def demonstrate_fa_accounts():
    """
    Demonstrate FA accounts functionality with practical examples.
    """
    print("=== Financial Advisor Accounts Demo ===")
    print("Note: This demo requires FA account permissions")
    
    app = FAAccountsApp()
    
    # Add callbacks for demonstration
    def on_fa_data(fa_data_type, xml_data):
        data_type_name = app.fa_data_types.get(fa_data_type, f"Type_{fa_data_type}")
        print(f"FA Data received: {data_type_name} ({len(xml_data)} chars)")
    
    def on_managed_accounts(accounts_list):
        accounts = [acc.strip() for acc in accounts_list.split(',') if acc.strip()]
        print(f"Managed accounts: {len(accounts)} accounts")
    
    app.add_fa_callback('receive_fa', on_fa_data)
    app.add_fa_callback('managed_accounts', on_managed_accounts)
    
    try:
        # Connect to TWS
        app.connect("127.0.0.1", 7497, 0)
        
        # Start processing thread
        api_thread = threading.Thread(target=app.run, daemon=True)
        api_thread.start()
        
        # Wait for connection
        time.sleep(3)
        
        if app.isConnected():
            print("Connected - requesting FA data")
            
            # Request all FA data
            if app.request_all_fa_data():
                print("FA data requests sent")
            
            # Wait for FA data
            time.sleep(5)
            
            # Display FA summary
            summary = app.get_fa_summary()
            print("\n--- FA Configuration Summary ---")
            print(f"Has Groups: {summary['has_groups']} ({summary['groups_count']} groups)")
            print(f"Has Profiles: {summary['has_profiles']} ({summary['profiles_count']} profiles)")
            print(f"Has Aliases: {summary['has_aliases']} ({summary['aliases_count']} aliases)")
            
            # Show group details
            groups = app.parse_fa_groups()
            if groups:
                print(f"\n--- Account Groups ({len(groups)}) ---")
                for group in groups[:3]:  # Show first 3
                    print(f"Group: {group['name']}")
                    print(f"  Default Method: {group['default_method']}")
                    print(f"  Accounts: {len(group['accounts'])}")
                    for account in group['accounts'][:2]:  # Show first 2 accounts
                        print(f"    {account['id']}: {account.get('percentage', account.get('amount', 'N/A'))}")
            
            # Show profile details
            profiles = app.parse_fa_profiles()
            if profiles:
                print(f"\n--- Allocation Profiles ({len(profiles)}) ---")
                for profile in profiles[:3]:  # Show first 3
                    print(f"Profile: {profile['name']}")
                    print(f"  Type: {profile['type']}")
                    print(f"  Allocations: {len(profile['allocations'])}")
                    for allocation in profile['allocations'][:2]:  # Show first 2
                        print(f"    {allocation['account']}: {allocation.get('percentage', allocation.get('amount', 'N/A'))}")
        
        else:
            print("Failed to connect to TWS")
            print("Note: FA functionality requires appropriate account permissions")
    
    except KeyboardInterrupt:
        print("\nShutting down...")
    
    finally:
        app.disconnect()


if __name__ == "__main__":
    """
    Main execution block for FA accounts demonstrations.
    """
    print("TWS API Financial Advisor Accounts Examples")
    print("=" * 50)
    
    # Run the demonstration
    demonstrate_fa_accounts()
