"""
Bulletin Subscriptions and System Messages

This module demonstrates how to subscribe to and receive bulletins and
system messages from the TWS API. Bulletins provide important system-wide
announcements, news, and operational updates.

Key Topics Covered:
- Subscribing to bulletin feeds
- Processing different types of bulletins
- Understanding bulletin message formats
- Handling system announcements and alerts
- Managing bulletin subscriptions and filtering
- Best practices for bulletin processing

Bulletins are essential for staying informed about system status,
market conditions, and important announcements that may affect
trading operations.
"""

import threading
import time
import logging
from typing import Dict, List, Optional, Callable, Set
from ibapi.client import EClient
from ibapi.wrapper import EWrapper


class BulletinSubscriptionsApp(EClient, EWrapper):
    """
    TWS API application for bulletin subscriptions and system messages.
    
    This class demonstrates how to subscribe to, receive, and process
    bulletins with comprehensive message handling capabilities.
    
    Attributes:
        bulletins (dict): Storage for received bulletins
        bulletin_callbacks (dict): Callbacks for bulletin events
        bulletin_types (dict): Bulletin type classifications
        active_subscriptions (set): Active bulletin subscriptions
    """
    
    def __init__(self):
        """Initialize the bulletin subscriptions application."""
        EClient.__init__(self, self)
        
        # Data storage
        self.bulletins: Dict[int, Dict] = {}  # msgId -> bulletin_data
        self.active_subscriptions: Set[str] = set()
        
        # Bulletin type classifications
        self.bulletin_types = {
            1: 'Regular News Bulletin',
            2: 'Exchange Unavailable',
            3: 'Exchange Available',
            4: 'Exchange Reset',
            5: 'New Firm Trading',
            6: 'Firm Not Trading',
            7: 'Baskets Not Trading',
            8: 'Baskets Trading',
            9: 'Multi-leg Not Trading',
            10: 'Multi-leg Trading',
            11: 'System Unavailable',
            12: 'System Available'
        }
        
        # Callback management
        self.bulletin_callbacks = {
            'update_news_bulletin': [],
            'system_message': [],
            'bulletin_received': []
        }
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def connectAck(self):
        """Handle successful connection."""
        self.logger.info("Connected to TWS - Bulletin Subscriptions service ready")
    
    def nextValidId(self, orderId):
        """
        Receive next valid order ID.
        
        Args:
            orderId (int): Next valid order ID
        """
        self.logger.info(f"Connection ready. Next valid order ID: {orderId}")
    
    def updateNewsBulletin(self, msgId: int, msgType: int, newsMessage: str, originExch: str):
        """
        Receive news bulletin updates.
        
        Args:
            msgId (int): Unique message identifier
            msgType (int): Type of bulletin message
            newsMessage (str): Bulletin message content
            originExch (str): Originating exchange
        """
        # Get bulletin type description
        type_description = self.bulletin_types.get(msgType, f"Unknown Type {msgType}")
        
        # Create bulletin entry
        bulletin = {
            'msg_id': msgId,
            'msg_type': msgType,
            'type_description': type_description,
            'message': newsMessage,
            'origin_exchange': originExch,
            'timestamp': time.time(),
            'time_formatted': time.strftime('%Y-%m-%d %H:%M:%S'),
            'is_system_message': self._is_system_message(msgType),
            'priority': self._get_bulletin_priority(msgType)
        }
        
        # Store the bulletin
        self.bulletins[msgId] = bulletin
        
        # Log the bulletin
        priority_indicator = "🚨" if bulletin['priority'] == 'HIGH' else "ℹ️"
        self.logger.info(f"{priority_indicator} Bulletin {msgId}: {type_description}")
        self.logger.info(f"  Exchange: {originExch}")
        self.logger.info(f"  Message: {newsMessage}")
        
        # Trigger callbacks
        for callback in self.bulletin_callbacks['update_news_bulletin']:
            try:
                callback(msgId, msgType, newsMessage, originExch)
            except Exception as e:
                self.logger.error(f"Bulletin callback error: {e}")
        
        # Trigger system message callbacks for system-related bulletins
        if bulletin['is_system_message']:
            for callback in self.bulletin_callbacks['system_message']:
                try:
                    callback(msgId, msgType, newsMessage, originExch)
                except Exception as e:
                    self.logger.error(f"System message callback error: {e}")
        
        # Trigger general bulletin callbacks
        for callback in self.bulletin_callbacks['bulletin_received']:
            try:
                callback(bulletin)
            except Exception as e:
                self.logger.error(f"Bulletin received callback error: {e}")
    
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors related to bulletin requests.
        
        Args:
            reqId (int): Request ID
            errorCode (int): Error code
            errorString (str): Error description
            advancedOrderRejectJson (str): Additional error details
        """
        self.logger.error(f"Error {reqId} {errorCode} {errorString}")
    
    def _is_system_message(self, msg_type: int) -> bool:
        """
        Determine if a bulletin type is a system message.
        
        Args:
            msg_type (int): Bulletin message type
            
        Returns:
            bool: True if it's a system message
        """
        system_types = {2, 3, 4, 11, 12}  # Exchange/System unavailable/available/reset
        return msg_type in system_types
    
    def _get_bulletin_priority(self, msg_type: int) -> str:
        """
        Get priority level for a bulletin type.
        
        Args:
            msg_type (int): Bulletin message type
            
        Returns:
            str: Priority level (HIGH, MEDIUM, LOW)
        """
        high_priority = {2, 4, 6, 11}  # Unavailable/Reset/Not Trading
        medium_priority = {3, 5, 12}   # Available/Trading/System Available
        
        if msg_type in high_priority:
            return 'HIGH'
        elif msg_type in medium_priority:
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def request_bulletin_subscriptions(self, all_messages: bool = True) -> bool:
        """
        Request bulletin subscriptions.
        
        Args:
            all_messages (bool): Subscribe to all bulletin types
            
        Returns:
            bool: True if subscription request was sent successfully
        """
        try:
            self.reqNewsBulletins(all_messages)
            
            subscription_type = "all bulletins" if all_messages else "filtered bulletins"
            self.active_subscriptions.add(subscription_type)
            
            self.logger.info(f"Requested bulletin subscription: {subscription_type}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to request bulletin subscriptions: {e}")
            return False
    
    def cancel_bulletin_subscriptions(self) -> bool:
        """
        Cancel bulletin subscriptions.
        
        Returns:
            bool: True if cancellation was sent successfully
        """
        try:
            self.cancelNewsBulletins()
            self.active_subscriptions.clear()
            
            self.logger.info("Canceled bulletin subscriptions")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cancel bulletin subscriptions: {e}")
            return False
    
    def get_bulletin(self, msg_id: int) -> Optional[Dict]:
        """
        Get a specific bulletin by message ID.
        
        Args:
            msg_id (int): Message ID
            
        Returns:
            dict: Bulletin data or None if not found
        """
        return self.bulletins.get(msg_id)
    
    def get_recent_bulletins(self, hours: int = 24) -> List[Dict]:
        """
        Get bulletins from the last N hours.
        
        Args:
            hours (int): Number of hours to look back
            
        Returns:
            list: List of recent bulletins
        """
        cutoff_time = time.time() - (hours * 3600)
        recent_bulletins = []
        
        for bulletin in self.bulletins.values():
            if bulletin['timestamp'] >= cutoff_time:
                recent_bulletins.append(bulletin)
        
        # Sort by timestamp (most recent first)
        recent_bulletins.sort(key=lambda x: x['timestamp'], reverse=True)
        return recent_bulletins
    
    def get_bulletins_by_type(self, msg_type: int) -> List[Dict]:
        """
        Get bulletins of a specific type.
        
        Args:
            msg_type (int): Bulletin message type
            
        Returns:
            list: List of bulletins of the specified type
        """
        type_bulletins = []
        
        for bulletin in self.bulletins.values():
            if bulletin['msg_type'] == msg_type:
                type_bulletins.append(bulletin)
        
        # Sort by timestamp (most recent first)
        type_bulletins.sort(key=lambda x: x['timestamp'], reverse=True)
        return type_bulletins
    
    def get_system_messages(self) -> List[Dict]:
        """
        Get all system-related bulletins.
        
        Returns:
            list: List of system message bulletins
        """
        system_bulletins = []
        
        for bulletin in self.bulletins.values():
            if bulletin['is_system_message']:
                system_bulletins.append(bulletin)
        
        # Sort by timestamp (most recent first)
        system_bulletins.sort(key=lambda x: x['timestamp'], reverse=True)
        return system_bulletins
    
    def get_high_priority_bulletins(self) -> List[Dict]:
        """
        Get high priority bulletins.
        
        Returns:
            list: List of high priority bulletins
        """
        high_priority = []
        
        for bulletin in self.bulletins.values():
            if bulletin['priority'] == 'HIGH':
                high_priority.append(bulletin)
        
        # Sort by timestamp (most recent first)
        high_priority.sort(key=lambda x: x['timestamp'], reverse=True)
        return high_priority
    
    def get_bulletins_summary(self) -> Dict:
        """
        Get summary statistics of received bulletins.
        
        Returns:
            dict: Bulletin summary statistics
        """
        if not self.bulletins:
            return {'total_bulletins': 0}
        
        # Count by type
        type_counts = {}
        priority_counts = {'HIGH': 0, 'MEDIUM': 0, 'LOW': 0}
        system_count = 0
        
        for bulletin in self.bulletins.values():
            # Count by type
            msg_type = bulletin['msg_type']
            type_desc = bulletin['type_description']
            type_counts[type_desc] = type_counts.get(type_desc, 0) + 1
            
            # Count by priority
            priority = bulletin['priority']
            priority_counts[priority] += 1
            
            # Count system messages
            if bulletin['is_system_message']:
                system_count += 1
        
        # Get time range
        timestamps = [bulletin['timestamp'] for bulletin in self.bulletins.values()]
        
        summary = {
            'total_bulletins': len(self.bulletins),
            'by_type': type_counts,
            'by_priority': priority_counts,
            'system_messages': system_count,
            'time_range': {
                'earliest': min(timestamps) if timestamps else None,
                'latest': max(timestamps) if timestamps else None
            },
            'active_subscriptions': list(self.active_subscriptions)
        }
        
        return summary
    
    def format_bulletin_report(self, bulletin: Dict) -> str:
        """
        Format a bulletin into a readable report.
        
        Args:
            bulletin (dict): Bulletin data
            
        Returns:
            str: Formatted bulletin report
        """
        priority_indicator = {
            'HIGH': '🚨',
            'MEDIUM': '⚠️',
            'LOW': 'ℹ️'
        }.get(bulletin['priority'], 'ℹ️')
        
        report_lines = [
            f"{priority_indicator} BULLETIN {bulletin['msg_id']} - {bulletin['priority']} PRIORITY",
            f"Type: {bulletin['type_description']}",
            f"Time: {bulletin['time_formatted']}",
            f"Exchange: {bulletin['origin_exchange']}",
            f"Message: {bulletin['message']}"
        ]
        
        if bulletin['is_system_message']:
            report_lines.insert(1, "🔧 SYSTEM MESSAGE")
        
        return "\n".join(report_lines)
    
    def add_bulletin_callback(self, event_type: str, callback: Callable):
        """
        Add a callback for bulletin events.
        
        Args:
            event_type (str): Type of event
            callback: Callback function to add
        """
        if event_type in self.bulletin_callbacks:
            self.bulletin_callbacks[event_type].append(callback)


def demonstrate_bulletin_subscriptions():
    """
    Demonstrate bulletin subscriptions functionality with practical examples.
    """
    print("=== Bulletin Subscriptions Demo ===")
    
    app = BulletinSubscriptionsApp()
    
    # Add callbacks for demonstration
    def on_bulletin(msg_id, msg_type, message, exchange):
        type_desc = app.bulletin_types.get(msg_type, f"Type_{msg_type}")
        print(f"📢 Bulletin {msg_id}: {type_desc}")
        print(f"   {message}")
    
    def on_system_message(msg_id, msg_type, message, exchange):
        print(f"🔧 System Message {msg_id}: {message}")
    
    def on_bulletin_received(bulletin):
        if bulletin['priority'] == 'HIGH':
            print(f"🚨 HIGH PRIORITY: {bulletin['message']}")
    
    app.add_bulletin_callback('update_news_bulletin', on_bulletin)
    app.add_bulletin_callback('system_message', on_system_message)
    app.add_bulletin_callback('bulletin_received', on_bulletin_received)
    
    try:
        # Connect to TWS
        app.connect("127.0.0.1", 7497, 0)
        
        # Start processing thread
        api_thread = threading.Thread(target=app.run, daemon=True)
        api_thread.start()
        
        # Wait for connection
        time.sleep(3)
        
        if app.isConnected():
            print("Connected - subscribing to bulletins")
            
            # Subscribe to all bulletins
            if app.request_bulletin_subscriptions(all_messages=True):
                print("Bulletin subscription requested")
            
            # Monitor bulletins for a while
            print("Monitoring bulletins for 30 seconds...")
            time.sleep(30)
            
            # Display bulletin summary
            summary = app.get_bulletins_summary()
            print("\n--- Bulletin Summary ---")
            print(f"Total bulletins received: {summary['total_bulletins']}")
            
            if summary['total_bulletins'] > 0:
                print(f"System messages: {summary['system_messages']}")
                print("By priority:")
                for priority, count in summary['by_priority'].items():
                    if count > 0:
                        print(f"  {priority}: {count}")
                
                print("By type:")
                for type_desc, count in summary['by_type'].items():
                    print(f"  {type_desc}: {count}")
                
                # Show recent bulletins
                recent = app.get_recent_bulletins(1)  # Last hour
                if recent:
                    print(f"\nRecent bulletins ({len(recent)}):")
                    for bulletin in recent[:3]:  # Show first 3
                        print(f"  {bulletin['time_formatted']}: {bulletin['type_description']}")
                        print(f"    {bulletin['message'][:100]}...")
                
                # Show high priority bulletins
                high_priority = app.get_high_priority_bulletins()
                if high_priority:
                    print(f"\nHigh priority bulletins ({len(high_priority)}):")
                    for bulletin in high_priority[:2]:  # Show first 2
                        print(app.format_bulletin_report(bulletin))
                        print("-" * 50)
            
            # Cancel bulletin subscriptions
            app.cancel_bulletin_subscriptions()
            print("Bulletin subscriptions canceled")
        
        else:
            print("Failed to connect to TWS")
    
    except KeyboardInterrupt:
        print("\nShutting down...")
    
    finally:
        app.disconnect()


if __name__ == "__main__":
    """
    Main execution block for bulletin subscriptions demonstrations.
    """
    print("TWS API Bulletin Subscriptions Examples")
    print("=" * 45)
    
    # Run the demonstration
    demonstrate_bulletin_subscriptions()
