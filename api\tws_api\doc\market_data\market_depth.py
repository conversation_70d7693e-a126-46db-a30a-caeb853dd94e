"""
Market Depth (Level II) Data

This module demonstrates how to request and receive Level II market depth
data from the TWS API. Market depth provides detailed order book information
showing multiple bid and ask price levels with their corresponding sizes.

Key Topics Covered:
- Requesting Level II market depth data
- Handling market depth updates and changes
- Processing bid and ask side depth information
- Understanding market maker information
- Managing market depth subscriptions
- Analyzing order book dynamics
- Best practices for depth data handling

Market depth data is essential for understanding market microstructure,
order flow analysis, and implementing sophisticated trading strategies.
"""

import threading
import time
import logging
from typing import Dict, List, Optional, Callable, Tuple
from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract


class MarketDepthApp(EClient, EWrapper):
    """
    TWS API application for Level II market depth data.
    
    This class demonstrates how to request, receive, and manage market
    depth subscriptions with comprehensive order book processing.
    
    Attributes:
        depth_data (dict): Storage for market depth data by request ID
        active_subscriptions (dict): Active market depth subscriptions
        depth_callbacks (dict): Callbacks for market depth events
        market_makers (dict): Market maker information storage
    """
    
    def __init__(self):
        """Initialize the market depth application."""
        EClient.__init__(self, self)
        
        # Data storage
        self.depth_data: Dict[int, Dict] = {}
        self.active_subscriptions: Dict[int, Contract] = {}
        self.market_makers: Dict[int, Dict] = {}
        
        # Callback management
        self.depth_callbacks = {
            'depth_update': [],
            'depth_l2_update': [],
            'depth_exchange_update': []
        }
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def connectAck(self):
        """Handle successful connection."""
        self.logger.info("Connected to TWS - Market Depth service ready")
    
    def nextValidId(self, orderId):
        """
        Receive next valid order ID.
        
        Args:
            orderId (int): Next valid order ID
        """
        self.logger.info(f"Connection ready. Next valid order ID: {orderId}")
    
    def updateMktDepth(self, reqId: int, position: int, operation: int, 
                      side: int, price: float, size: int):
        """
        Receive Level I market depth updates.
        
        Args:
            reqId (int): Request ID for the market depth subscription
            position (int): Position in the order book (0-based)
            operation (int): Operation type (0=insert, 1=update, 2=delete)
            side (int): Side of the book (0=ask, 1=bid)
            price (float): Price level
            size (int): Size at this price level
        """
        # Initialize data structure if needed
        if reqId not in self.depth_data:
            self.depth_data[reqId] = {
                'bids': {},  # position -> {price, size, timestamp}
                'asks': {}   # position -> {price, size, timestamp}
            }
        
        side_name = 'bids' if side == 1 else 'asks'
        operation_names = {0: 'INSERT', 1: 'UPDATE', 2: 'DELETE'}
        
        # Process the depth update
        if operation == 0:  # INSERT
            self.depth_data[reqId][side_name][position] = {
                'price': price,
                'size': size,
                'timestamp': time.time()
            }
        elif operation == 1:  # UPDATE
            if position in self.depth_data[reqId][side_name]:
                self.depth_data[reqId][side_name][position].update({
                    'price': price,
                    'size': size,
                    'timestamp': time.time()
                })
        elif operation == 2:  # DELETE
            if position in self.depth_data[reqId][side_name]:
                del self.depth_data[reqId][side_name][position]
        
        self.logger.debug(f"Depth Update - ReqId: {reqId}, {operation_names.get(operation)} "
                         f"{side_name.upper()} Position: {position}, Price: {price}, Size: {size}")
        
        # Trigger callbacks
        for callback in self.depth_callbacks['depth_update']:
            try:
                callback(reqId, position, operation, side, price, size)
            except Exception as e:
                self.logger.error(f"Depth update callback error: {e}")
    
    def updateMktDepthL2(self, reqId: int, position: int, marketMaker: str,
                        operation: int, side: int, price: float, size: int, isSmartDepth: bool):
        """
        Receive Level II market depth updates with market maker information.
        
        Args:
            reqId (int): Request ID for the market depth subscription
            position (int): Position in the order book
            marketMaker (str): Market maker identifier
            operation (int): Operation type (0=insert, 1=update, 2=delete)
            side (int): Side of the book (0=ask, 1=bid)
            price (float): Price level
            size (int): Size at this price level
            isSmartDepth (bool): Whether this is smart depth data
        """
        # Initialize data structure if needed
        if reqId not in self.depth_data:
            self.depth_data[reqId] = {
                'bids': {},
                'asks': {}
            }
        
        if reqId not in self.market_makers:
            self.market_makers[reqId] = {}
        
        side_name = 'bids' if side == 1 else 'asks'
        operation_names = {0: 'INSERT', 1: 'UPDATE', 2: 'DELETE'}
        
        # Process the L2 depth update
        if operation == 0:  # INSERT
            self.depth_data[reqId][side_name][position] = {
                'price': price,
                'size': size,
                'market_maker': marketMaker,
                'is_smart_depth': isSmartDepth,
                'timestamp': time.time()
            }
            self.market_makers[reqId][marketMaker] = {
                'last_seen': time.time(),
                'is_smart_depth': isSmartDepth
            }
        elif operation == 1:  # UPDATE
            if position in self.depth_data[reqId][side_name]:
                self.depth_data[reqId][side_name][position].update({
                    'price': price,
                    'size': size,
                    'market_maker': marketMaker,
                    'is_smart_depth': isSmartDepth,
                    'timestamp': time.time()
                })
        elif operation == 2:  # DELETE
            if position in self.depth_data[reqId][side_name]:
                del self.depth_data[reqId][side_name][position]
        
        self.logger.debug(f"L2 Depth Update - ReqId: {reqId}, {operation_names.get(operation)} "
                         f"{side_name.upper()} Position: {position}, MM: {marketMaker}, "
                         f"Price: {price}, Size: {size}")
        
        # Trigger callbacks
        for callback in self.depth_callbacks['depth_l2_update']:
            try:
                callback(reqId, position, marketMaker, operation, side, price, size, isSmartDepth)
            except Exception as e:
                self.logger.error(f"L2 depth update callback error: {e}")
    
    def updateMktDepthExchanges(self, depthMktDataDescriptions):
        """
        Receive market depth exchange information.
        
        Args:
            depthMktDataDescriptions: List of depth market data descriptions
        """
        self.logger.info("Market Depth Exchanges Update:")
        for desc in depthMktDataDescriptions:
            self.logger.info(f"  Exchange: {desc.exchange}, SecType: {desc.secType}, "
                           f"ListingExch: {desc.listingExch}, ServiceDataType: {desc.serviceDataType}")
        
        # Trigger callbacks
        for callback in self.depth_callbacks['depth_exchange_update']:
            try:
                callback(depthMktDataDescriptions)
            except Exception as e:
                self.logger.error(f"Depth exchange update callback error: {e}")
    
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors related to market depth requests.
        
        Args:
            reqId (int): Request ID
            errorCode (int): Error code
            errorString (str): Error description
            advancedOrderRejectJson (str): Additional error details
        """
        self.logger.error(f"Error {reqId} {errorCode} {errorString}")
        
        # Handle market depth specific errors
        if reqId in self.active_subscriptions:
            if errorCode in [200, 354]:  # No security definition, no market data permissions
                self.logger.error(f"Market depth subscription {reqId} failed: {errorString}")
    
    def request_market_depth(self, req_id: int, contract: Contract, 
                           num_rows: int = 10, is_smart_depth: bool = False,
                           market_depth_options: List = None) -> bool:
        """
        Request Level II market depth data.
        
        Args:
            req_id (int): Unique request identifier
            contract (Contract): Contract to get market depth for
            num_rows (int): Number of depth rows to retrieve (max 20)
            is_smart_depth (bool): Request smart depth data
            market_depth_options (list): Additional market depth options
            
        Returns:
            bool: True if request was sent successfully
        """
        if req_id in self.active_subscriptions:
            self.logger.error(f"Request ID {req_id} already has an active market depth subscription")
            return False
        
        if num_rows > 20:
            self.logger.warning("Maximum 20 depth rows allowed, limiting to 20")
            num_rows = 20
        
        try:
            if market_depth_options is None:
                market_depth_options = []
            
            if is_smart_depth:
                self.reqMktDepth(req_id, contract, num_rows, True, market_depth_options)
            else:
                self.reqMktDepth(req_id, contract, num_rows, False, market_depth_options)
            
            self.active_subscriptions[req_id] = contract
            
            self.logger.info(f"Requested market depth - ReqId: {req_id}, "
                           f"Symbol: {contract.symbol}, Rows: {num_rows}, "
                           f"Smart Depth: {is_smart_depth}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to request market depth: {e}")
            return False
    
    def cancel_market_depth(self, req_id: int, is_smart_depth: bool = False) -> bool:
        """
        Cancel a market depth subscription.
        
        Args:
            req_id (int): Request ID to cancel
            is_smart_depth (bool): Whether this was a smart depth subscription
            
        Returns:
            bool: True if cancellation was sent successfully
        """
        if req_id not in self.active_subscriptions:
            self.logger.warning(f"No active market depth subscription found for request ID {req_id}")
            return False
        
        try:
            self.cancelMktDepth(req_id, is_smart_depth)
            del self.active_subscriptions[req_id]
            
            self.logger.info(f"Canceled market depth subscription - ReqId: {req_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cancel market depth: {e}")
            return False
    
    def request_depth_exchanges(self):
        """Request information about available market depth exchanges."""
        try:
            self.reqMktDepthExchanges()
            self.logger.info("Requested market depth exchanges information")
        except Exception as e:
            self.logger.error(f"Failed to request depth exchanges: {e}")
    
    def get_depth_data(self, req_id: int) -> Dict:
        """
        Get market depth data for a specific request.
        
        Args:
            req_id (int): Request identifier
            
        Returns:
            dict: Market depth data
        """
        return self.depth_data.get(req_id, {'bids': {}, 'asks': {}})
    
    def get_best_bid_ask(self, req_id: int) -> Tuple[Optional[float], Optional[float]]:
        """
        Get the best bid and ask prices from depth data.
        
        Args:
            req_id (int): Request identifier
            
        Returns:
            tuple: (best_bid, best_ask) or (None, None) if no data
        """
        depth = self.get_depth_data(req_id)
        
        best_bid = None
        best_ask = None
        
        # Find best bid (highest price)
        if depth['bids']:
            best_bid = max(level['price'] for level in depth['bids'].values())
        
        # Find best ask (lowest price)
        if depth['asks']:
            best_ask = min(level['price'] for level in depth['asks'].values())
        
        return best_bid, best_ask
    
    def get_depth_summary(self, req_id: int, num_levels: int = 5) -> Dict:
        """
        Get a summary of market depth showing top levels.
        
        Args:
            req_id (int): Request identifier
            num_levels (int): Number of levels to include
            
        Returns:
            dict: Depth summary
        """
        depth = self.get_depth_data(req_id)
        
        # Sort bids by price (descending) and asks by price (ascending)
        sorted_bids = sorted(
            [(pos, data) for pos, data in depth['bids'].items()],
            key=lambda x: x[1]['price'],
            reverse=True
        )[:num_levels]
        
        sorted_asks = sorted(
            [(pos, data) for pos, data in depth['asks'].items()],
            key=lambda x: x[1]['price']
        )[:num_levels]
        
        summary = {
            'bids': [{'position': pos, **data} for pos, data in sorted_bids],
            'asks': [{'position': pos, **data} for pos, data in sorted_asks],
            'spread': None,
            'total_bid_size': sum(data['size'] for _, data in sorted_bids),
            'total_ask_size': sum(data['size'] for _, data in sorted_asks)
        }
        
        # Calculate spread
        if sorted_bids and sorted_asks:
            best_bid = sorted_bids[0][1]['price']
            best_ask = sorted_asks[0][1]['price']
            summary['spread'] = best_ask - best_bid
        
        return summary
    
    def get_market_makers(self, req_id: int) -> Dict:
        """
        Get market maker information for a request.
        
        Args:
            req_id (int): Request identifier
            
        Returns:
            dict: Market maker information
        """
        return self.market_makers.get(req_id, {})
    
    def add_depth_callback(self, event_type: str, callback: Callable):
        """
        Add a callback for market depth events.
        
        Args:
            event_type (str): Type of event
            callback: Callback function to add
        """
        if event_type in self.depth_callbacks:
            self.depth_callbacks[event_type].append(callback)


def demonstrate_market_depth():
    """
    Demonstrate market depth functionality with practical examples.
    """
    print("=== Market Depth Demo ===")
    
    app = MarketDepthApp()
    
    # Add callbacks for demonstration
    def on_depth_update(req_id, position, operation, side, price, size):
        operations = {0: 'INSERT', 1: 'UPDATE', 2: 'DELETE'}
        sides = {0: 'ASK', 1: 'BID'}
        print(f"Depth: {operations.get(operation)} {sides.get(side)} "
              f"Pos:{position} Price:{price} Size:{size}")
    
    def on_l2_update(req_id, position, market_maker, operation, side, price, size, is_smart):
        operations = {0: 'INSERT', 1: 'UPDATE', 2: 'DELETE'}
        sides = {0: 'ASK', 1: 'BID'}
        print(f"L2 Depth: {operations.get(operation)} {sides.get(side)} "
              f"MM:{market_maker} Price:{price} Size:{size}")
    
    app.add_depth_callback('depth_update', on_depth_update)
    app.add_depth_callback('depth_l2_update', on_l2_update)
    
    try:
        # Connect to TWS
        app.connect("127.0.0.1", 7497, 0)
        
        # Start processing thread
        api_thread = threading.Thread(target=app.run, daemon=True)
        api_thread.start()
        
        # Wait for connection
        time.sleep(3)
        
        if app.isConnected():
            print("Connected - requesting market depth")
            
            # Request depth exchanges information
            app.request_depth_exchanges()
            
            # Create sample contracts
            from api.tws_api.doc.contracts.contract_creation import ContractBuilder
            builder = ContractBuilder()
            
            # Request market depth for Apple stock
            aapl_contract = builder.create_stock_contract("AAPL")
            if app.request_market_depth(9001, aapl_contract, 10):
                print("AAPL market depth requested")
            
            # Request Level II depth for a liquid stock
            spy_contract = builder.create_stock_contract("SPY")
            if app.request_market_depth(9002, spy_contract, 10, True):
                print("SPY Level II market depth requested")
            
            # Monitor for updates
            print("Monitoring market depth for 20 seconds...")
            time.sleep(20)
            
            # Display depth summary
            print("\n--- Market Depth Summary ---")
            for req_id in [9001, 9002]:
                contract = app.active_subscriptions.get(req_id)
                if contract:
                    summary = app.get_depth_summary(req_id, 3)
                    print(f"\n{contract.symbol} Depth:")
                    
                    print("  Bids:")
                    for bid in summary['bids']:
                        print(f"    {bid['price']:.2f} x {bid['size']}")
                    
                    print("  Asks:")
                    for ask in summary['asks']:
                        print(f"    {ask['price']:.2f} x {ask['size']}")
                    
                    if summary['spread']:
                        print(f"  Spread: {summary['spread']:.2f}")
                    
                    # Show market makers if available
                    market_makers = app.get_market_makers(req_id)
                    if market_makers:
                        print(f"  Market Makers: {list(market_makers.keys())}")
            
            # Cancel subscriptions
            for req_id in list(app.active_subscriptions.keys()):
                app.cancel_market_depth(req_id)
            print("Market depth subscriptions canceled")
        
        else:
            print("Failed to connect to TWS")
    
    except KeyboardInterrupt:
        print("\nShutting down...")
    
    finally:
        # Cancel all active subscriptions
        for req_id in list(app.active_subscriptions.keys()):
            app.cancel_market_depth(req_id)
        
        app.disconnect()


if __name__ == "__main__":
    """
    Main execution block for market depth demonstrations.
    """
    print("TWS API Market Depth Examples")
    print("=" * 35)
    
    # Run the demonstration
    demonstrate_market_depth()
