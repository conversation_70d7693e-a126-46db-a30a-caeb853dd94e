"""
Order Types and Examples

This module provides comprehensive examples of different order types
supported by the TWS API. Understanding various order types is essential
for implementing sophisticated trading strategies.

Key Topics Covered:
- Market orders for immediate execution
- Limit orders with price constraints
- Stop orders for risk management
- Stop-limit orders combining stop and limit features
- Trailing stop orders for dynamic stop management
- Bracket orders for automated profit-taking and stop-loss
- Conditional orders with complex logic
- Time-in-force options and order duration

Each order type serves specific trading purposes and understanding their
characteristics helps in choosing the right order type for each situation.
"""

import logging
from typing import Dict, List, Optional
from ibapi.order import Order
from ibapi.contract import Contract


class OrderTypeBuilder:
    """
    Builder class for creating different types of orders with proper configuration.
    
    This class provides methods for creating various order types with
    appropriate parameters and validation.
    
    Attributes:
        logger: Logger instance for debugging and information
    """
    
    def __init__(self):
        """Initialize the order type builder."""
        self.logger = logging.getLogger(__name__)
    
    def create_market_order(self, action: str, quantity: float, 
                          account: str = "", time_in_force: str = "DAY") -> Order:
        """
        Create a market order for immediate execution at current market price.
        
        Market orders are executed immediately at the best available price.
        They guarantee execution but not price.
        
        Args:
            action (str): "BUY" or "SELL"
            quantity (float): Number of shares/contracts
            account (str): Account for the order (optional)
            time_in_force (str): Order duration (DAY, GTC, IOC, FOK)
            
        Returns:
            Order: Configured market order
        """
        order = Order()
        order.action = action.upper()
        order.totalQuantity = quantity
        order.orderType = "MKT"
        order.tif = time_in_force
        
        if account:
            order.account = account
        
        self.logger.info(f"Created market order: {action} {quantity} shares")
        return order
    
    def create_limit_order(self, action: str, quantity: float, limit_price: float,
                          account: str = "", time_in_force: str = "DAY") -> Order:
        """
        Create a limit order with a specified maximum/minimum price.
        
        Limit orders guarantee price but not execution. Buy orders execute
        at or below the limit price, sell orders at or above.
        
        Args:
            action (str): "BUY" or "SELL"
            quantity (float): Number of shares/contracts
            limit_price (float): Maximum buy price or minimum sell price
            account (str): Account for the order (optional)
            time_in_force (str): Order duration
            
        Returns:
            Order: Configured limit order
        """
        order = Order()
        order.action = action.upper()
        order.totalQuantity = quantity
        order.orderType = "LMT"
        order.lmtPrice = limit_price
        order.tif = time_in_force
        
        if account:
            order.account = account
        
        self.logger.info(f"Created limit order: {action} {quantity} @ ${limit_price}")
        return order
    
    def create_stop_order(self, action: str, quantity: float, stop_price: float,
                         account: str = "", time_in_force: str = "DAY") -> Order:
        """
        Create a stop order that becomes a market order when triggered.
        
        Stop orders are used for risk management. They become market orders
        when the stop price is reached.
        
        Args:
            action (str): "BUY" or "SELL"
            quantity (float): Number of shares/contracts
            stop_price (float): Price that triggers the order
            account (str): Account for the order (optional)
            time_in_force (str): Order duration
            
        Returns:
            Order: Configured stop order
        """
        order = Order()
        order.action = action.upper()
        order.totalQuantity = quantity
        order.orderType = "STP"
        order.auxPrice = stop_price
        order.tif = time_in_force
        
        if account:
            order.account = account
        
        self.logger.info(f"Created stop order: {action} {quantity} stop @ ${stop_price}")
        return order
    
    def create_stop_limit_order(self, action: str, quantity: float, stop_price: float,
                               limit_price: float, account: str = "", 
                               time_in_force: str = "DAY") -> Order:
        """
        Create a stop-limit order combining stop and limit features.
        
        Stop-limit orders become limit orders when the stop price is triggered,
        providing both price protection and execution control.
        
        Args:
            action (str): "BUY" or "SELL"
            quantity (float): Number of shares/contracts
            stop_price (float): Price that triggers the order
            limit_price (float): Limit price after trigger
            account (str): Account for the order (optional)
            time_in_force (str): Order duration
            
        Returns:
            Order: Configured stop-limit order
        """
        order = Order()
        order.action = action.upper()
        order.totalQuantity = quantity
        order.orderType = "STP LMT"
        order.auxPrice = stop_price
        order.lmtPrice = limit_price
        order.tif = time_in_force
        
        if account:
            order.account = account
        
        self.logger.info(f"Created stop-limit order: {action} {quantity} "
                        f"stop @ ${stop_price}, limit @ ${limit_price}")
        return order
    
    def create_trailing_stop_order(self, action: str, quantity: float, 
                                  trail_amount: float, trail_percent: float = None,
                                  account: str = "", time_in_force: str = "DAY") -> Order:
        """
        Create a trailing stop order that adjusts with favorable price movement.
        
        Trailing stops automatically adjust the stop price as the market
        moves favorably, locking in profits while limiting losses.
        
        Args:
            action (str): "BUY" or "SELL"
            quantity (float): Number of shares/contracts
            trail_amount (float): Fixed trailing amount in currency
            trail_percent (float): Trailing percentage (alternative to amount)
            account (str): Account for the order (optional)
            time_in_force (str): Order duration
            
        Returns:
            Order: Configured trailing stop order
        """
        order = Order()
        order.action = action.upper()
        order.totalQuantity = quantity
        order.orderType = "TRAIL"
        order.tif = time_in_force
        
        if trail_percent is not None:
            order.trailingPercent = trail_percent
            trail_desc = f"{trail_percent}%"
        else:
            order.auxPrice = trail_amount
            trail_desc = f"${trail_amount}"
        
        if account:
            order.account = account
        
        self.logger.info(f"Created trailing stop order: {action} {quantity} "
                        f"trail {trail_desc}")
        return order
    
    def create_market_on_close_order(self, action: str, quantity: float,
                                    account: str = "") -> Order:
        """
        Create a market-on-close order for execution at closing price.
        
        MOC orders are executed at the closing auction, providing
        execution at or near the closing price.
        
        Args:
            action (str): "BUY" or "SELL"
            quantity (float): Number of shares/contracts
            account (str): Account for the order (optional)
            
        Returns:
            Order: Configured market-on-close order
        """
        order = Order()
        order.action = action.upper()
        order.totalQuantity = quantity
        order.orderType = "MOC"
        order.tif = "DAY"
        
        if account:
            order.account = account
        
        self.logger.info(f"Created market-on-close order: {action} {quantity}")
        return order
    
    def create_limit_on_close_order(self, action: str, quantity: float, 
                                   limit_price: float, account: str = "") -> Order:
        """
        Create a limit-on-close order for execution at closing auction.
        
        LOC orders participate in the closing auction with a price limit,
        combining closing execution with price protection.
        
        Args:
            action (str): "BUY" or "SELL"
            quantity (float): Number of shares/contracts
            limit_price (float): Maximum buy price or minimum sell price
            account (str): Account for the order (optional)
            
        Returns:
            Order: Configured limit-on-close order
        """
        order = Order()
        order.action = action.upper()
        order.totalQuantity = quantity
        order.orderType = "LOC"
        order.lmtPrice = limit_price
        order.tif = "DAY"
        
        if account:
            order.account = account
        
        self.logger.info(f"Created limit-on-close order: {action} {quantity} @ ${limit_price}")
        return order
    
    def create_iceberg_order(self, action: str, quantity: float, limit_price: float,
                           display_size: int, account: str = "", 
                           time_in_force: str = "DAY") -> Order:
        """
        Create an iceberg order that only shows a portion of the total size.
        
        Iceberg orders hide the full order size by only displaying
        a small portion, reducing market impact.
        
        Args:
            action (str): "BUY" or "SELL"
            quantity (float): Total number of shares/contracts
            limit_price (float): Limit price
            display_size (int): Number of shares to display
            account (str): Account for the order (optional)
            time_in_force (str): Order duration
            
        Returns:
            Order: Configured iceberg order
        """
        order = Order()
        order.action = action.upper()
        order.totalQuantity = quantity
        order.orderType = "LMT"
        order.lmtPrice = limit_price
        order.displaySize = display_size
        order.tif = time_in_force
        
        if account:
            order.account = account
        
        self.logger.info(f"Created iceberg order: {action} {quantity} @ ${limit_price} "
                        f"(display {display_size})")
        return order
    
    def create_midprice_order(self, action: str, quantity: float, 
                             account: str = "", time_in_force: str = "DAY") -> Order:
        """
        Create a midprice order that executes at the bid-ask midpoint.
        
        Midprice orders attempt to execute at the midpoint between
        the current bid and ask prices.
        
        Args:
            action (str): "BUY" or "SELL"
            quantity (float): Number of shares/contracts
            account (str): Account for the order (optional)
            time_in_force (str): Order duration
            
        Returns:
            Order: Configured midprice order
        """
        order = Order()
        order.action = action.upper()
        order.totalQuantity = quantity
        order.orderType = "MIDPRICE"
        order.tif = time_in_force
        
        if account:
            order.account = account
        
        self.logger.info(f"Created midprice order: {action} {quantity}")
        return order
    
    def create_pegged_to_market_order(self, action: str, quantity: float,
                                     offset: float = 0.0, account: str = "",
                                     time_in_force: str = "DAY") -> Order:
        """
        Create an order pegged to the market with an optional offset.
        
        Pegged orders automatically adjust their price relative to
        the market, maintaining a specified relationship.
        
        Args:
            action (str): "BUY" or "SELL"
            quantity (float): Number of shares/contracts
            offset (float): Price offset from market (can be negative)
            account (str): Account for the order (optional)
            time_in_force (str): Order duration
            
        Returns:
            Order: Configured pegged order
        """
        order = Order()
        order.action = action.upper()
        order.totalQuantity = quantity
        order.orderType = "PEG MKT"
        order.auxPrice = offset
        order.tif = time_in_force
        
        if account:
            order.account = account
        
        offset_desc = f" offset ${offset}" if offset != 0 else ""
        self.logger.info(f"Created pegged market order: {action} {quantity}{offset_desc}")
        return order
    
    def create_bracket_order(self, action: str, quantity: float, limit_price: float,
                           profit_target: float, stop_loss: float,
                           account: str = "") -> List[Order]:
        """
        Create a bracket order with parent, profit target, and stop loss.
        
        Bracket orders automatically place profit-taking and stop-loss
        orders when the parent order is filled.
        
        Args:
            action (str): "BUY" or "SELL"
            quantity (float): Number of shares/contracts
            limit_price (float): Entry price
            profit_target (float): Profit-taking price
            stop_loss (float): Stop-loss price
            account (str): Account for the order (optional)
            
        Returns:
            List[Order]: List containing parent, profit, and stop orders
        """
        # Parent order
        parent = Order()
        parent.action = action.upper()
        parent.totalQuantity = quantity
        parent.orderType = "LMT"
        parent.lmtPrice = limit_price
        parent.tif = "DAY"
        parent.transmit = False  # Don't transmit until all orders are ready
        
        if account:
            parent.account = account
        
        # Profit target order (opposite action)
        profit_action = "SELL" if action.upper() == "BUY" else "BUY"
        profit = Order()
        profit.action = profit_action
        profit.totalQuantity = quantity
        profit.orderType = "LMT"
        profit.lmtPrice = profit_target
        profit.tif = "GTC"
        profit.parentId = parent.orderId
        profit.transmit = False
        
        if account:
            profit.account = account
        
        # Stop loss order (opposite action)
        stop = Order()
        stop.action = profit_action
        stop.totalQuantity = quantity
        stop.orderType = "STP"
        stop.auxPrice = stop_loss
        stop.tif = "GTC"
        stop.parentId = parent.orderId
        stop.transmit = True  # Transmit all orders
        
        if account:
            stop.account = account
        
        self.logger.info(f"Created bracket order: {action} {quantity} @ ${limit_price} "
                        f"(target: ${profit_target}, stop: ${stop_loss})")
        
        return [parent, profit, stop]
    
    def get_order_description(self, order: Order) -> str:
        """
        Generate a human-readable description of an order.
        
        Args:
            order (Order): Order to describe
            
        Returns:
            str: Order description
        """
        desc_parts = [
            f"{order.action} {order.totalQuantity}",
            f"Type: {order.orderType}"
        ]
        
        if order.orderType == "LMT" and order.lmtPrice:
            desc_parts.append(f"Limit: ${order.lmtPrice}")
        
        if order.orderType in ["STP", "STP LMT"] and order.auxPrice:
            desc_parts.append(f"Stop: ${order.auxPrice}")
        
        if order.orderType == "STP LMT" and order.lmtPrice:
            desc_parts.append(f"Limit: ${order.lmtPrice}")
        
        if order.orderType == "TRAIL":
            if order.trailingPercent:
                desc_parts.append(f"Trail: {order.trailingPercent}%")
            elif order.auxPrice:
                desc_parts.append(f"Trail: ${order.auxPrice}")
        
        if order.tif and order.tif != "DAY":
            desc_parts.append(f"TIF: {order.tif}")
        
        if order.displaySize:
            desc_parts.append(f"Display: {order.displaySize}")
        
        return " | ".join(desc_parts)


def demonstrate_order_types():
    """
    Demonstrate various order types and their configurations.
    """
    print("=== Order Types Demo ===")
    
    builder = OrderTypeBuilder()
    
    # Create examples of different order types
    order_examples = []
    
    # Basic order types
    market_order = builder.create_market_order("BUY", 100)
    order_examples.append(("Market Order", market_order))
    
    limit_order = builder.create_limit_order("BUY", 100, 150.00)
    order_examples.append(("Limit Order", limit_order))
    
    stop_order = builder.create_stop_order("SELL", 100, 145.00)
    order_examples.append(("Stop Order", stop_order))
    
    stop_limit_order = builder.create_stop_limit_order("SELL", 100, 145.00, 144.50)
    order_examples.append(("Stop-Limit Order", stop_limit_order))
    
    # Advanced order types
    trailing_stop = builder.create_trailing_stop_order("SELL", 100, trail_percent=5.0)
    order_examples.append(("Trailing Stop Order", trailing_stop))
    
    moc_order = builder.create_market_on_close_order("BUY", 100)
    order_examples.append(("Market-on-Close Order", moc_order))
    
    iceberg_order = builder.create_iceberg_order("BUY", 1000, 150.00, 100)
    order_examples.append(("Iceberg Order", iceberg_order))
    
    midprice_order = builder.create_midprice_order("BUY", 100)
    order_examples.append(("Midprice Order", midprice_order))
    
    pegged_order = builder.create_pegged_to_market_order("BUY", 100, -0.05)
    order_examples.append(("Pegged Market Order", pegged_order))
    
    # Display order examples
    print("\n--- Order Type Examples ---")
    for name, order in order_examples:
        description = builder.get_order_description(order)
        print(f"\n{name}:")
        print(f"  {description}")
        print(f"  Order Type: {order.orderType}")
        if hasattr(order, 'lmtPrice') and order.lmtPrice:
            print(f"  Limit Price: ${order.lmtPrice}")
        if hasattr(order, 'auxPrice') and order.auxPrice:
            print(f"  Aux Price: ${order.auxPrice}")
        if hasattr(order, 'tif'):
            print(f"  Time in Force: {order.tif}")
    
    # Demonstrate bracket order
    print("\n--- Bracket Order Example ---")
    bracket_orders = builder.create_bracket_order("BUY", 100, 150.00, 155.00, 145.00)
    
    order_names = ["Parent (Entry)", "Profit Target", "Stop Loss"]
    for i, (name, order) in enumerate(zip(order_names, bracket_orders)):
        description = builder.get_order_description(order)
        print(f"\n{name}:")
        print(f"  {description}")
        print(f"  Transmit: {order.transmit}")
        if hasattr(order, 'parentId') and order.parentId:
            print(f"  Parent ID: {order.parentId}")
    
    # Show time-in-force options
    print("\n--- Time-in-Force Options ---")
    tif_options = {
        "DAY": "Order valid for the current trading day only",
        "GTC": "Good Till Canceled - remains active until filled or canceled",
        "IOC": "Immediate or Cancel - execute immediately or cancel",
        "FOK": "Fill or Kill - execute completely immediately or cancel",
        "GTD": "Good Till Date - active until specified date",
        "OPG": "At the Opening - execute at market opening only",
        "CLS": "At the Close - execute at market closing only"
    }
    
    for tif, description in tif_options.items():
        print(f"  {tif}: {description}")
    
    # Show order type use cases
    print("\n--- Order Type Use Cases ---")
    use_cases = {
        "Market Order": "Immediate execution when price is less important than speed",
        "Limit Order": "Price protection when you have a specific target price",
        "Stop Order": "Risk management to limit losses or protect profits",
        "Stop-Limit": "Combines stop trigger with price protection",
        "Trailing Stop": "Dynamic stop that follows favorable price movement",
        "Iceberg": "Hide large order size to reduce market impact",
        "Bracket": "Automated profit-taking and risk management"
    }
    
    for order_type, use_case in use_cases.items():
        print(f"  {order_type}: {use_case}")


if __name__ == "__main__":
    """
    Main execution block for order types demonstrations.
    """
    print("TWS API Order Types Examples")
    print("=" * 35)
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Run the demonstration
    demonstrate_order_types()
