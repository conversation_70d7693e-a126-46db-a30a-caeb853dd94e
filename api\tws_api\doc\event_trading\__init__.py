"""
TWS API Event Trading Documentation

This module provides comprehensive documentation and examples for Event
Trading functionality in the Interactive Brokers TWS API. Event Trading
enables automated trading based on specific market events and conditions.

The event_trading module covers:
- event_contracts: Creating and managing event-based contracts
- event_conditions: Setting up event-driven trading conditions
- event_monitoring: Monitoring market events and triggers
- event_orders: Placing orders based on event conditions
- event_strategies: Implementing event-driven trading strategies
- event_data: Working with event data and market information

Key Concepts:
- Event Contracts: Special contracts tied to specific events
- Event Conditions: Criteria that trigger trading actions
- Market Events: Specific occurrences that can trigger trades
- Event Monitoring: Continuous watching for event conditions
- Conditional Orders: Orders that execute when events occur
- Event Strategies: Systematic approaches to event-based trading

Usage:
    from api.tws_api.doc.event_trading import event_contracts
    from api.tws_api.doc.event_trading import event_conditions
    from api.tws_api.doc.event_trading import event_monitoring
    # ... import other modules as needed

Important Notes:
- Event trading requires specific market data permissions
- Event contracts may have unique characteristics and limitations
- Event conditions must be carefully defined to avoid false triggers
- Event trading strategies require thorough testing and validation
- Some events may be rare or have limited trading opportunities
"""

# Import all event trading modules for easy access
from . import event_contracts
from . import event_conditions
from . import event_monitoring
from . import event_orders
from . import event_strategies
from . import event_data

__all__ = [
    'event_contracts',
    'event_conditions',
    'event_monitoring',
    'event_orders',
    'event_strategies',
    'event_data'
]
