"""
Remote TWS API Connections

This module demonstrates how to establish and manage remote connections to
TWS or IB Gateway running on different machines. It covers network configuration,
security considerations, connection pooling, and load balancing techniques.

Key Topics Covered:
- Configuring TWS for remote API access
- Establishing remote connections with proper error handling
- Network security and validation
- Connection pooling for multiple remote hosts
- Load balancing across multiple TWS instances
- Firewall and network troubleshooting

Remote connections are essential for distributed trading systems where
the API client runs on a different machine than TWS/IB Gateway.
"""

import socket
import threading
import time
import random
import logging
from typing import List, Dict, Tuple, Optional
from ibapi.client import EClient
from ibapi.wrapper import EWrapper


class RemoteConnectionApp(EClient, EWrapper):
    """
    TWS API application optimized for remote connections.
    
    This class provides enhanced functionality for connecting to remote
    TWS instances with robust error handling, connection validation,
    and network diagnostics.
    
    Attributes:
        connection_params (dict): Current connection parameters
        connection_timeout (int): Connection timeout in seconds
        network_diagnostics (dict): Network diagnostic information
        remote_hosts (list): List of available remote hosts
    """
    
    def __init__(self):
        """Initialize the remote connection application."""
        EClient.__init__(self, self)
        
        # Connection management
        self.connection_params = {}
        self.connection_timeout = 30
        self.connected = False
        
        # Network diagnostics
        self.network_diagnostics = {
            'latency_tests': [],
            'connection_attempts': 0,
            'successful_connections': 0,
            'failed_connections': 0
        }
        
        # Remote host management
        self.remote_hosts = []
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def add_remote_host(self, host: str, port: int, description: str = ""):
        """
        Add a remote host to the available hosts list.
        
        Args:
            host (str): Remote host IP address or hostname
            port (int): TWS port number
            description (str): Optional description of the host
        """
        host_config = {
            'host': host,
            'port': port,
            'description': description,
            'last_used': None,
            'success_count': 0,
            'failure_count': 0
        }
        
        self.remote_hosts.append(host_config)
        self.logger.info(f"Added remote host: {host}:{port} ({description})")
    
    def validate_network_connectivity(self, host: str, port: int) -> bool:
        """
        Validate network connectivity to a remote host.
        
        This method performs basic network checks before attempting
        a TWS API connection to diagnose potential issues early.
        
        Args:
            host (str): Remote host address
            port (int): Remote port number
            
        Returns:
            bool: True if network connectivity is available
        """
        self.logger.info(f"Validating network connectivity to {host}:{port}")
        
        try:
            # Test basic socket connectivity
            test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            test_socket.settimeout(5)
            
            start_time = time.time()
            result = test_socket.connect_ex((host, port))
            latency = time.time() - start_time
            
            test_socket.close()
            
            if result == 0:
                self.network_diagnostics['latency_tests'].append({
                    'host': host,
                    'port': port,
                    'latency': latency,
                    'timestamp': time.time()
                })
                
                self.logger.info(f"Network connectivity OK (latency: {latency:.3f}s)")
                return True
            else:
                self.logger.warning(f"Network connectivity failed (error code: {result})")
                return False
                
        except Exception as e:
            self.logger.error(f"Network validation error: {e}")
            return False
    
    def connect_remote(self, host: str, port: int, clientId: int, 
                      validate_network: bool = True) -> bool:
        """
        Connect to a remote TWS instance with comprehensive error handling.
        
        Args:
            host (str): Remote host address
            port (int): Remote port number
            clientId (int): Client ID for the connection
            validate_network (bool): Whether to validate network first
            
        Returns:
            bool: True if connection successful
        """
        self.connection_params = {
            'host': host,
            'port': port,
            'clientId': clientId
        }
        
        self.network_diagnostics['connection_attempts'] += 1
        
        # Validate network connectivity first
        if validate_network and not self.validate_network_connectivity(host, port):
            self.logger.error("Network validation failed - aborting connection")
            self.network_diagnostics['failed_connections'] += 1
            return False
        
        try:
            self.logger.info(f"Attempting remote connection to {host}:{port}")
            
            # Attempt the TWS API connection
            self.connect(host, port, clientId)
            
            # Start message processing
            api_thread = threading.Thread(target=self.run, daemon=True)
            api_thread.start()
            
            # Wait for connection with timeout
            start_time = time.time()
            while not self.connected and (time.time() - start_time) < self.connection_timeout:
                time.sleep(0.1)
            
            if self.connected:
                self.logger.info(f"Successfully connected to {host}:{port}")
                self.network_diagnostics['successful_connections'] += 1
                
                # Update host statistics
                for host_config in self.remote_hosts:
                    if host_config['host'] == host and host_config['port'] == port:
                        host_config['success_count'] += 1
                        host_config['last_used'] = time.time()
                        break
                
                return True
            else:
                self.logger.error(f"Connection timeout after {self.connection_timeout} seconds")
                self.network_diagnostics['failed_connections'] += 1
                return False
                
        except Exception as e:
            self.logger.error(f"Remote connection error: {e}")
            self.network_diagnostics['failed_connections'] += 1
            return False
    
    def connectAck(self):
        """Handle successful connection acknowledgment."""
        self.connected = True
        self.logger.info("Remote connection established successfully")
    
    def connectionClosed(self):
        """Handle connection closure."""
        self.connected = False
        self.logger.warning("Remote connection closed")
    
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors with remote connection specific diagnostics.
        
        Args:
            reqId (int): Request ID
            errorCode (int): Error code
            errorString (str): Error description
            advancedOrderRejectJson (str): Additional error details
        """
        self.logger.error(f"Error {reqId} {errorCode} {errorString}")
        
        if errorCode == 502:
            self.logger.error("Remote connection failed. Check:")
            host = self.connection_params.get('host', 'unknown')
            port = self.connection_params.get('port', 'unknown')
            self.logger.error(f"1. TWS is running on {host}")
            self.logger.error("2. API is enabled in TWS settings")
            self.logger.error("3. Your IP is in the Trusted IPs list")
            self.logger.error("4. No firewall blocking the connection")
            self.logger.error(f"5. Port {port} is correct and accessible")
    
    def get_network_diagnostics(self) -> dict:
        """
        Get comprehensive network diagnostic information.
        
        Returns:
            dict: Network diagnostics and statistics
        """
        diagnostics = self.network_diagnostics.copy()
        
        # Calculate average latency
        if diagnostics['latency_tests']:
            latencies = [test['latency'] for test in diagnostics['latency_tests']]
            diagnostics['average_latency'] = sum(latencies) / len(latencies)
            diagnostics['min_latency'] = min(latencies)
            diagnostics['max_latency'] = max(latencies)
        
        # Calculate success rate
        total_attempts = diagnostics['connection_attempts']
        if total_attempts > 0:
            success_rate = diagnostics['successful_connections'] / total_attempts
            diagnostics['success_rate'] = success_rate
        
        return diagnostics


class RemoteConnectionPool:
    """
    Connection pool manager for multiple remote TWS instances.
    
    This class manages connections to multiple remote TWS instances,
    providing load balancing, failover, and connection pooling capabilities.
    
    Attributes:
        hosts (list): List of remote host configurations
        connections (dict): Active connections by client ID
        available_client_ids (list): Available client IDs for new connections
        load_balancing_strategy (str): Strategy for selecting hosts
    """
    
    def __init__(self, load_balancing_strategy: str = "round_robin"):
        """
        Initialize the remote connection pool.
        
        Args:
            load_balancing_strategy (str): Load balancing strategy
                Options: "round_robin", "random", "least_used"
        """
        self.hosts = []
        self.connections = {}
        self.available_client_ids = list(range(32))  # Client IDs 0-31
        self.load_balancing_strategy = load_balancing_strategy
        self.current_host_index = 0
        
        self.logger = logging.getLogger(__name__)
    
    def add_host(self, host: str, port: int, description: str = ""):
        """
        Add a remote host to the connection pool.
        
        Args:
            host (str): Remote host address
            port (int): Remote port number
            description (str): Optional host description
        """
        host_config = {
            'host': host,
            'port': port,
            'description': description,
            'active_connections': 0,
            'total_connections': 0,
            'last_used': 0,
            'available': True
        }
        
        self.hosts.append(host_config)
        self.logger.info(f"Added host to pool: {host}:{port}")
    
    def select_host(self) -> Optional[dict]:
        """
        Select a host based on the load balancing strategy.
        
        Returns:
            dict: Selected host configuration, or None if no hosts available
        """
        available_hosts = [h for h in self.hosts if h['available']]
        
        if not available_hosts:
            return None
        
        if self.load_balancing_strategy == "round_robin":
            host = available_hosts[self.current_host_index % len(available_hosts)]
            self.current_host_index += 1
            return host
            
        elif self.load_balancing_strategy == "random":
            return random.choice(available_hosts)
            
        elif self.load_balancing_strategy == "least_used":
            return min(available_hosts, key=lambda h: h['active_connections'])
        
        else:
            return available_hosts[0]  # Default to first available
    
    def get_connection(self) -> Tuple[Optional[int], Optional[RemoteConnectionApp]]:
        """
        Get an available connection from the pool.
        
        Returns:
            tuple: (client_id, connection_app) or (None, None) if unavailable
        """
        if not self.available_client_ids:
            self.logger.error("No available client IDs")
            return None, None
        
        host_config = self.select_host()
        if not host_config:
            self.logger.error("No available hosts")
            return None, None
        
        client_id = self.available_client_ids.pop(0)
        
        try:
            # Create new connection
            app = RemoteConnectionApp()
            
            if app.connect_remote(host_config['host'], host_config['port'], client_id):
                self.connections[client_id] = {
                    'app': app,
                    'host_config': host_config,
                    'created_time': time.time()
                }
                
                # Update host statistics
                host_config['active_connections'] += 1
                host_config['total_connections'] += 1
                host_config['last_used'] = time.time()
                
                self.logger.info(f"Created connection {client_id} to {host_config['host']}")
                return client_id, app
            else:
                # Return client ID to available pool
                self.available_client_ids.insert(0, client_id)
                self.logger.error(f"Failed to connect to {host_config['host']}")
                return None, None
                
        except Exception as e:
            self.available_client_ids.insert(0, client_id)
            self.logger.error(f"Connection creation error: {e}")
            return None, None
    
    def release_connection(self, client_id: int):
        """
        Release a connection back to the pool.
        
        Args:
            client_id (int): Client ID to release
        """
        if client_id in self.connections:
            connection_info = self.connections[client_id]
            
            # Disconnect the application
            connection_info['app'].disconnect()
            
            # Update host statistics
            connection_info['host_config']['active_connections'] -= 1
            
            # Remove from active connections
            del self.connections[client_id]
            
            # Return client ID to available pool
            self.available_client_ids.append(client_id)
            self.available_client_ids.sort()
            
            self.logger.info(f"Released connection {client_id}")
    
    def get_pool_status(self) -> dict:
        """
        Get comprehensive pool status information.
        
        Returns:
            dict: Pool status and statistics
        """
        return {
            'total_hosts': len(self.hosts),
            'available_hosts': len([h for h in self.hosts if h['available']]),
            'active_connections': len(self.connections),
            'available_client_ids': len(self.available_client_ids),
            'load_balancing_strategy': self.load_balancing_strategy,
            'host_details': self.hosts.copy()
        }


def demonstrate_remote_connection():
    """
    Demonstrate remote connection establishment and management.
    """
    print("=== Remote Connection Demo ===")
    
    app = RemoteConnectionApp()
    
    # Add some example remote hosts (these would be real IPs in practice)
    app.add_remote_host("127.0.0.1", 7497, "Local TWS Paper")
    app.add_remote_host("127.0.0.1", 4002, "Local Gateway Paper")
    
    # Attempt connection to first host
    if app.connect_remote("127.0.0.1", 7497, 0):
        print("Remote connection successful!")
        
        # Show network diagnostics
        diagnostics = app.get_network_diagnostics()
        print("\nNetwork Diagnostics:")
        for key, value in diagnostics.items():
            if isinstance(value, list) and len(value) > 0:
                print(f"  {key}: {len(value)} entries")
            else:
                print(f"  {key}: {value}")
        
        time.sleep(3)
        app.disconnect()
    else:
        print("Remote connection failed")


def demonstrate_connection_pool():
    """
    Demonstrate connection pooling with multiple remote hosts.
    """
    print("\n=== Connection Pool Demo ===")
    
    pool = RemoteConnectionPool("round_robin")
    
    # Add hosts to the pool
    pool.add_host("127.0.0.1", 7497, "Local TWS Paper")
    pool.add_host("127.0.0.1", 4002, "Local Gateway Paper")
    
    print("Connection pool created with 2 hosts")
    
    # Get pool status
    status = pool.get_pool_status()
    print(f"Pool status: {status['active_connections']} active, "
          f"{status['available_client_ids']} client IDs available")
    
    # Try to get a connection
    client_id, connection = pool.get_connection()
    
    if connection:
        print(f"Got connection with client ID {client_id}")
        
        # Use the connection briefly
        time.sleep(2)
        
        # Release the connection
        pool.release_connection(client_id)
        print(f"Released connection {client_id}")
    else:
        print("Failed to get connection from pool")


if __name__ == "__main__":
    """
    Main execution block for remote connection demonstrations.
    """
    print("TWS API Remote Connection Examples")
    print("=" * 40)
    
    # Demonstrate basic remote connection
    demonstrate_remote_connection()
    
    # Demonstrate connection pooling
    demonstrate_connection_pool()
