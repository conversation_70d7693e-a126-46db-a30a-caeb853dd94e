"""
Contract Search and Resolution

This module demonstrates how to search for contracts and resolve ambiguities
when multiple contracts match the same description. It provides tools for
finding the correct contract when dealing with similar instruments.

Key Topics Covered:
- Searching for contracts using partial information
- Handling multiple contract matches
- Resolving contract ambiguities
- Finding contracts by various criteria
- Contract filtering and selection
- Best practices for contract discovery

Contract search is essential when you need to find specific instruments
without knowing their exact contract specifications or when dealing with
instruments that have multiple variants.
"""

import threading
import time
import logging
from typing import Dict, List, Optional, Callable, Any
from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract, ContractDetails


class ContractSearchApp(EClient, EWrapper):
    """
    TWS API application for contract search and resolution.
    
    This class provides methods for searching contracts, handling multiple
    matches, and resolving ambiguities in contract specifications.
    
    Attributes:
        search_results (dict): Storage for contract search results
        active_searches (set): Set of active search request IDs
        search_callbacks (dict): Callbacks for search events
    """
    
    def __init__(self):
        """Initialize the contract search application."""
        EClient.__init__(self, self)
        
        # Data storage
        self.search_results: Dict[int, List[ContractDetails]] = {}
        
        # Request management
        self.active_searches: set = set()
        
        # Callback management
        self.search_callbacks = {
            'contract_found': [],
            'search_complete': [],
            'multiple_matches': [],
            'no_matches': []
        }
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def connectAck(self):
        """Handle successful connection."""
        self.logger.info("Connected to TWS - Contract Search service ready")
    
    def nextValidId(self, orderId):
        """
        Receive next valid order ID.
        
        Args:
            orderId (int): Next valid order ID
        """
        self.logger.info(f"Connection ready. Next valid order ID: {orderId}")
    
    def contractDetails(self, reqId: int, contractDetails: ContractDetails):
        """
        Receive contract details from search requests.
        
        Args:
            reqId (int): Request ID for the search
            contractDetails (ContractDetails): Found contract details
        """
        # Initialize data structure if needed
        if reqId not in self.search_results:
            self.search_results[reqId] = []
        
        # Store the found contract
        self.search_results[reqId].append(contractDetails)
        
        contract = contractDetails.contract
        self.logger.info(f"Contract Found - ReqId: {reqId}, "
                        f"Symbol: {contract.symbol}, ConId: {contract.conId}, "
                        f"Exchange: {contract.exchange}")
        
        # Trigger callbacks
        for callback in self.search_callbacks['contract_found']:
            try:
                callback(reqId, contractDetails)
            except Exception as e:
                self.logger.error(f"Contract found callback error: {e}")
    
    def contractDetailsEnd(self, reqId: int):
        """
        Handle end of contract search.
        
        Args:
            reqId (int): Request ID for the completed search
        """
        self.active_searches.discard(reqId)
        
        # Analyze search results
        results = self.search_results.get(reqId, [])
        result_count = len(results)
        
        self.logger.info(f"Contract Search Complete - ReqId: {reqId}, "
                        f"Found: {result_count} contracts")
        
        # Trigger appropriate callbacks based on results
        if result_count == 0:
            for callback in self.search_callbacks['no_matches']:
                try:
                    callback(reqId)
                except Exception as e:
                    self.logger.error(f"No matches callback error: {e}")
        
        elif result_count > 1:
            for callback in self.search_callbacks['multiple_matches']:
                try:
                    callback(reqId, results)
                except Exception as e:
                    self.logger.error(f"Multiple matches callback error: {e}")
        
        # Always trigger search complete
        for callback in self.search_callbacks['search_complete']:
            try:
                callback(reqId, results)
            except Exception as e:
                self.logger.error(f"Search complete callback error: {e}")
    
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors related to contract searches.
        
        Args:
            reqId (int): Request ID
            errorCode (int): Error code
            errorString (str): Error description
            advancedOrderRejectJson (str): Additional error details
        """
        self.logger.error(f"Error {reqId} {errorCode} {errorString}")
        
        if reqId in self.active_searches:
            self.active_searches.discard(reqId)
    
    def search_contracts(self, req_id: int, search_contract: Contract) -> bool:
        """
        Search for contracts matching the given criteria.
        
        Args:
            req_id (int): Unique request identifier
            search_contract (Contract): Contract with search criteria
            
        Returns:
            bool: True if search was initiated successfully
        """
        if req_id in self.active_searches:
            self.logger.error(f"Request ID {req_id} already has an active search")
            return False
        
        try:
            self.reqContractDetails(req_id, search_contract)
            self.active_searches.add(req_id)
            
            self.logger.info(f"Contract search initiated - ReqId: {req_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initiate contract search: {e}")
            return False
    
    def search_by_symbol(self, req_id: int, symbol: str, sec_type: str = "", 
                        exchange: str = "", currency: str = "") -> bool:
        """
        Search for contracts by symbol with optional filters.
        
        Args:
            req_id (int): Request identifier
            symbol (str): Symbol to search for
            sec_type (str): Security type filter (optional)
            exchange (str): Exchange filter (optional)
            currency (str): Currency filter (optional)
            
        Returns:
            bool: True if search was initiated successfully
        """
        search_contract = Contract()
        search_contract.symbol = symbol
        
        if sec_type:
            search_contract.secType = sec_type
        if exchange:
            search_contract.exchange = exchange
        if currency:
            search_contract.currency = currency
        
        return self.search_contracts(req_id, search_contract)
    
    def search_options_chain(self, req_id: int, underlying_symbol: str, 
                           exchange: str = "SMART", currency: str = "USD") -> bool:
        """
        Search for all options for an underlying symbol.
        
        Args:
            req_id (int): Request identifier
            underlying_symbol (str): Underlying symbol
            exchange (str): Exchange (default: "SMART")
            currency (str): Currency (default: "USD")
            
        Returns:
            bool: True if search was initiated successfully
        """
        search_contract = Contract()
        search_contract.symbol = underlying_symbol
        search_contract.secType = "OPT"
        search_contract.exchange = exchange
        search_contract.currency = currency
        
        return self.search_contracts(req_id, search_contract)
    
    def search_futures_chain(self, req_id: int, symbol: str, exchange: str) -> bool:
        """
        Search for all futures contracts for a symbol.
        
        Args:
            req_id (int): Request identifier
            symbol (str): Futures symbol
            exchange (str): Exchange
            
        Returns:
            bool: True if search was initiated successfully
        """
        search_contract = Contract()
        search_contract.symbol = symbol
        search_contract.secType = "FUT"
        search_contract.exchange = exchange
        
        return self.search_contracts(req_id, search_contract)
    
    def get_search_results(self, req_id: int) -> List[ContractDetails]:
        """
        Get search results for a specific request.
        
        Args:
            req_id (int): Request identifier
            
        Returns:
            list: List of found contract details
        """
        return self.search_results.get(req_id, [])
    
    def filter_results_by_exchange(self, req_id: int, exchange: str) -> List[ContractDetails]:
        """
        Filter search results by exchange.
        
        Args:
            req_id (int): Request identifier
            exchange (str): Exchange to filter by
            
        Returns:
            list: Filtered contract details
        """
        results = self.get_search_results(req_id)
        return [details for details in results 
                if details.contract.exchange == exchange]
    
    def filter_results_by_currency(self, req_id: int, currency: str) -> List[ContractDetails]:
        """
        Filter search results by currency.
        
        Args:
            req_id (int): Request identifier
            currency (str): Currency to filter by
            
        Returns:
            list: Filtered contract details
        """
        results = self.get_search_results(req_id)
        return [details for details in results 
                if details.contract.currency == currency]
    
    def find_most_liquid_contract(self, req_id: int) -> Optional[ContractDetails]:
        """
        Find the most liquid contract from search results.
        
        This is a heuristic based on primary exchange and common patterns.
        
        Args:
            req_id (int): Request identifier
            
        Returns:
            ContractDetails: Most liquid contract or None
        """
        results = self.get_search_results(req_id)
        if not results:
            return None
        
        # Prioritize by exchange preference
        exchange_priority = ['SMART', 'NYSE', 'NASDAQ', 'ARCA', 'BATS']
        
        for preferred_exchange in exchange_priority:
            for details in results:
                if details.contract.exchange == preferred_exchange:
                    return details
        
        # If no preferred exchange found, return first result
        return results[0]
    
    def resolve_ambiguity(self, req_id: int, selection_criteria: Dict[str, Any]) -> Optional[ContractDetails]:
        """
        Resolve contract ambiguity using selection criteria.
        
        Args:
            req_id (int): Request identifier
            selection_criteria (dict): Criteria for selection
            
        Returns:
            ContractDetails: Selected contract or None
        """
        results = self.get_search_results(req_id)
        if not results:
            return None
        
        # Apply selection criteria
        filtered_results = results
        
        for criterion, value in selection_criteria.items():
            if criterion == 'exchange':
                filtered_results = [r for r in filtered_results 
                                  if r.contract.exchange == value]
            elif criterion == 'currency':
                filtered_results = [r for r in filtered_results 
                                  if r.contract.currency == value]
            elif criterion == 'primaryExchange':
                filtered_results = [r for r in filtered_results 
                                  if getattr(r.contract, 'primaryExchange', '') == value]
            elif criterion == 'secType':
                filtered_results = [r for r in filtered_results 
                                  if r.contract.secType == value]
        
        return filtered_results[0] if filtered_results else None
    
    def get_search_summary(self, req_id: int) -> Dict[str, Any]:
        """
        Get summary of search results.
        
        Args:
            req_id (int): Request identifier
            
        Returns:
            dict: Search summary
        """
        results = self.get_search_results(req_id)
        
        summary = {
            'total_results': len(results),
            'security_types': set(),
            'exchanges': set(),
            'currencies': set(),
            'contract_ids': []
        }
        
        for details in results:
            contract = details.contract
            summary['security_types'].add(contract.secType)
            summary['exchanges'].add(contract.exchange)
            summary['currencies'].add(contract.currency)
            summary['contract_ids'].append(contract.conId)
        
        # Convert sets to lists
        summary['security_types'] = list(summary['security_types'])
        summary['exchanges'] = list(summary['exchanges'])
        summary['currencies'] = list(summary['currencies'])
        
        return summary
    
    def add_search_callback(self, event_type: str, callback: Callable):
        """
        Add a callback for search events.
        
        Args:
            event_type (str): Type of event
            callback: Callback function to add
        """
        if event_type in self.search_callbacks:
            self.search_callbacks[event_type].append(callback)


def demonstrate_contract_search():
    """
    Demonstrate contract search functionality with examples.
    """
    print("=== Contract Search Demo ===")
    
    app = ContractSearchApp()
    
    # Add callbacks for demonstration
    def on_contract_found(req_id, contract_details):
        contract = contract_details.contract
        print(f"Found: {contract.symbol} ({contract.secType}) "
              f"ConId: {contract.conId} Exchange: {contract.exchange}")
    
    def on_multiple_matches(req_id, results):
        print(f"Multiple matches found for request {req_id}: {len(results)} contracts")
    
    def on_no_matches(req_id):
        print(f"No matches found for request {req_id}")
    
    def on_search_complete(req_id, results):
        print(f"Search complete for request {req_id}: {len(results)} total results")
    
    app.add_search_callback('contract_found', on_contract_found)
    app.add_search_callback('multiple_matches', on_multiple_matches)
    app.add_search_callback('no_matches', on_no_matches)
    app.add_search_callback('search_complete', on_search_complete)
    
    try:
        # Connect to TWS
        app.connect("127.0.0.1", 7497, 0)
        
        # Start processing thread
        api_thread = threading.Thread(target=app.run, daemon=True)
        api_thread.start()
        
        # Wait for connection
        time.sleep(3)
        
        if app.isConnected():
            print("Connected - starting contract searches")
            
            # Search for Apple stock (should find multiple matches)
            if app.search_by_symbol(9001, "AAPL", "STK"):
                print("Searching for AAPL stock...")
            
            # Search for Apple options chain
            if app.search_options_chain(9002, "AAPL"):
                print("Searching for AAPL options...")
            
            # Search for ES futures
            if app.search_futures_chain(9003, "ES", "CME"):
                print("Searching for ES futures...")
            
            # Wait for results
            time.sleep(10)
            
            # Analyze results
            print("\n--- Search Results Analysis ---")
            
            # AAPL stock results
            aapl_results = app.get_search_results(9001)
            if aapl_results:
                print(f"AAPL Stock: {len(aapl_results)} results")
                most_liquid = app.find_most_liquid_contract(9001)
                if most_liquid:
                    print(f"  Most liquid: {most_liquid.contract.exchange}")
                
                # Resolve ambiguity
                resolved = app.resolve_ambiguity(9001, {'exchange': 'SMART'})
                if resolved:
                    print(f"  Resolved to: ConId {resolved.contract.conId}")
            
            # Options results summary
            options_summary = app.get_search_summary(9002)
            print(f"AAPL Options: {options_summary}")
            
            # Futures results summary
            futures_summary = app.get_search_summary(9003)
            print(f"ES Futures: {futures_summary}")
        
        else:
            print("Failed to connect to TWS")
    
    except KeyboardInterrupt:
        print("\nShutting down...")
    
    finally:
        app.disconnect()


if __name__ == "__main__":
    """
    Main execution block for contract search demonstrations.
    """
    print("TWS API Contract Search Examples")
    print("=" * 40)
    
    # Run the demonstration
    demonstrate_contract_search()
