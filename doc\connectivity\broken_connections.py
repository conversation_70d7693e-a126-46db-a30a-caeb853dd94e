"""
Broken Connection Recovery and Resilience

This module demonstrates how to handle broken TWS API connections and implement
robust recovery mechanisms. It covers connection monitoring, automatic reconnection,
error handling, and maintaining application state during connection disruptions.

Key Topics Covered:
- Connection failure detection and diagnosis
- Automatic reconnection with exponential backoff
- State preservation during disconnections
- Subscription recovery after reconnection
- Connection health monitoring and alerting
- Performance optimization for resilient connections
- Best practices for production environments

Robust connection handling is critical for production trading applications
that must maintain reliability even during network disruptions.
"""

import threading
import time
import logging
import queue
from enum import Enum
from typing import Dict, List, Optional, Callable
from ibapi.client import EClient
from ibapi.wrapper import EWrapper


class ConnectionState(Enum):
    """Enumeration of connection states."""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    FAILED = "failed"


class ResilientIBApp(EClient, EWrapper):
    """
    Resilient TWS API application with automatic recovery capabilities.
    
    This class implements comprehensive connection recovery mechanisms including
    automatic reconnection, state preservation, and subscription recovery.
    
    Attributes:
        connection_state (ConnectionState): Current connection state
        connection_params (dict): Connection parameters for reconnection
        reconnect_attempts (int): Current number of reconnection attempts
        max_reconnect_attempts (int): Maximum reconnection attempts
        reconnect_delay (float): Current reconnection delay
        subscriptions (dict): Active subscriptions for recovery
        recovery_callbacks (list): Callbacks for recovery events
    """
    
    def __init__(self):
        """Initialize the resilient TWS API application."""
        EClient.__init__(self, self)
        
        # Connection state management
        self.connection_state = ConnectionState.DISCONNECTED
        self.connection_params = {}
        
        # Reconnection configuration
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 10
        self.reconnect_delay = 2.0  # Initial delay in seconds
        self.max_reconnect_delay = 300.0  # Maximum delay (5 minutes)
        self.backoff_multiplier = 1.5
        
        # State preservation
        self.subscriptions = {}
        self.pending_requests = {}
        self.last_valid_order_id = None
        
        # Recovery and monitoring
        self.recovery_callbacks = []
        self.connection_history = []
        self.error_history = []
        
        # Performance metrics
        self.performance_metrics = {
            'total_disconnections': 0,
            'successful_reconnections': 0,
            'failed_reconnections': 0,
            'total_downtime': 0,
            'average_reconnection_time': 0
        }
        
        # Threading
        self.reconnection_thread = None
        self.monitoring_active = False
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def connect_with_recovery(self, host: str, port: int, clientId: int) -> bool:
        """
        Connect to TWS with automatic recovery capabilities.
        
        Args:
            host (str): TWS host address
            port (int): TWS port number
            clientId (int): Client ID for the connection
            
        Returns:
            bool: True if initial connection successful
        """
        self.connection_params = {
            'host': host,
            'port': port,
            'clientId': clientId
        }
        
        return self._attempt_connection()
    
    def _attempt_connection(self) -> bool:
        """
        Attempt to establish connection with error handling.
        
        Returns:
            bool: True if connection successful
        """
        if self.connection_state == ConnectionState.CONNECTING:
            return False  # Already attempting connection
        
        self.connection_state = ConnectionState.CONNECTING
        connection_start_time = time.time()
        
        try:
            self.logger.info(f"Attempting connection (attempt {self.reconnect_attempts + 1})")
            
            # Attempt the connection
            self.connect(
                self.connection_params['host'],
                self.connection_params['port'],
                self.connection_params['clientId']
            )
            
            # Start message processing if not already running
            if not hasattr(self, '_processing_thread') or not self._processing_thread.is_alive():
                self._processing_thread = threading.Thread(target=self.run, daemon=True)
                self._processing_thread.start()
            
            # Wait for connection confirmation
            timeout = 15  # seconds
            start_time = time.time()
            
            while (self.connection_state == ConnectionState.CONNECTING and 
                   time.time() - start_time < timeout):
                time.sleep(0.1)
            
            if self.connection_state == ConnectionState.CONNECTED:
                connection_time = time.time() - connection_start_time
                self._handle_successful_connection(connection_time)
                return True
            else:
                self.logger.warning("Connection attempt timed out")
                return self._handle_connection_failure()
                
        except Exception as e:
            self.logger.error(f"Connection attempt failed: {e}")
            return self._handle_connection_failure()
    
    def _handle_successful_connection(self, connection_time: float):
        """
        Handle successful connection establishment.
        
        Args:
            connection_time (float): Time taken to establish connection
        """
        self.logger.info("Connection established successfully")
        
        # Update metrics
        if self.reconnect_attempts > 0:
            self.performance_metrics['successful_reconnections'] += 1
            
            # Update average reconnection time
            current_avg = self.performance_metrics['average_reconnection_time']
            successful_count = self.performance_metrics['successful_reconnections']
            
            new_avg = ((current_avg * (successful_count - 1)) + connection_time) / successful_count
            self.performance_metrics['average_reconnection_time'] = new_avg
        
        # Reset reconnection parameters
        self.reconnect_attempts = 0
        self.reconnect_delay = 2.0
        
        # Record connection event
        self.connection_history.append({
            'event': 'connected',
            'timestamp': time.time(),
            'attempt_number': self.reconnect_attempts
        })
        
        # Start connection monitoring
        self._start_connection_monitoring()
        
        # Trigger recovery callbacks
        for callback in self.recovery_callbacks:
            try:
                callback('connected', self)
            except Exception as e:
                self.logger.error(f"Recovery callback error: {e}")
    
    def _handle_connection_failure(self) -> bool:
        """
        Handle connection failure and initiate reconnection if appropriate.
        
        Returns:
            bool: True if reconnection will be attempted
        """
        self.connection_state = ConnectionState.DISCONNECTED
        self.reconnect_attempts += 1
        
        # Record failure
        self.performance_metrics['failed_reconnections'] += 1
        
        if self.reconnect_attempts <= self.max_reconnect_attempts:
            self.logger.info(f"Scheduling reconnection attempt {self.reconnect_attempts} "
                           f"in {self.reconnect_delay} seconds")
            
            # Schedule reconnection
            self._schedule_reconnection()
            return True
        else:
            self.logger.error("Maximum reconnection attempts exceeded")
            self.connection_state = ConnectionState.FAILED
            
            # Trigger failure callbacks
            for callback in self.recovery_callbacks:
                try:
                    callback('failed', self)
                except Exception as e:
                    self.logger.error(f"Failure callback error: {e}")
            
            return False
    
    def _schedule_reconnection(self):
        """Schedule automatic reconnection attempt."""
        if self.reconnection_thread and self.reconnection_thread.is_alive():
            return  # Reconnection already scheduled
        
        def reconnection_worker():
            time.sleep(self.reconnect_delay)
            
            if self.connection_state == ConnectionState.DISCONNECTED:
                self.connection_state = ConnectionState.RECONNECTING
                self._attempt_connection()
        
        self.reconnection_thread = threading.Thread(target=reconnection_worker, daemon=True)
        self.reconnection_thread.start()
        
        # Increase delay for next attempt (exponential backoff)
        self.reconnect_delay = min(
            self.reconnect_delay * self.backoff_multiplier,
            self.max_reconnect_delay
        )
    
    def connectAck(self):
        """Handle connection acknowledgment from TWS."""
        self.connection_state = ConnectionState.CONNECTED
        self.logger.info("Connection acknowledged by TWS")
    
    def connectionClosed(self):
        """Handle connection closure."""
        if self.connection_state == ConnectionState.CONNECTED:
            self.performance_metrics['total_disconnections'] += 1
            
            # Record disconnection time for downtime calculation
            self.connection_history.append({
                'event': 'disconnected',
                'timestamp': time.time(),
                'reason': 'connection_closed'
            })
        
        self.connection_state = ConnectionState.DISCONNECTED
        self.logger.warning("Connection closed by TWS")
        
        # Stop monitoring
        self.monitoring_active = False
        
        # Initiate reconnection
        self._handle_connection_failure()
    
    def nextValidId(self, orderId):
        """
        Receive next valid order ID and restore subscriptions.
        
        Args:
            orderId (int): Next valid order ID from TWS
        """
        self.last_valid_order_id = orderId
        self.logger.info(f"Next valid order ID: {orderId}")
        
        # Connection is fully ready - restore subscriptions
        self._restore_subscriptions()
    
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors with connection recovery logic.
        
        Args:
            reqId (int): Request ID
            errorCode (int): Error code
            errorString (str): Error description
            advancedOrderRejectJson (str): Additional error details
        """
        # Record error
        self.error_history.append({
            'timestamp': time.time(),
            'reqId': reqId,
            'errorCode': errorCode,
            'errorString': errorString
        })
        
        self.logger.error(f"Error {reqId} {errorCode} {errorString}")
        
        # Handle connection-related errors
        if errorCode in [502, 504]:  # Connection failed/lost
            self.connection_state = ConnectionState.DISCONNECTED
            self._handle_connection_failure()
            
        elif errorCode == 1100:  # Connectivity lost
            self.connection_state = ConnectionState.DISCONNECTED
            self.logger.warning("Connectivity between IB and TWS lost")
            self._handle_connection_failure()
            
        elif errorCode in [1101, 1102]:  # Connectivity restored
            self.connection_state = ConnectionState.CONNECTED
            self.logger.info("Connectivity restored")
            
            if errorCode == 1101:  # Data lost, need to resubscribe
                self._restore_subscriptions()
    
    def store_subscription(self, req_id: int, subscription_type: str, params: tuple):
        """
        Store a subscription for recovery after reconnection.
        
        Args:
            req_id (int): Request ID
            subscription_type (str): Type of subscription
            params (tuple): Parameters for the subscription
        """
        self.subscriptions[req_id] = {
            'type': subscription_type,
            'params': params,
            'timestamp': time.time()
        }
        
        self.logger.debug(f"Stored subscription {req_id} ({subscription_type})")
    
    def _restore_subscriptions(self):
        """Restore all stored subscriptions after reconnection."""
        if not self.subscriptions:
            return
        
        self.logger.info(f"Restoring {len(self.subscriptions)} subscriptions")
        
        for req_id, subscription in self.subscriptions.items():
            try:
                sub_type = subscription['type']
                params = subscription['params']
                
                if sub_type == 'market_data':
                    self.reqMktData(req_id, *params)
                elif sub_type == 'account_updates':
                    self.reqAccountUpdates(*params)
                elif sub_type == 'positions':
                    self.reqPositions()
                # Add more subscription types as needed
                
                self.logger.debug(f"Restored subscription {req_id} ({sub_type})")
                
            except Exception as e:
                self.logger.error(f"Failed to restore subscription {req_id}: {e}")
    
    def _start_connection_monitoring(self):
        """Start connection health monitoring."""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        
        def monitoring_loop():
            last_heartbeat = time.time()
            heartbeat_interval = 30  # seconds
            
            while self.monitoring_active and self.connection_state == ConnectionState.CONNECTED:
                try:
                    current_time = time.time()
                    
                    # Send periodic heartbeat
                    if current_time - last_heartbeat > heartbeat_interval:
                        self.reqCurrentTime()
                        last_heartbeat = current_time
                    
                    time.sleep(5)  # Check every 5 seconds
                    
                except Exception as e:
                    self.logger.error(f"Monitoring error: {e}")
                    break
        
        monitor_thread = threading.Thread(target=monitoring_loop, daemon=True)
        monitor_thread.start()
    
    def add_recovery_callback(self, callback: Callable):
        """
        Add a callback for recovery events.
        
        Args:
            callback (Callable): Function to call on recovery events
                Signature: callback(event_type: str, app: ResilientIBApp)
        """
        self.recovery_callbacks.append(callback)
    
    def get_connection_metrics(self) -> dict:
        """
        Get comprehensive connection metrics and statistics.
        
        Returns:
            dict: Connection metrics and performance data
        """
        metrics = self.performance_metrics.copy()
        
        # Calculate total downtime
        total_downtime = 0
        last_disconnect_time = None
        
        for event in self.connection_history:
            if event['event'] == 'disconnected':
                last_disconnect_time = event['timestamp']
            elif event['event'] == 'connected' and last_disconnect_time:
                downtime = event['timestamp'] - last_disconnect_time
                total_downtime += downtime
                last_disconnect_time = None
        
        metrics['total_downtime'] = total_downtime
        metrics['current_state'] = self.connection_state.value
        metrics['reconnect_attempts'] = self.reconnect_attempts
        metrics['active_subscriptions'] = len(self.subscriptions)
        metrics['recent_errors'] = len([e for e in self.error_history 
                                      if time.time() - e['timestamp'] < 3600])  # Last hour
        
        return metrics


def demonstrate_connection_recovery():
    """
    Demonstrate connection recovery and resilience mechanisms.
    """
    print("=== Connection Recovery Demo ===")
    
    app = ResilientIBApp()
    
    # Add recovery callback
    def recovery_callback(event_type, app_instance):
        print(f"Recovery Event: {event_type}")
        if event_type == 'connected':
            print("Application recovered successfully!")
        elif event_type == 'failed':
            print("Recovery failed - manual intervention required")
    
    app.add_recovery_callback(recovery_callback)
    
    try:
        # Attempt connection with recovery
        if app.connect_with_recovery("127.0.0.1", 7497, 0):
            print("Initial connection successful")
            
            # Store some example subscriptions
            app.store_subscription(1001, 'market_data', ('AAPL', '', False, False, []))
            app.store_subscription(1002, 'account_updates', (True, 'DU123456'))
            
            # Monitor for a while
            print("Monitoring connection for 10 seconds...")
            time.sleep(10)
            
            # Show connection metrics
            metrics = app.get_connection_metrics()
            print("\nConnection Metrics:")
            for key, value in metrics.items():
                print(f"  {key}: {value}")
        
        else:
            print("Initial connection failed")
    
    except KeyboardInterrupt:
        print("\nShutting down...")
    
    finally:
        app.disconnect()


if __name__ == "__main__":
    """
    Main execution block for connection recovery demonstrations.
    """
    print("TWS API Connection Recovery and Resilience Examples")
    print("=" * 55)
    
    # Run the connection recovery demonstration
    demonstrate_connection_recovery()
