"""
Contract Details Retrieval and Processing

This module demonstrates how to request and receive detailed contract
information from the TWS API. Contract details provide comprehensive
information about financial instruments including specifications,
trading hours, and market data permissions.

Key Topics Covered:
- Requesting contract details for single and multiple contracts
- Handling contract details responses
- Processing bond contract details
- Understanding contract details fields and their meanings
- Working with trading hours and time zones
- Managing contract details subscriptions
- Best practices for contract information retrieval

Contract details are essential for understanding instrument specifications,
trading schedules, and market data availability.
"""

import threading
import time
import logging
from typing import Dict, List, Optional, Callable
from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract
from ibapi.contract import ContractDetails


class ContractDetailsApp(EClient, EWrapper):
    """
    TWS API application for contract details retrieval.
    
    This class demonstrates how to request, receive, and manage contract
    details with comprehensive data storage and processing capabilities.
    
    Attributes:
        contract_details_data (dict): Storage for contract details responses
        bond_details_data (dict): Storage for bond contract details
        active_requests (set): Set of active contract details request IDs
        details_callbacks (dict): Callbacks for contract details events
    """
    
    def __init__(self):
        """Initialize the contract details application."""
        EClient.__init__(self, self)
        
        # Data storage
        self.contract_details_data: Dict[int, List[ContractDetails]] = {}
        self.bond_details_data: Dict[int, List[ContractDetails]] = {}
        
        # Request management
        self.active_requests: set = set()
        
        # Callback management
        self.details_callbacks = {
            'contract_details_received': [],
            'contract_details_end': [],
            'bond_details_received': [],
            'bond_details_end': []
        }
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def connectAck(self):
        """Handle successful connection."""
        self.logger.info("Connected to TWS - Contract Details service ready")
    
    def nextValidId(self, orderId):
        """
        Receive next valid order ID.
        
        Args:
            orderId (int): Next valid order ID
        """
        self.logger.info(f"Connection ready. Next valid order ID: {orderId}")
    
    def contractDetails(self, reqId: int, contractDetails: ContractDetails):
        """
        Receive contract details for a specific request.
        
        This method is called for each contract that matches the requested
        contract specification, providing detailed information about the instrument.
        
        Args:
            reqId (int): Request ID for the contract details request
            contractDetails (ContractDetails): Detailed contract information
        """
        # Initialize data structure if needed
        if reqId not in self.contract_details_data:
            self.contract_details_data[reqId] = []
        
        # Store the contract details
        self.contract_details_data[reqId].append(contractDetails)
        
        # Extract key information for logging
        contract = contractDetails.contract
        self.logger.info(f"Contract Details - ReqId: {reqId}, "
                        f"Symbol: {contract.symbol}, ConId: {contract.conId}, "
                        f"Exchange: {contract.exchange}, Currency: {contract.currency}")
        
        # Trigger callbacks
        for callback in self.details_callbacks['contract_details_received']:
            try:
                callback(reqId, contractDetails)
            except Exception as e:
                self.logger.error(f"Contract details callback error: {e}")
    
    def contractDetailsEnd(self, reqId: int):
        """
        Handle end of contract details transmission.
        
        Args:
            reqId (int): Request ID for the completed contract details request
        """
        self.logger.info(f"Contract Details End - ReqId: {reqId}")
        
        # Remove from active requests
        self.active_requests.discard(reqId)
        
        # Trigger end callbacks
        for callback in self.details_callbacks['contract_details_end']:
            try:
                callback(reqId)
            except Exception as e:
                self.logger.error(f"Contract details end callback error: {e}")
    
    def bondContractDetails(self, reqId: int, contractDetails: ContractDetails):
        """
        Receive bond contract details.
        
        This method is called specifically for bond contracts, which have
        limited information due to market data license restrictions.
        
        Args:
            reqId (int): Request ID for the bond contract details request
            contractDetails (ContractDetails): Bond contract details
        """
        # Initialize data structure if needed
        if reqId not in self.bond_details_data:
            self.bond_details_data[reqId] = []
        
        # Store the bond contract details
        self.bond_details_data[reqId].append(contractDetails)
        
        # Extract key information for logging
        contract = contractDetails.contract
        self.logger.info(f"Bond Contract Details - ReqId: {reqId}, "
                        f"Symbol: {contract.symbol}, ConId: {contract.conId}")
        
        # Trigger callbacks
        for callback in self.details_callbacks['bond_details_received']:
            try:
                callback(reqId, contractDetails)
            except Exception as e:
                self.logger.error(f"Bond details callback error: {e}")
    
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors related to contract details requests.
        
        Args:
            reqId (int): Request ID
            errorCode (int): Error code
            errorString (str): Error description
            advancedOrderRejectJson (str): Additional error details
        """
        self.logger.error(f"Error {reqId} {errorCode} {errorString}")
        
        # Handle contract details specific errors
        if reqId in self.active_requests:
            self.logger.error(f"Contract details request {reqId} error: {errorString}")
            self.active_requests.discard(reqId)
    
    def request_contract_details(self, req_id: int, contract: Contract) -> bool:
        """
        Request contract details for a specific contract.
        
        Args:
            req_id (int): Unique request identifier
            contract (Contract): Contract to get details for
            
        Returns:
            bool: True if request was sent successfully
        """
        if req_id in self.active_requests:
            self.logger.error(f"Request ID {req_id} already has an active contract details request")
            return False
        
        try:
            self.reqContractDetails(req_id, contract)
            self.active_requests.add(req_id)
            
            self.logger.info(f"Requested contract details - ReqId: {req_id}, "
                           f"Symbol: {contract.symbol}, SecType: {contract.secType}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to request contract details: {e}")
            return False
    
    def get_contract_details(self, req_id: Optional[int] = None) -> Dict:
        """
        Get stored contract details.
        
        Args:
            req_id (int, optional): Specific request ID to retrieve
            
        Returns:
            dict: Contract details data
        """
        if req_id is not None:
            return self.contract_details_data.get(req_id, [])
        else:
            return self.contract_details_data.copy()
    
    def get_bond_details(self, req_id: Optional[int] = None) -> Dict:
        """
        Get stored bond contract details.
        
        Args:
            req_id (int, optional): Specific request ID to retrieve
            
        Returns:
            dict: Bond contract details data
        """
        if req_id is not None:
            return self.bond_details_data.get(req_id, [])
        else:
            return self.bond_details_data.copy()
    
    def find_contract_by_conid(self, con_id: int, req_id: Optional[int] = None) -> Optional[ContractDetails]:
        """
        Find contract details by contract ID.
        
        Args:
            con_id (int): Contract ID to search for
            req_id (int, optional): Specific request ID to search
            
        Returns:
            ContractDetails: Matching contract details or None
        """
        if req_id is not None:
            req_ids = [req_id]
        else:
            req_ids = list(self.contract_details_data.keys())
        
        for rid in req_ids:
            if rid in self.contract_details_data:
                for details in self.contract_details_data[rid]:
                    if details.contract.conId == con_id:
                        return details
        
        return None
    
    def get_contracts_by_symbol(self, symbol: str, req_id: Optional[int] = None) -> List[ContractDetails]:
        """
        Get all contracts matching a symbol.
        
        Args:
            symbol (str): Symbol to search for
            req_id (int, optional): Specific request ID to search
            
        Returns:
            list: List of matching contract details
        """
        matching_contracts = []
        
        if req_id is not None:
            req_ids = [req_id]
        else:
            req_ids = list(self.contract_details_data.keys())
        
        for rid in req_ids:
            if rid in self.contract_details_data:
                for details in self.contract_details_data[rid]:
                    if details.contract.symbol.upper() == symbol.upper():
                        matching_contracts.append(details)
        
        return matching_contracts
    
    def analyze_trading_hours(self, contract_details: ContractDetails) -> Dict:
        """
        Analyze trading hours information from contract details.
        
        Args:
            contract_details (ContractDetails): Contract details to analyze
            
        Returns:
            dict: Parsed trading hours information
        """
        analysis = {
            'time_zone': contract_details.timeZoneId,
            'trading_hours': contract_details.tradingHours,
            'liquid_hours': contract_details.liquidHours,
            'parsed_trading_hours': [],
            'parsed_liquid_hours': []
        }
        
        # Parse trading hours (simplified parsing)
        if contract_details.tradingHours:
            # Trading hours format: "20231201:0930-1600;20231202:0930-1600"
            for day_hours in contract_details.tradingHours.split(';'):
                if ':' in day_hours:
                    date_part, hours_part = day_hours.split(':', 1)
                    analysis['parsed_trading_hours'].append({
                        'date': date_part,
                        'hours': hours_part
                    })
        
        # Parse liquid hours
        if contract_details.liquidHours:
            for day_hours in contract_details.liquidHours.split(';'):
                if ':' in day_hours:
                    date_part, hours_part = day_hours.split(':', 1)
                    analysis['parsed_liquid_hours'].append({
                        'date': date_part,
                        'hours': hours_part
                    })
        
        return analysis
    
    def get_contract_summary(self, req_id: Optional[int] = None) -> Dict:
        """
        Get summary of contract details for a request.
        
        Args:
            req_id (int, optional): Specific request ID to analyze
            
        Returns:
            dict: Summary of contract details
        """
        if req_id is not None:
            req_ids = [req_id]
        else:
            req_ids = list(self.contract_details_data.keys())
        
        summary = {
            'total_contracts': 0,
            'security_types': set(),
            'exchanges': set(),
            'currencies': set(),
            'symbols': set(),
            'contract_ids': []
        }
        
        for rid in req_ids:
            if rid in self.contract_details_data:
                for details in self.contract_details_data[rid]:
                    contract = details.contract
                    summary['total_contracts'] += 1
                    summary['security_types'].add(contract.secType)
                    summary['exchanges'].add(contract.exchange)
                    summary['currencies'].add(contract.currency)
                    summary['symbols'].add(contract.symbol)
                    summary['contract_ids'].append(contract.conId)
        
        # Convert sets to lists for JSON serialization
        summary['security_types'] = list(summary['security_types'])
        summary['exchanges'] = list(summary['exchanges'])
        summary['currencies'] = list(summary['currencies'])
        summary['symbols'] = list(summary['symbols'])
        
        return summary
    
    def add_details_callback(self, event_type: str, callback: Callable):
        """
        Add a callback for contract details events.
        
        Args:
            event_type (str): Type of event ('contract_details_received', etc.)
            callback: Callback function to add
        """
        if event_type in self.details_callbacks:
            self.details_callbacks[event_type].append(callback)


def demonstrate_contract_details():
    """
    Demonstrate contract details functionality with practical examples.
    """
    print("=== Contract Details Demo ===")
    
    app = ContractDetailsApp()
    
    # Add callbacks for demonstration
    def on_contract_details(req_id, contract_details):
        contract = contract_details.contract
        print(f"Contract Details: {contract.symbol} ({contract.secType}) "
              f"ConId: {contract.conId} Exchange: {contract.exchange}")
        
        # Show some key details
        print(f"  Market Name: {contract_details.marketName}")
        print(f"  Min Tick: {contract_details.minTick}")
        print(f"  Time Zone: {contract_details.timeZoneId}")
    
    def on_details_end(req_id):
        print(f"Contract details complete for request {req_id}")
    
    app.add_details_callback('contract_details_received', on_contract_details)
    app.add_details_callback('contract_details_end', on_details_end)
    
    try:
        # Connect to TWS
        app.connect("127.0.0.1", 7497, 0)
        
        # Start processing thread
        api_thread = threading.Thread(target=app.run, daemon=True)
        api_thread.start()
        
        # Wait for connection
        time.sleep(3)
        
        if app.isConnected():
            print("Connected - requesting contract details")
            
            # Create sample contracts for details requests
            from doc.contracts.contract_creation import ContractBuilder
            builder = ContractBuilder()
            
            # Request details for Apple stock
            aapl_contract = builder.create_stock_contract("AAPL")
            if app.request_contract_details(9001, aapl_contract):
                print("AAPL contract details requested")
            
            # Request details for EUR/USD forex
            eurusd_contract = builder.create_forex_contract("EUR", "USD")
            if app.request_contract_details(9002, eurusd_contract):
                print("EUR/USD contract details requested")
            
            # Wait for responses
            time.sleep(10)
            
            # Display summary
            print("\n--- Contract Details Summary ---")
            summary = app.get_contract_summary()
            for key, value in summary.items():
                if isinstance(value, list) and len(value) > 10:
                    print(f"{key}: {len(value)} items")
                else:
                    print(f"{key}: {value}")
            
            # Analyze trading hours for first contract
            details_data = app.get_contract_details(9001)
            if details_data:
                first_details = details_data[0]
                hours_analysis = app.analyze_trading_hours(first_details)
                print(f"\n--- Trading Hours Analysis ---")
                print(f"Time Zone: {hours_analysis['time_zone']}")
                print(f"Trading Hours: {hours_analysis['trading_hours'][:100]}...")
        
        else:
            print("Failed to connect to TWS")
    
    except KeyboardInterrupt:
        print("\nShutting down...")
    
    finally:
        app.disconnect()


if __name__ == "__main__":
    """
    Main execution block for contract details demonstrations.
    """
    print("TWS API Contract Details Examples")
    print("=" * 40)
    
    # Run the demonstration
    demonstrate_contract_details()
