"""
TWS API Bulletins and System Messages Documentation

This module provides comprehensive documentation and examples for receiving
and processing bulletins and system messages from the Interactive Brokers
TWS API. Bulletins provide important system-wide announcements and updates.

The bulletins module covers:
- bulletin_subscriptions: Subscribing to and receiving bulletins
- bulletin_types: Understanding different types of bulletins
- bulletin_processing: Processing and handling bulletin messages
- system_messages: System-wide messages and announcements
- bulletin_filtering: Filtering bulletins by type and importance
- bulletin_storage: Storing and managing bulletin history

Key Concepts:
- Bulletins: System-wide announcements and important messages
- Bulletin Types: Different categories of bulletins (news, system, etc.)
- Message IDs: Unique identifiers for bulletin messages
- Bulletin Subscriptions: Requesting to receive bulletin updates
- System Messages: Critical system announcements
- Bulletin History: Maintaining records of received bulletins

Usage:
    from api.tws_api.doc.bulletins import bulletin_subscriptions
    from api.tws_api.doc.bulletins import bulletin_types
    from api.tws_api.doc.bulletins import bulletin_processing
    # ... import other modules as needed

Important Notes:
- Bulletins provide important system and market information
- Some bulletins may require immediate attention or action
- Bulletin subscriptions are typically automatic upon connection
- System messages may indicate service disruptions or changes
- Bulletin filtering helps manage information flow
"""

# Import all bulletin modules for easy access
from . import bulletin_subscriptions
from . import bulletin_types
from . import bulletin_processing
from . import system_messages
from . import bulletin_filtering
from . import bulletin_storage

__all__ = [
    'bulletin_subscriptions',
    'bulletin_types',
    'bulletin_processing',
    'system_messages',
    'bulletin_filtering',
    'bulletin_storage'
]
