-> GET /sso/Login?forwardTo=22&RL=1&ip2loc=US
<- 200 GET /sso/Login?forwardTo=22&RL=1&ip2loc=US
-> GET /css/bootstrap-5.2.2/bootstrap.min.css
-> GET /css/fontawesome-6.2.0/all.min.css
<- 200 GET /css/bootstrap-5.2.2/bootstrap.min.css
<- 200 GET /css/fontawesome-6.2.0/all.min.css
-> GET /css/reg-am/login.min.css
-> GET /css/ibkr/theme-ibkr-portal.min.css
<- 200 GET /css/reg-am/login.min.css
-> GET /css/bootstrap-switch-3.3.2/bootstrap-switch.min.css
-> GET /scripts/common/js/jquery-3.7.0/jquery.min.js
<- 200 GET /css/ibkr/theme-ibkr-portal.min.css
-> GET /scripts/common/js/bootstrap-5.2.2/bootstrap.bundle.min.js
-> GET /sso/lib/xyz.bundle.min.js
<- 200 GET /scripts/common/js/bootstrap-5.2.2/bootstrap.bundle.min.js
<- 200 GET /scripts/common/js/jquery-3.7.0/jquery.min.js
<- 200 GET /sso/lib/xyz.bundle.min.js
<- 200 GET /css/bootstrap-switch-3.3.2/bootstrap-switch.min.css
-> GET /images/common/logos/ibkr/interactive-brokers.svg
-> GET /fonts/proxima-nova/Proxima-Nova-Regular.woff2
-> GET /fonts/fontawesome-6.2.0/webfonts/fa-brands-400.woff2
-> GET /fonts/fontawesome-6.4.2/webfonts/fa-solid-900.woff2
-> GET /fonts/proxima-nova/Proxima-Nova-Semibold.woff2
-> POST /portal.proxy/v1/gstat/bulletins
<- 200 GET /fonts/proxima-nova/Proxima-Nova-Regular.woff2
-> GET /sso/images/2fa-animated-once.gif
<- 200 GET /fonts/fontawesome-6.2.0/webfonts/fa-brands-400.woff2
<- 204 POST /portal.proxy/v1/gstat/bulletins
<- 200 GET /fonts/fontawesome-6.4.2/webfonts/fa-solid-900.woff2
<- 200 GET /sso/images/2fa-animated-once.gif
<- 200 GET /fonts/proxima-nova/Proxima-Nova-Semibold.woff2
<- 200 GET /images/common/logos/ibkr/interactive-brokers.svg
-> GET /images/web/favicons/home-screen-icon-128x128.png
<- 200 GET /images/web/favicons/home-screen-icon-128x128.png
-> GET /images/common/logos/ibkr/interactive-brokers-inverse.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers-inverse.svg
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> POST /sso/Dispatcher
<- 302 POST /sso/Dispatcher
-> request: /v1/api/sso/validate?gw=1
-> GET /v1/api/sso/validate?gw=1,200|337ms
-> request: /v1/api/one/user
-> request: /v1/api/ssodh/init
-> sso GET https://api.ibkr.com/v1/api/ssodh/init
-> GET /v1/api/one/user,200|315ms
-> request: /v1/api/ssodh/st
-> sso GET https://api.ibkr.com/v1/api/ssodh/st
-> request: /sso/Authenticator?ACTION=PUBLISH_TST&RESP_TYPE=JSON&DEVICE_ID=3c7948a8|1C-5A-3E-51-95-9B
-> sso GET https://api.ibkr.com/sso/Authenticator?ACTION=PUBLISH_TST&RESP_TYPE=JSON&DEVICE_ID=3c7948a8|1C-5A-3E-51-95-9B
<- 200 GET https://api.ibkr.com/sso/Authenticator?ACTION=PUBLISH_TST&RESP_TYPE=JSON&DEVICE_ID=3c7948a8|1C-5A-3E-51-95-9B 0
-> request: /v1/api/iserver/auth/status
-> GET /v1/api/iserver/auth/status,200|79ms
-> request: /v1/api/iserver/auth/ssodh/init
-> POST https://api.ibkr.com/v1/api/iserver/auth/ssodh/init,200|4029ms
-> request: /v1/api/iserver/accounts
-> GET /v1/api/iserver/accounts,200|487ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|84ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|85ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|108ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|93ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|82ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|87ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|186ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|108ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|88ms
-> request: /sso/ping
-> GET /sso/ping,200|561ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|83ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|93ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|100ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|87ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|91ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|87ms
-> GET /sso/Login?forwardTo=22&RL=1&ip2loc=US
<- 200 GET /sso/Login?forwardTo=22&RL=1&ip2loc=US
-> GET /css/bootstrap-5.2.2/bootstrap.min.css
-> GET /css/fontawesome-6.2.0/all.min.css
<- 200 GET /css/fontawesome-6.2.0/all.min.css
<- 200 GET /css/bootstrap-5.2.2/bootstrap.min.css
-> GET /css/bootstrap-switch-3.3.2/bootstrap-switch.min.css
-> GET /css/reg-am/login.min.css
<- 200 GET /css/bootstrap-switch-3.3.2/bootstrap-switch.min.css
-> GET /css/ibkr/theme-ibkr-portal.min.css
<- 200 GET /css/ibkr/theme-ibkr-portal.min.css
-> GET /scripts/common/js/jquery-3.7.0/jquery.min.js
<- 200 GET /css/reg-am/login.min.css
-> GET /scripts/common/js/bootstrap-5.2.2/bootstrap.bundle.min.js
-> GET /sso/lib/xyz.bundle.min.js
<- 200 GET /scripts/common/js/jquery-3.7.0/jquery.min.js
<- 200 GET /scripts/common/js/bootstrap-5.2.2/bootstrap.bundle.min.js
<- 200 GET /sso/lib/xyz.bundle.min.js
-> GET /images/common/logos/ibkr/interactive-brokers.svg
-> GET /fonts/proxima-nova/Proxima-Nova-Regular.woff2
-> GET /fonts/proxima-nova/Proxima-Nova-Semibold.woff2
-> GET /fonts/fontawesome-6.4.2/webfonts/fa-solid-900.woff2
-> GET /fonts/fontawesome-6.2.0/webfonts/fa-brands-400.woff2
-> POST /portal.proxy/v1/gstat/bulletins
<- 200 GET /images/common/logos/ibkr/interactive-brokers.svg
-> GET /sso/images/2fa-animated-once.gif
<- 200 GET /fonts/fontawesome-6.2.0/webfonts/fa-brands-400.woff2
<- 200 GET /fonts/proxima-nova/Proxima-Nova-Regular.woff2
<- 200 GET /fonts/proxima-nova/Proxima-Nova-Semibold.woff2
<- 200 GET /fonts/fontawesome-6.4.2/webfonts/fa-solid-900.woff2
<- 200 GET /sso/images/2fa-animated-once.gif
<- 204 POST /portal.proxy/v1/gstat/bulletins
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> request: /v1/api/tickle
-> GET /v1/api/tickle,401|291ms
-> request: /v1/api/sso/validate?gw=1
-> GET /v1/api/sso/validate?gw=1,401|291ms
-> request: /v1/api/sso/validate?gw=1
-> GET /v1/api/sso/validate?gw=1,401|377ms
-> request: /v1/api/sso/validate?gw=1
-> GET /v1/api/sso/validate?gw=1,401|292ms
-> request: /v1/api/sso/validate?gw=1
-> GET /v1/api/sso/validate?gw=1,401|298ms
-> request: /v1/api/sso/validate?gw=1
-> GET /v1/api/sso/validate?gw=1,401|297ms
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> GET /sso/Login?forwardTo=22&RL=1&ip2loc=US
<- 200 GET /sso/Login?forwardTo=22&RL=1&ip2loc=US
-> GET /css/reg-am/login.min.css
-> GET /css/ibkr/theme-ibkr-portal.min.css
-> GET /scripts/common/js/bootstrap-5.2.2/bootstrap.bundle.min.js
-> GET /scripts/common/js/jquery-3.7.0/jquery.min.js
-> GET /sso/lib/xyz.bundle.min.js
<- 304 GET /scripts/common/js/bootstrap-5.2.2/bootstrap.bundle.min.js
<- 304 GET /scripts/common/js/jquery-3.7.0/jquery.min.js
<- 200 GET /css/reg-am/login.min.css
<- 200 GET /css/ibkr/theme-ibkr-portal.min.css
<- 304 GET /sso/lib/xyz.bundle.min.js
-> GET /fonts/proxima-nova/Proxima-Nova-Regular.woff2
-> GET /fonts/fontawesome-6.4.2/webfonts/fa-solid-900.woff2
<- 304 GET /fonts/proxima-nova/Proxima-Nova-Regular.woff2
<- 304 GET /fonts/fontawesome-6.4.2/webfonts/fa-solid-900.woff2
-> GET /fonts/proxima-nova/Proxima-Nova-Semibold.woff2
-> POST /portal.proxy/v1/gstat/bulletins
<- 304 GET /fonts/proxima-nova/Proxima-Nova-Semibold.woff2
<- 204 POST /portal.proxy/v1/gstat/bulletins
-> GET /images/web/favicons/home-screen-icon-128x128.png
<- 200 GET /images/web/favicons/home-screen-icon-128x128.png
-> GET /images/common/logos/ibkr/interactive-brokers-inverse.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers-inverse.svg
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> POST /sso/Dispatcher
<- 302 POST /sso/Dispatcher
-> request: /v1/api/sso/validate?gw=1
-> GET /v1/api/sso/validate?gw=1,200|90ms
-> request: /v1/api/one/user
-> request: /v1/api/ssodh/init
-> sso GET https://api.ibkr.com/v1/api/ssodh/init
-> GET /v1/api/one/user,200|86ms
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> request: /sso/ping
-> GET /sso/ping,200|284ms
-> request: /sso/ping
-> GET /sso/ping,200|1514ms
-> request: /sso/ping
-> GET /sso/ping,200|486ms
-> request: /sso/ping
-> GET /sso/ping,200|486ms
-> request: /sso/ping
-> request: /sso/ping
-> GET /sso/ping,200|692ms
-> request: /sso/ping
-> GET /sso/ping,200|487ms
-> request: /sso/ping
-> GET /sso/ping,200|487ms
-> request: /sso/ping
-> GET /sso/ping,200|487ms
-> request: /sso/ping
-> GET /sso/ping,200|288ms
-> request: /sso/ping
-> GET /sso/ping,200|485ms
-> request: /sso/ping
-> GET /sso/ping,200|486ms
-> request: /sso/ping
-> GET /sso/ping,200|288ms
-> request: /sso/ping
-> GET /sso/ping,200|487ms
-> request: /sso/ping
-> GET /sso/ping,200|818ms
-> request: /sso/ping
-> GET /sso/ping,200|494ms
-> request: /sso/ping
-> GET /sso/ping,200|488ms
-> request: /sso/ping
-> GET /sso/ping,200|488ms
-> request: /sso/ping
-> GET /sso/ping,200|491ms
-> request: /sso/ping
-> GET /sso/ping,200|486ms
-> request: /sso/ping
-> GET /sso/ping,200|487ms
-> request: /sso/ping
-> GET /sso/ping,200|489ms
-> request: /sso/ping
-> GET /sso/ping,200|488ms
-> request: /sso/ping
-> request: /sso/ping
-> request: /sso/ping
-> request: /sso/ping
-> request: /sso/ping
-> request: /sso/ping
-> request: /sso/ping
-> request: /sso/ping
-> request: /sso/ping
-> request: /sso/ping
-> request: /sso/ping
-> request: /sso/ping
-> request: /sso/ping
-> request: /sso/ping
-> request: /sso/ping
-> request: /sso/ping
-> request: /sso/ping
-> GET /sso/ping,200|608ms
-> GET /sso/ping,200|609ms
-> GET /sso/ping,200|623ms
-> GET /sso/ping,200|715ms
-> GET /sso/ping,200|903ms
-> GET /sso/ping,200|1089ms
-> GET /sso/ping,200|1114ms
-> GET /sso/ping,200|1119ms
-> GET /sso/ping,200|1374ms
-> GET /sso/ping,200|1386ms
-> GET /sso/ping,200|1402ms
-> GET /sso/ping,200|1410ms
-> GET /sso/ping,200|1768ms
-> GET /sso/ping,200|1856ms
-> GET /sso/ping,200|1928ms
-> GET /sso/ping,200|1959ms
-> request: /sso/ping
-> GET /sso/ping,200|479ms
-> request: /sso/ping
-> GET /sso/ping,200|481ms
-> request: /sso/ping
-> GET /sso/ping,200|478ms
-> request: /sso/ping
-> GET /sso/ping,200|479ms
-> request: /sso/ping
-> GET /sso/ping,200|478ms
-> request: /sso/ping
-> GET /sso/ping,200|481ms
-> request: /sso/ping
-> GET /sso/ping,200|593ms
-> request: /sso/ping
-> GET /sso/ping,200|481ms
-> request: /sso/ping
-> GET /sso/ping,200|480ms
-> request: /sso/ping
-> GET /sso/ping,200|477ms
-> request: /sso/ping
-> GET /sso/ping,200|486ms
-> request: /sso/ping
-> GET /sso/ping,200|634ms
-> request: /sso/ping
-> GET /sso/ping,200|488ms
-> request: /sso/ping
-> GET /sso/ping,200|485ms
-> request: /sso/ping
-> GET /sso/ping,200|486ms
-> request: /sso/ping
-> GET /sso/ping,200|481ms
-> request: /sso/ping
-> GET /sso/ping,200|485ms
-> request: /sso/ping
-> GET /sso/ping,200|486ms
-> request: /sso/ping
-> GET /sso/ping,200|487ms
-> request: /sso/ping
-> GET /sso/ping,200|487ms
-> request: /sso/ping
-> GET /sso/ping,200|484ms
-> request: /sso/ping
-> GET /sso/ping,200|482ms
-> request: /sso/ping
-> GET /sso/ping,200|487ms
-> request: /sso/ping
-> GET /sso/ping,200|483ms
-> request: /sso/ping
-> GET /sso/ping,200|484ms
-> request: /sso/ping
-> GET /sso/ping,200|482ms
-> request: /sso/ping
-> GET /sso/ping,200|495ms
-> request: /sso/ping
-> GET /sso/ping,200|481ms
-> request: /sso/ping
-> GET /sso/ping,200|493ms
-> request: /sso/ping
-> GET /sso/ping,200|482ms
-> request: /sso/ping
-> GET /sso/ping,200|487ms
-> request: /sso/ping
-> GET /sso/ping,200|485ms
-> request: /sso/ping
-> GET /sso/ping,200|484ms
-> request: /sso/ping
-> GET /sso/ping,200|483ms
-> request: /sso/ping
-> GET /sso/ping,200|496ms
-> request: /sso/ping
-> GET /sso/ping,200|479ms
-> request: /sso/ping
-> GET /sso/ping,200|483ms
-> request: /sso/ping
-> GET /sso/ping,200|479ms
-> request: /sso/ping
-> GET /sso/ping,200|525ms
-> request: /sso/ping
-> GET /sso/ping,200|498ms
-> request: /sso/ping
-> GET /sso/ping,200|484ms
-> request: /sso/ping
-> GET /sso/ping,200|491ms
