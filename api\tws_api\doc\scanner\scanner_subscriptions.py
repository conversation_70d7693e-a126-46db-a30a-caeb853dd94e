"""
Market Scanner Subscriptions and Management

This module demonstrates how to set up and manage market scanner subscriptions
using the TWS API. Scanner subscriptions provide real-time screening of
securities based on various criteria and filters.

Key Topics Covered:
- Creating scanner subscription requests
- Managing active scanner subscriptions
- Processing scanner data updates
- Understanding scanner parameters and filters
- Handling scanner results and rankings
- Canceling scanner subscriptions
- Best practices for scanner usage

Market scanners are powerful tools for identifying trading opportunities
and monitoring market conditions in real-time.
"""

import threading
import time
import logging
from typing import Dict, List, Optional, Callable
from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.scanner import ScannerSubscription
from ibapi.contract import ContractDetails


class ScannerSubscriptionsApp(EClient, EWrapper):
    """
    TWS API application for market scanner subscriptions.
    
    This class demonstrates how to create, manage, and process market
    scanner subscriptions with comprehensive result handling.
    
    Attributes:
        scanner_subscriptions (dict): Active scanner subscriptions by request ID
        scanner_results (dict): Scanner results storage by request ID
        scanner_callbacks (dict): Callbacks for scanner events
        available_parameters (dict): Available scanner parameters
    """
    
    def __init__(self):
        """Initialize the scanner subscriptions application."""
        EClient.__init__(self, self)
        
        # Data storage
        self.scanner_subscriptions: Dict[int, ScannerSubscription] = {}
        self.scanner_results: Dict[int, List[Dict]] = {}
        self.available_parameters: Dict = {}
        
        # Callback management
        self.scanner_callbacks = {
            'scanner_data': [],
            'scanner_data_end': [],
            'scanner_parameters': []
        }
        
        # Common scanner types
        self.scanner_types = {
            'TOP_PERC_GAIN': 'Top % gainers',
            'TOP_PERC_LOSE': 'Top % losers',
            'MOST_ACTIVE': 'Most active by volume',
            'HOT_BY_VOLUME': 'Hot by volume',
            'TOP_VOLUME_RATE': 'Top volume rate',
            'HOT_BY_PRICE': 'Hot by price',
            'TOP_PRICE_RANGE': 'Top price range',
            'HOT_BY_OPT_VOLUME': 'Hot by option volume',
            'OPT_VOLUME_MOST_ACTIVE': 'Option volume most active',
            'COMBO_MOST_ACTIVE': 'Combo most active',
            'MOST_ACTIVE_USD': 'Most active by USD volume',
            'TOP_OPEN_PERC_GAIN': 'Top open % gain',
            'TOP_OPEN_PERC_LOSE': 'Top open % lose',
            'TOP_OPT_IMP_VOLAT_GAIN': 'Top option implied volatility % gain',
            'TOP_OPT_IMP_VOLAT_LOSE': 'Top option implied volatility % lose',
            'HIGH_OPT_IMP_VOLAT': 'High option implied volatility',
            'LOW_OPT_IMP_VOLAT': 'Low option implied volatility',
            'HIGH_OPT_VOLUME_PUT_CALL_RATIO': 'High option volume put/call ratio',
            'LOW_OPT_VOLUME_PUT_CALL_RATIO': 'Low option volume put/call ratio'
        }
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def connectAck(self):
        """Handle successful connection."""
        self.logger.info("Connected to TWS - Scanner Subscriptions service ready")
    
    def nextValidId(self, orderId):
        """
        Receive next valid order ID.
        
        Args:
            orderId (int): Next valid order ID
        """
        self.logger.info(f"Connection ready. Next valid order ID: {orderId}")
    
    def scannerData(self, reqId: int, rank: int, contractDetails: ContractDetails,
                   distance: str, benchmark: str, projection: str, legsStr: str):
        """
        Receive scanner data results.
        
        Args:
            reqId (int): Request ID for the scanner subscription
            rank (int): Ranking of this result
            contractDetails (ContractDetails): Contract details for the result
            distance (str): Distance value
            benchmark (str): Benchmark value
            projection (str): Projection value
            legsStr (str): Legs string for combos
        """
        # Initialize results storage if needed
        if reqId not in self.scanner_results:
            self.scanner_results[reqId] = []
        
        # Extract contract information
        contract = contractDetails.contract
        
        # Create result entry
        result = {
            'rank': rank,
            'contract_details': contractDetails,
            'contract': contract,
            'symbol': contract.symbol,
            'sec_type': contract.secType,
            'exchange': contract.exchange,
            'currency': contract.currency,
            'con_id': contract.conId,
            'distance': distance,
            'benchmark': benchmark,
            'projection': projection,
            'legs': legsStr,
            'market_name': contractDetails.marketName,
            'trading_class': contractDetails.tradingClass,
            'min_tick': contractDetails.minTick,
            'timestamp': time.time()
        }
        
        # Store the result
        self.scanner_results[reqId].append(result)
        
        self.logger.info(f"Scanner Data - ReqId: {reqId}, Rank: {rank}, "
                        f"Symbol: {contract.symbol}, Exchange: {contract.exchange}")
        
        # Trigger callbacks
        for callback in self.scanner_callbacks['scanner_data']:
            try:
                callback(reqId, rank, contractDetails, distance, benchmark, projection, legsStr)
            except Exception as e:
                self.logger.error(f"Scanner data callback error: {e}")
    
    def scannerDataEnd(self, reqId: int):
        """
        Handle end of scanner data transmission.
        
        Args:
            reqId (int): Request ID for the completed scanner subscription
        """
        result_count = len(self.scanner_results.get(reqId, []))
        self.logger.info(f"Scanner Data End - ReqId: {reqId}, Results: {result_count}")
        
        # Trigger end callbacks
        for callback in self.scanner_callbacks['scanner_data_end']:
            try:
                callback(reqId)
            except Exception as e:
                self.logger.error(f"Scanner data end callback error: {e}")
    
    def scannerParameters(self, xml: str):
        """
        Receive scanner parameters information.
        
        Args:
            xml (str): XML string containing available scanner parameters
        """
        self.logger.info("Scanner parameters received")
        
        # Store the parameters XML (could be parsed for detailed info)
        self.available_parameters['xml'] = xml
        self.available_parameters['received_at'] = time.time()
        
        # Trigger callbacks
        for callback in self.scanner_callbacks['scanner_parameters']:
            try:
                callback(xml)
            except Exception as e:
                self.logger.error(f"Scanner parameters callback error: {e}")
    
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors related to scanner requests.
        
        Args:
            reqId (int): Request ID
            errorCode (int): Error code
            errorString (str): Error description
            advancedOrderRejectJson (str): Additional error details
        """
        self.logger.error(f"Error {reqId} {errorCode} {errorString}")
        
        # Handle scanner-specific errors
        if reqId in self.scanner_subscriptions:
            self.logger.error(f"Scanner subscription {reqId} error: {errorString}")
    
    def request_scanner_parameters(self):
        """Request available scanner parameters from TWS."""
        try:
            self.reqScannerParameters()
            self.logger.info("Requested scanner parameters")
        except Exception as e:
            self.logger.error(f"Failed to request scanner parameters: {e}")
    
    def create_scanner_subscription(self, scan_code: str = "TOP_PERC_GAIN",
                                  instrument: str = "STK", location_code: str = "STK.US.MAJOR",
                                  number_of_rows: int = 20) -> ScannerSubscription:
        """
        Create a scanner subscription with specified parameters.
        
        Args:
            scan_code (str): Type of scan to perform
            instrument (str): Instrument type (STK, OPT, FUT, etc.)
            location_code (str): Market location code
            number_of_rows (int): Maximum number of results
            
        Returns:
            ScannerSubscription: Configured scanner subscription
        """
        subscription = ScannerSubscription()
        subscription.scanCode = scan_code
        subscription.instrument = instrument
        subscription.locationCode = location_code
        subscription.numberOfRows = number_of_rows
        
        self.logger.info(f"Created scanner subscription: {scan_code} for {instrument} "
                        f"in {location_code}, {number_of_rows} rows")
        
        return subscription
    
    def create_filtered_scanner(self, scan_code: str, instrument: str = "STK",
                              location_code: str = "STK.US.MAJOR", number_of_rows: int = 20,
                              above_price: float = None, below_price: float = None,
                              above_volume: int = None, market_cap_above: float = None,
                              market_cap_below: float = None, coupon_rate_above: float = None,
                              coupon_rate_below: float = None, exclude_convertible: bool = False,
                              average_option_volume_above: int = None,
                              scanner_setting_pairs: str = "",
                              stock_type_filter: str = "") -> ScannerSubscription:
        """
        Create a scanner subscription with advanced filters.
        
        Args:
            scan_code (str): Type of scan to perform
            instrument (str): Instrument type
            location_code (str): Market location code
            number_of_rows (int): Maximum number of results
            above_price (float): Minimum price filter
            below_price (float): Maximum price filter
            above_volume (int): Minimum volume filter
            market_cap_above (float): Minimum market cap filter
            market_cap_below (float): Maximum market cap filter
            coupon_rate_above (float): Minimum coupon rate filter
            coupon_rate_below (float): Maximum coupon rate filter
            exclude_convertible (bool): Exclude convertible securities
            average_option_volume_above (int): Minimum average option volume
            scanner_setting_pairs (str): Additional scanner settings
            stock_type_filter (str): Stock type filter
            
        Returns:
            ScannerSubscription: Configured scanner subscription with filters
        """
        subscription = ScannerSubscription()
        subscription.scanCode = scan_code
        subscription.instrument = instrument
        subscription.locationCode = location_code
        subscription.numberOfRows = number_of_rows
        
        # Apply filters
        if above_price is not None:
            subscription.abovePrice = above_price
        if below_price is not None:
            subscription.belowPrice = below_price
        if above_volume is not None:
            subscription.aboveVolume = above_volume
        if market_cap_above is not None:
            subscription.marketCapAbove = market_cap_above
        if market_cap_below is not None:
            subscription.marketCapBelow = market_cap_below
        if coupon_rate_above is not None:
            subscription.couponRateAbove = coupon_rate_above
        if coupon_rate_below is not None:
            subscription.couponRateBelow = coupon_rate_below
        if exclude_convertible:
            subscription.excludeConvertible = exclude_convertible
        if average_option_volume_above is not None:
            subscription.averageOptionVolumeAbove = average_option_volume_above
        if scanner_setting_pairs:
            subscription.scannerSettingPairs = scanner_setting_pairs
        if stock_type_filter:
            subscription.stockTypeFilter = stock_type_filter
        
        filter_desc = []
        if above_price: filter_desc.append(f"price>${above_price}")
        if below_price: filter_desc.append(f"price<${below_price}")
        if above_volume: filter_desc.append(f"volume>{above_volume:,}")
        
        self.logger.info(f"Created filtered scanner: {scan_code} with filters: {', '.join(filter_desc)}")
        
        return subscription
    
    def start_scanner_subscription(self, req_id: int, subscription: ScannerSubscription,
                                 scanner_subscription_options: List = None,
                                 scanner_subscription_filter_options: List = None) -> bool:
        """
        Start a scanner subscription.
        
        Args:
            req_id (int): Unique request identifier
            subscription (ScannerSubscription): Scanner subscription configuration
            scanner_subscription_options (list): Additional subscription options
            scanner_subscription_filter_options (list): Additional filter options
            
        Returns:
            bool: True if subscription was started successfully
        """
        if req_id in self.scanner_subscriptions:
            self.logger.error(f"Request ID {req_id} already has an active scanner subscription")
            return False
        
        try:
            if scanner_subscription_options is None:
                scanner_subscription_options = []
            if scanner_subscription_filter_options is None:
                scanner_subscription_filter_options = []
            
            self.reqScannerSubscription(req_id, subscription, scanner_subscription_options,
                                      scanner_subscription_filter_options)
            
            self.scanner_subscriptions[req_id] = subscription
            
            self.logger.info(f"Started scanner subscription - ReqId: {req_id}, "
                           f"Scan: {subscription.scanCode}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start scanner subscription: {e}")
            return False
    
    def cancel_scanner_subscription(self, req_id: int) -> bool:
        """
        Cancel a scanner subscription.
        
        Args:
            req_id (int): Request ID to cancel
            
        Returns:
            bool: True if cancellation was sent successfully
        """
        if req_id not in self.scanner_subscriptions:
            self.logger.warning(f"No active scanner subscription found for request ID {req_id}")
            return False
        
        try:
            self.cancelScannerSubscription(req_id)
            del self.scanner_subscriptions[req_id]
            
            self.logger.info(f"Canceled scanner subscription - ReqId: {req_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cancel scanner subscription: {e}")
            return False
    
    def get_scanner_results(self, req_id: int) -> List[Dict]:
        """
        Get scanner results for a specific request.
        
        Args:
            req_id (int): Request identifier
            
        Returns:
            list: List of scanner results
        """
        return self.scanner_results.get(req_id, [])
    
    def get_top_results(self, req_id: int, count: int = 10) -> List[Dict]:
        """
        Get top N scanner results.
        
        Args:
            req_id (int): Request identifier
            count (int): Number of top results to return
            
        Returns:
            list: Top scanner results
        """
        results = self.get_scanner_results(req_id)
        return sorted(results, key=lambda x: x['rank'])[:count]
    
    def get_results_by_exchange(self, req_id: int, exchange: str) -> List[Dict]:
        """
        Filter scanner results by exchange.
        
        Args:
            req_id (int): Request identifier
            exchange (str): Exchange to filter by
            
        Returns:
            list: Filtered scanner results
        """
        results = self.get_scanner_results(req_id)
        return [result for result in results if result['exchange'] == exchange]
    
    def get_scanner_summary(self, req_id: int) -> Dict:
        """
        Get summary statistics for scanner results.
        
        Args:
            req_id (int): Request identifier
            
        Returns:
            dict: Scanner results summary
        """
        results = self.get_scanner_results(req_id)
        
        if not results:
            return {'total_results': 0}
        
        exchanges = {}
        currencies = {}
        sec_types = {}
        
        for result in results:
            # Count by exchange
            exchange = result['exchange']
            exchanges[exchange] = exchanges.get(exchange, 0) + 1
            
            # Count by currency
            currency = result['currency']
            currencies[currency] = currencies.get(currency, 0) + 1
            
            # Count by security type
            sec_type = result['sec_type']
            sec_types[sec_type] = sec_types.get(sec_type, 0) + 1
        
        summary = {
            'total_results': len(results),
            'exchanges': exchanges,
            'currencies': currencies,
            'security_types': sec_types,
            'top_symbol': results[0]['symbol'] if results else None,
            'scan_code': self.scanner_subscriptions.get(req_id, {}).scanCode if req_id in self.scanner_subscriptions else None
        }
        
        return summary
    
    def add_scanner_callback(self, event_type: str, callback: Callable):
        """
        Add a callback for scanner events.
        
        Args:
            event_type (str): Type of event
            callback: Callback function to add
        """
        if event_type in self.scanner_callbacks:
            self.scanner_callbacks[event_type].append(callback)


def demonstrate_scanner_subscriptions():
    """
    Demonstrate scanner subscriptions functionality with practical examples.
    """
    print("=== Scanner Subscriptions Demo ===")
    
    app = ScannerSubscriptionsApp()
    
    # Add callbacks for demonstration
    def on_scanner_data(req_id, rank, contract_details, distance, benchmark, projection, legs):
        contract = contract_details.contract
        print(f"Rank {rank}: {contract.symbol} ({contract.exchange}) - {distance}")
    
    def on_scanner_end(req_id):
        results = app.get_scanner_results(req_id)
        print(f"Scanner {req_id} complete: {len(results)} results")
    
    def on_scanner_parameters(xml):
        print(f"Scanner parameters received: {len(xml)} characters")
    
    app.add_scanner_callback('scanner_data', on_scanner_data)
    app.add_scanner_callback('scanner_data_end', on_scanner_end)
    app.add_scanner_callback('scanner_parameters', on_scanner_parameters)
    
    try:
        # Connect to TWS
        app.connect("127.0.0.1", 7497, 0)
        
        # Start processing thread
        api_thread = threading.Thread(target=app.run, daemon=True)
        api_thread.start()
        
        # Wait for connection
        time.sleep(3)
        
        if app.isConnected():
            print("Connected - requesting scanner data")
            
            # Request scanner parameters
            app.request_scanner_parameters()
            time.sleep(2)
            
            # Create and start various scanner subscriptions
            
            # Top % gainers
            gainers_scan = app.create_scanner_subscription("TOP_PERC_GAIN", "STK", "STK.US.MAJOR", 10)
            if app.start_scanner_subscription(9001, gainers_scan):
                print("Started top % gainers scan")
            
            # Most active stocks with price filter
            active_scan = app.create_filtered_scanner("MOST_ACTIVE", "STK", "STK.US.MAJOR", 10,
                                                    above_price=10.0, above_volume=1000000)
            if app.start_scanner_subscription(9002, active_scan):
                print("Started most active scan with filters")
            
            # Top % losers
            losers_scan = app.create_scanner_subscription("TOP_PERC_LOSE", "STK", "STK.US.MAJOR", 10)
            if app.start_scanner_subscription(9003, losers_scan):
                print("Started top % losers scan")
            
            # Wait for scanner results
            print("Waiting for scanner results...")
            time.sleep(15)
            
            # Display scanner summaries
            print("\n--- Scanner Results Summary ---")
            for req_id in [9001, 9002, 9003]:
                summary = app.get_scanner_summary(req_id)
                scan_name = {9001: "Top Gainers", 9002: "Most Active", 9003: "Top Losers"}.get(req_id)
                
                print(f"\n{scan_name} (ReqId: {req_id}):")
                print(f"  Total Results: {summary['total_results']}")
                if summary['total_results'] > 0:
                    print(f"  Top Symbol: {summary['top_symbol']}")
                    print(f"  Exchanges: {list(summary['exchanges'].keys())}")
                    print(f"  Currencies: {list(summary['currencies'].keys())}")
                
                # Show top 3 results
                top_results = app.get_top_results(req_id, 3)
                if top_results:
                    print("  Top 3 Results:")
                    for result in top_results:
                        print(f"    {result['rank']}: {result['symbol']} ({result['exchange']})")
            
            # Cancel all scanner subscriptions
            for req_id in list(app.scanner_subscriptions.keys()):
                app.cancel_scanner_subscription(req_id)
            print("Scanner subscriptions canceled")
        
        else:
            print("Failed to connect to TWS")
    
    except KeyboardInterrupt:
        print("\nShutting down...")
    
    finally:
        # Cancel all active subscriptions
        for req_id in list(app.scanner_subscriptions.keys()):
            app.cancel_scanner_subscription(req_id)
        
        app.disconnect()


if __name__ == "__main__":
    """
    Main execution block for scanner subscriptions demonstrations.
    """
    print("TWS API Scanner Subscriptions Examples")
    print("=" * 45)
    
    # Run the demonstration
    demonstrate_scanner_subscriptions()
