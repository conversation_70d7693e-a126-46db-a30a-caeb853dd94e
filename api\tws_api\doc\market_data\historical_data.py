"""
Historical Market Data Retrieval

This module demonstrates how to request and receive historical market data
from the TWS API. Historical data provides past price and volume information
for analysis, backtesting, and research purposes.

Key Topics Covered:
- Requesting historical bar data with various time frames
- Handling historical data responses and storage
- Understanding bar size and duration parameters
- Working with different data types (TRADES, MIDPOINT, BID, ASK)
- Managing historical data requests and limitations
- Processing large historical datasets
- Best practices for historical data retrieval

Historical data is essential for quantitative analysis, strategy development,
and backtesting trading algorithms.
"""

import threading
import time
import logging
from typing import Dict, List, Optional, Callable
from datetime import datetime, timedelta
from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract
from ibapi.common import BarData


class HistoricalDataApp(EClient, EWrapper):
    """
    TWS API application for historical market data retrieval.
    
    This class demonstrates how to request, receive, and manage historical
    market data with comprehensive data storage and processing capabilities.
    
    Attributes:
        historical_data (dict): Storage for historical bar data by request ID
        active_requests (set): Set of active historical data request IDs
        data_callbacks (dict): Callbacks for historical data events
        request_metadata (dict): Metadata about historical data requests
    """
    
    def __init__(self):
        """Initialize the historical data application."""
        EClient.__init__(self, self)
        
        # Data storage
        self.historical_data: Dict[int, List[BarData]] = {}
        self.request_metadata: Dict[int, Dict] = {}
        
        # Request management
        self.active_requests: set = set()
        
        # Callback management
        self.data_callbacks = {
            'historical_data_received': [],
            'historical_data_end': [],
            'historical_data_update': []
        }
        
        # Valid bar sizes and their descriptions
        self.valid_bar_sizes = {
            '1 secs': '1 second bars',
            '5 secs': '5 second bars',
            '10 secs': '10 second bars',
            '15 secs': '15 second bars',
            '30 secs': '30 second bars',
            '1 min': '1 minute bars',
            '2 mins': '2 minute bars',
            '3 mins': '3 minute bars',
            '5 mins': '5 minute bars',
            '10 mins': '10 minute bars',
            '15 mins': '15 minute bars',
            '20 mins': '20 minute bars',
            '30 mins': '30 minute bars',
            '1 hour': '1 hour bars',
            '2 hours': '2 hour bars',
            '3 hours': '3 hour bars',
            '4 hours': '4 hour bars',
            '8 hours': '8 hour bars',
            '1 day': '1 day bars',
            '1 week': '1 week bars',
            '1 month': '1 month bars'
        }
        
        # Valid what-to-show options
        self.what_to_show_options = [
            'TRADES', 'MIDPOINT', 'BID', 'ASK', 'BID_ASK',
            'HISTORICAL_VOLATILITY', 'OPTION_IMPLIED_VOLATILITY',
            'REBATE_RATE', 'FEE_RATE', 'YIELD_BID', 'YIELD_ASK', 'YIELD_BID_ASK', 'YIELD_LAST'
        ]
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def connectAck(self):
        """Handle successful connection."""
        self.logger.info("Connected to TWS - Historical Data service ready")
    
    def nextValidId(self, orderId):
        """
        Receive next valid order ID.
        
        Args:
            orderId (int): Next valid order ID
        """
        self.logger.info(f"Connection ready. Next valid order ID: {orderId}")
    
    def historicalData(self, reqId: int, bar: BarData):
        """
        Receive historical bar data.
        
        Args:
            reqId (int): Request ID for the historical data request
            bar (BarData): Historical bar data
        """
        # Initialize data structure if needed
        if reqId not in self.historical_data:
            self.historical_data[reqId] = []
        
        # Store the historical bar
        self.historical_data[reqId].append(bar)
        
        self.logger.debug(f"Historical Bar - ReqId: {reqId}, Date: {bar.date}, "
                         f"OHLC: {bar.open}/{bar.high}/{bar.low}/{bar.close}, Volume: {bar.volume}")
        
        # Trigger callbacks
        for callback in self.data_callbacks['historical_data_received']:
            try:
                callback(reqId, bar)
            except Exception as e:
                self.logger.error(f"Historical data callback error: {e}")
    
    def historicalDataEnd(self, reqId: int, start: str, end: str):
        """
        Handle end of historical data transmission.
        
        Args:
            reqId (int): Request ID for the completed historical data request
            start (str): Start date of the data
            end (str): End date of the data
        """
        self.active_requests.discard(reqId)
        
        bar_count = len(self.historical_data.get(reqId, []))
        self.logger.info(f"Historical Data End - ReqId: {reqId}, "
                        f"Bars: {bar_count}, Period: {start} to {end}")
        
        # Update metadata
        if reqId in self.request_metadata:
            self.request_metadata[reqId].update({
                'actual_start': start,
                'actual_end': end,
                'bar_count': bar_count,
                'completed_at': time.time()
            })
        
        # Trigger end callbacks
        for callback in self.data_callbacks['historical_data_end']:
            try:
                callback(reqId, start, end)
            except Exception as e:
                self.logger.error(f"Historical data end callback error: {e}")
    
    def historicalDataUpdate(self, reqId: int, bar: BarData):
        """
        Receive historical data updates (for keep-up-to-date requests).
        
        Args:
            reqId (int): Request ID
            bar (BarData): Updated bar data
        """
        # Update the last bar in the dataset
        if reqId in self.historical_data and self.historical_data[reqId]:
            # Replace the last bar with the updated one
            self.historical_data[reqId][-1] = bar
            
            self.logger.debug(f"Historical Data Update - ReqId: {reqId}, "
                             f"Updated bar: {bar.date}")
            
            # Trigger update callbacks
            for callback in self.data_callbacks['historical_data_update']:
                try:
                    callback(reqId, bar)
                except Exception as e:
                    self.logger.error(f"Historical data update callback error: {e}")
    
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors related to historical data requests.
        
        Args:
            reqId (int): Request ID
            errorCode (int): Error code
            errorString (str): Error description
            advancedOrderRejectJson (str): Additional error details
        """
        self.logger.error(f"Error {reqId} {errorCode} {errorString}")
        
        # Handle historical data specific errors
        if reqId in self.active_requests:
            self.logger.error(f"Historical data request {reqId} error: {errorString}")
            self.active_requests.discard(reqId)
    
    def request_historical_data(self, req_id: int, contract: Contract, 
                              end_date_time: str = "", duration: str = "1 D",
                              bar_size: str = "1 hour", what_to_show: str = "TRADES",
                              use_rth: bool = True, format_date: int = 1,
                              keep_up_to_date: bool = False, 
                              chart_options: List = None) -> bool:
        """
        Request historical market data.
        
        Args:
            req_id (int): Unique request identifier
            contract (Contract): Contract to get historical data for
            end_date_time (str): End date/time (empty string for current time)
            duration (str): Duration string (e.g., "1 D", "2 W", "1 M", "1 Y")
            bar_size (str): Bar size (e.g., "1 min", "1 hour", "1 day")
            what_to_show (str): Type of data to retrieve
            use_rth (bool): Use regular trading hours only
            format_date (int): Date format (1=yyyyMMdd HH:mm:ss, 2=time_t)
            keep_up_to_date (bool): Keep data updated with live bars
            chart_options (list): Additional chart options
            
        Returns:
            bool: True if request was sent successfully
        """
        if req_id in self.active_requests:
            self.logger.error(f"Request ID {req_id} already has an active historical data request")
            return False
        
        # Validate parameters
        if bar_size not in self.valid_bar_sizes:
            self.logger.error(f"Invalid bar size: {bar_size}")
            return False
        
        if what_to_show not in self.what_to_show_options:
            self.logger.error(f"Invalid what_to_show: {what_to_show}")
            return False
        
        try:
            if chart_options is None:
                chart_options = []
            
            # Store request metadata
            self.request_metadata[req_id] = {
                'contract': contract,
                'end_date_time': end_date_time,
                'duration': duration,
                'bar_size': bar_size,
                'what_to_show': what_to_show,
                'use_rth': use_rth,
                'keep_up_to_date': keep_up_to_date,
                'requested_at': time.time()
            }
            
            self.reqHistoricalData(req_id, contract, end_date_time, duration,
                                 bar_size, what_to_show, use_rth, format_date,
                                 keep_up_to_date, chart_options)
            
            self.active_requests.add(req_id)
            
            self.logger.info(f"Requested historical data - ReqId: {req_id}, "
                           f"Symbol: {contract.symbol}, Duration: {duration}, "
                           f"Bar Size: {bar_size}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to request historical data: {e}")
            return False
    
    def cancel_historical_data(self, req_id: int) -> bool:
        """
        Cancel a historical data request.
        
        Args:
            req_id (int): Request ID to cancel
            
        Returns:
            bool: True if cancellation was sent successfully
        """
        if req_id not in self.active_requests:
            self.logger.warning(f"No active historical data request found for request ID {req_id}")
            return False
        
        try:
            self.cancelHistoricalData(req_id)
            self.active_requests.discard(req_id)
            
            self.logger.info(f"Canceled historical data request - ReqId: {req_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cancel historical data: {e}")
            return False
    
    def get_historical_data(self, req_id: int) -> List[BarData]:
        """
        Get historical data for a specific request.
        
        Args:
            req_id (int): Request identifier
            
        Returns:
            list: List of historical bar data
        """
        return self.historical_data.get(req_id, [])
    
    def get_data_as_dict(self, req_id: int) -> List[Dict]:
        """
        Get historical data as a list of dictionaries.
        
        Args:
            req_id (int): Request identifier
            
        Returns:
            list: List of bar data dictionaries
        """
        bars = self.get_historical_data(req_id)
        return [
            {
                'date': bar.date,
                'open': bar.open,
                'high': bar.high,
                'low': bar.low,
                'close': bar.close,
                'volume': bar.volume,
                'wap': bar.wap,
                'count': bar.count
            }
            for bar in bars
        ]
    
    def calculate_statistics(self, req_id: int) -> Dict:
        """
        Calculate basic statistics for historical data.
        
        Args:
            req_id (int): Request identifier
            
        Returns:
            dict: Statistical summary
        """
        bars = self.get_historical_data(req_id)
        if not bars:
            return {}
        
        closes = [bar.close for bar in bars]
        volumes = [bar.volume for bar in bars]
        
        stats = {
            'bar_count': len(bars),
            'date_range': {
                'start': bars[0].date if bars else None,
                'end': bars[-1].date if bars else None
            },
            'price_stats': {
                'min_close': min(closes),
                'max_close': max(closes),
                'avg_close': sum(closes) / len(closes),
                'first_close': closes[0],
                'last_close': closes[-1],
                'total_return': (closes[-1] - closes[0]) / closes[0] * 100 if closes[0] != 0 else 0
            },
            'volume_stats': {
                'min_volume': min(volumes),
                'max_volume': max(volumes),
                'avg_volume': sum(volumes) / len(volumes),
                'total_volume': sum(volumes)
            }
        }
        
        return stats
    
    def export_to_csv(self, req_id: int, filename: str) -> bool:
        """
        Export historical data to CSV file.
        
        Args:
            req_id (int): Request identifier
            filename (str): Output filename
            
        Returns:
            bool: True if export successful
        """
        try:
            import csv
            
            bars = self.get_historical_data(req_id)
            if not bars:
                self.logger.warning(f"No data to export for request {req_id}")
                return False
            
            with open(filename, 'w', newline='') as csvfile:
                fieldnames = ['date', 'open', 'high', 'low', 'close', 'volume', 'wap', 'count']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for bar in bars:
                    writer.writerow({
                        'date': bar.date,
                        'open': bar.open,
                        'high': bar.high,
                        'low': bar.low,
                        'close': bar.close,
                        'volume': bar.volume,
                        'wap': bar.wap,
                        'count': bar.count
                    })
            
            self.logger.info(f"Exported {len(bars)} bars to {filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to export data: {e}")
            return False
    
    def add_data_callback(self, event_type: str, callback: Callable):
        """
        Add a callback for historical data events.
        
        Args:
            event_type (str): Type of event
            callback: Callback function to add
        """
        if event_type in self.data_callbacks:
            self.data_callbacks[event_type].append(callback)


def demonstrate_historical_data():
    """
    Demonstrate historical data functionality with practical examples.
    """
    print("=== Historical Data Demo ===")
    
    app = HistoricalDataApp()
    
    # Add callbacks for demonstration
    def on_historical_data(req_id, bar):
        print(f"Bar: {bar.date} OHLC: {bar.open:.2f}/{bar.high:.2f}/{bar.low:.2f}/{bar.close:.2f} Vol: {bar.volume}")
    
    def on_data_end(req_id, start, end):
        print(f"Historical data complete for request {req_id}: {start} to {end}")
        
        # Show statistics
        stats = app.calculate_statistics(req_id)
        if stats:
            print(f"  Bars: {stats['bar_count']}")
            print(f"  Price range: {stats['price_stats']['min_close']:.2f} - {stats['price_stats']['max_close']:.2f}")
            print(f"  Total return: {stats['price_stats']['total_return']:.2f}%")
    
    app.add_data_callback('historical_data_received', on_historical_data)
    app.add_data_callback('historical_data_end', on_data_end)
    
    try:
        # Connect to TWS
        app.connect("127.0.0.1", 7497, 0)
        
        # Start processing thread
        api_thread = threading.Thread(target=app.run, daemon=True)
        api_thread.start()
        
        # Wait for connection
        time.sleep(3)
        
        if app.isConnected():
            print("Connected - requesting historical data")
            
            # Create sample contracts
            from api.tws_api.doc.contracts.contract_creation import ContractBuilder
            builder = ContractBuilder()
            
            # Request 1 day of 1-minute bars for Apple
            aapl_contract = builder.create_stock_contract("AAPL")
            if app.request_historical_data(9001, aapl_contract, "", "1 D", "1 min"):
                print("AAPL 1-day 1-minute data requested")
            
            # Request 1 week of daily bars for SPY
            spy_contract = builder.create_stock_contract("SPY")
            if app.request_historical_data(9002, spy_contract, "", "1 W", "1 day"):
                print("SPY 1-week daily data requested")
            
            # Wait for data
            time.sleep(15)
            
            # Export data to CSV
            app.export_to_csv(9001, "aapl_1min.csv")
            app.export_to_csv(9002, "spy_daily.csv")
            print("Data exported to CSV files")
        
        else:
            print("Failed to connect to TWS")
    
    except KeyboardInterrupt:
        print("\nShutting down...")
    
    finally:
        # Cancel all active requests
        for req_id in list(app.active_requests):
            app.cancel_historical_data(req_id)
        
        app.disconnect()


if __name__ == "__main__":
    """
    Main execution block for historical data demonstrations.
    """
    print("TWS API Historical Data Examples")
    print("=" * 40)
    
    # Run the demonstration
    demonstrate_historical_data()
