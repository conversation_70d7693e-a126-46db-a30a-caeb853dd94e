"""
Verifying TWS API Connections

This module demonstrates how to verify and monitor TWS API connections.
It covers connection status checking, connection information retrieval,
and implementing heartbeat mechanisms for connection health monitoring.

Key Topics Covered:
- Connection status verification using isConnected()
- Retrieving connection information and server details
- Implementing heartbeat mechanisms for connection monitoring
- Connection health checks and diagnostics
- Handling connection state changes

The examples provide practical approaches for ensuring connection reliability
and implementing robust connection monitoring in production applications.
"""

import threading
import time
import logging
from ibapi.client import EClient
from ibapi.wrapper import EWrapper


class ConnectionVerificationApp(EClient, EWrapper):
    """
    TWS API application with comprehensive connection verification capabilities.
    
    This class demonstrates various methods for verifying and monitoring
    TWS API connections, including status checking, heartbeat mechanisms,
    and connection health monitoring.
    
    Attributes:
        connected (bool): Current connection status
        server_version (int): TWS server version
        connection_time (str): Connection timestamp
        last_heartbeat (float): Last heartbeat timestamp
        heartbeat_interval (int): Heartbeat interval in seconds
        connection_stats (dict): Connection statistics and metrics
    """
    
    def __init__(self):
        """Initialize the connection verification application."""
        EClient.__init__(self, self)
        
        # Connection status tracking
        self.connected = False
        self.server_version = None
        self.connection_time = None
        
        # Heartbeat monitoring
        self.last_heartbeat = time.time()
        self.heartbeat_interval = 30  # seconds
        self.heartbeat_active = False
        
        # Connection statistics
        self.connection_stats = {
            'connection_attempts': 0,
            'successful_connections': 0,
            'disconnections': 0,
            'total_uptime': 0,
            'last_connection_start': None
        }
        
        # Setup logging for connection monitoring
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def connectAck(self):
        """
        Handle successful connection acknowledgment.
        
        This method is called when the connection is successfully established
        and retrieves important connection information.
        """
        self.connected = True
        self.server_version = self.serverVersion()
        self.connection_time = self.twsConnectionTime()
        
        # Update connection statistics
        self.connection_stats['successful_connections'] += 1
        self.connection_stats['last_connection_start'] = time.time()
        
        self.logger.info("Connection established successfully")
        self.logger.info(f"Server Version: {self.server_version}")
        self.logger.info(f"Connection Time: {self.connection_time}")
        
        # Start heartbeat monitoring
        self.start_heartbeat_monitoring()
    
    def connectionClosed(self):
        """
        Handle connection closure.
        
        This method is called when the connection to TWS is closed,
        either intentionally or due to network issues.
        """
        self.connected = False
        self.heartbeat_active = False
        
        # Update connection statistics
        self.connection_stats['disconnections'] += 1
        if self.connection_stats['last_connection_start']:
            uptime = time.time() - self.connection_stats['last_connection_start']
            self.connection_stats['total_uptime'] += uptime
        
        self.logger.warning("Connection closed")
    
    def nextValidId(self, orderId):
        """
        Receive next valid order ID - indicates connection is ready.
        
        Args:
            orderId (int): Next valid order ID from TWS
        """
        self.logger.info(f"Connection ready. Next valid order ID: {orderId}")
    
    def currentTime(self, time_val):
        """
        Receive current server time - used for heartbeat monitoring.
        
        Args:
            time_val (int): Current server time as Unix timestamp
        """
        self.last_heartbeat = time.time()
        self.logger.debug(f"Heartbeat received: {time_val}")
    
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors and connection-related messages.
        
        Args:
            reqId (int): Request ID
            errorCode (int): Error code
            errorString (str): Error description
            advancedOrderRejectJson (str): Additional error details
        """
        self.logger.error(f"Error {reqId} {errorCode} {errorString}")
        
        # Handle connection-related errors
        if errorCode in [502, 504]:
            self.connected = False
            self.heartbeat_active = False
        elif errorCode in [1100]:  # Connectivity lost
            self.connected = False
            self.heartbeat_active = False
            self.logger.warning("Connectivity between IB and TWS lost")
        elif errorCode in [1101, 1102]:  # Connectivity restored
            self.connected = True
            self.logger.info("Connectivity restored")
    
    def verify_connection_status(self):
        """
        Verify current connection status using multiple methods.
        
        Returns:
            dict: Comprehensive connection status information
        """
        status = {
            'is_connected': self.isConnected(),
            'internal_connected_flag': self.connected,
            'server_version': self.server_version,
            'connection_time': self.connection_time,
            'heartbeat_active': self.heartbeat_active,
            'last_heartbeat_age': time.time() - self.last_heartbeat if self.last_heartbeat else None
        }
        
        self.logger.info("Connection Status Check:")
        for key, value in status.items():
            self.logger.info(f"  {key}: {value}")
        
        return status
    
    def start_heartbeat_monitoring(self):
        """
        Start heartbeat monitoring to verify connection health.
        
        The heartbeat mechanism periodically requests the current time
        from TWS to ensure the connection is still active and responsive.
        """
        if self.heartbeat_active:
            return  # Already running
        
        self.heartbeat_active = True
        self.logger.info(f"Starting heartbeat monitoring (interval: {self.heartbeat_interval}s)")
        
        def heartbeat_loop():
            """Internal heartbeat monitoring loop."""
            while self.heartbeat_active and self.isConnected():
                try:
                    # Check if heartbeat is overdue
                    heartbeat_age = time.time() - self.last_heartbeat
                    
                    if heartbeat_age > self.heartbeat_interval:
                        self.logger.debug("Sending heartbeat request")
                        self.reqCurrentTime()
                        self.last_heartbeat = time.time()
                    
                    # Sleep for a portion of the heartbeat interval
                    time.sleep(self.heartbeat_interval // 3)
                    
                except Exception as e:
                    self.logger.error(f"Heartbeat monitoring error: {e}")
                    break
            
            self.heartbeat_active = False
            self.logger.info("Heartbeat monitoring stopped")
        
        # Start heartbeat thread
        heartbeat_thread = threading.Thread(target=heartbeat_loop, daemon=True)
        heartbeat_thread.start()
    
    def stop_heartbeat_monitoring(self):
        """Stop heartbeat monitoring."""
        self.heartbeat_active = False
        self.logger.info("Heartbeat monitoring stopped")
    
    def get_connection_statistics(self):
        """
        Get comprehensive connection statistics.
        
        Returns:
            dict: Connection statistics and metrics
        """
        stats = self.connection_stats.copy()
        
        # Calculate current session uptime if connected
        if self.connected and stats['last_connection_start']:
            current_uptime = time.time() - stats['last_connection_start']
            stats['current_session_uptime'] = current_uptime
        
        # Calculate success rate
        if stats['connection_attempts'] > 0:
            stats['success_rate'] = stats['successful_connections'] / stats['connection_attempts']
        else:
            stats['success_rate'] = 0
        
        return stats
    
    def connect_with_verification(self, host="127.0.0.1", port=7497, clientId=0, timeout=10):
        """
        Connect to TWS with built-in verification and timeout.
        
        Args:
            host (str): TWS host address
            port (int): TWS port number
            clientId (int): Client ID for this connection
            timeout (int): Connection timeout in seconds
            
        Returns:
            bool: True if connection successful, False otherwise
        """
        self.connection_stats['connection_attempts'] += 1
        
        self.logger.info(f"Attempting connection to {host}:{port} (Client ID: {clientId})")
        
        try:
            # Attempt connection
            self.connect(host, port, clientId)
            
            # Start message processing
            api_thread = threading.Thread(target=self.run, daemon=True)
            api_thread.start()
            
            # Wait for connection with timeout
            start_time = time.time()
            while not self.connected and (time.time() - start_time) < timeout:
                time.sleep(0.1)
            
            if self.connected:
                self.logger.info("Connection verification successful")
                return True
            else:
                self.logger.error(f"Connection failed - timeout after {timeout} seconds")
                return False
                
        except Exception as e:
            self.logger.error(f"Connection attempt failed: {e}")
            return False


def demonstrate_connection_verification():
    """
    Demonstrate various connection verification techniques.
    
    This function shows how to verify connections, monitor connection
    health, and retrieve connection statistics.
    """
    print("=== Connection Verification Demo ===")
    
    # Create application with verification capabilities
    app = ConnectionVerificationApp()
    
    # Attempt connection with verification
    if app.connect_with_verification("127.0.0.1", 7497, 0):
        print("Connection established successfully!")
        
        # Demonstrate connection status checking
        print("\n--- Connection Status Check ---")
        status = app.verify_connection_status()
        
        # Wait a bit to let heartbeat system work
        print("\nWaiting for heartbeat system to activate...")
        time.sleep(5)
        
        # Check status again
        print("\n--- Status After Heartbeat ---")
        status = app.verify_connection_status()
        
        # Show connection statistics
        print("\n--- Connection Statistics ---")
        stats = app.get_connection_statistics()
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # Demonstrate manual connection check
        print(f"\nManual connection check: {app.isConnected()}")
        
        # Keep connection alive for demonstration
        print("\nMonitoring connection for 10 seconds...")
        time.sleep(10)
        
        # Final status check
        print("\n--- Final Status Check ---")
        app.verify_connection_status()
        
        # Cleanup
        app.stop_heartbeat_monitoring()
        app.disconnect()
        print("Disconnected from TWS")
        
    else:
        print("Failed to establish connection")


class ConnectionHealthMonitor:
    """
    Advanced connection health monitoring utility.
    
    This class provides comprehensive monitoring capabilities for TWS API
    connections, including health checks, performance metrics, and alerting.
    """
    
    def __init__(self, app):
        """
        Initialize the connection health monitor.
        
        Args:
            app: TWS API application instance to monitor
        """
        self.app = app
        self.monitoring_active = False
        self.health_checks = []
        self.performance_metrics = {
            'response_times': [],
            'error_count': 0,
            'last_error_time': None
        }
    
    def add_health_check(self, check_function, interval=60):
        """
        Add a custom health check function.
        
        Args:
            check_function: Function that returns True if healthy
            interval (int): Check interval in seconds
        """
        self.health_checks.append({
            'function': check_function,
            'interval': interval,
            'last_check': 0
        })
    
    def start_monitoring(self):
        """Start comprehensive connection monitoring."""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        
        def monitoring_loop():
            """Main monitoring loop."""
            while self.monitoring_active:
                try:
                    # Run health checks
                    current_time = time.time()
                    for check in self.health_checks:
                        if current_time - check['last_check'] >= check['interval']:
                            try:
                                result = check['function']()
                                check['last_check'] = current_time
                                if not result:
                                    print(f"Health check failed: {check['function'].__name__}")
                            except Exception as e:
                                print(f"Health check error: {e}")
                    
                    time.sleep(5)  # Check every 5 seconds
                    
                except Exception as e:
                    print(f"Monitoring error: {e}")
        
        monitor_thread = threading.Thread(target=monitoring_loop, daemon=True)
        monitor_thread.start()
    
    def stop_monitoring(self):
        """Stop connection monitoring."""
        self.monitoring_active = False


if __name__ == "__main__":
    """
    Main execution block for connection verification examples.
    """
    print("TWS API Connection Verification Examples")
    print("=" * 50)
    
    # Run the connection verification demonstration
    demonstrate_connection_verification()
