"""
Account Updates and Real-time Portfolio Monitoring

This module demonstrates how to request and receive real-time account updates
from the TWS API. Account updates provide continuous monitoring of account
values, portfolio positions, and other account-related changes.

Key Topics Covered:
- Requesting account updates for specific accounts
- Handling real-time account value updates
- Processing portfolio position updates
- Managing account update subscriptions
- Understanding account value keys and their meanings
- Canceling account update subscriptions
- Best practices for account monitoring

Account updates provide real-time information about account changes and are
essential for monitoring trading activity and account status.
"""

import threading
import time
import logging
from typing import Dict, List, Optional, Set, Callable
from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract


class AccountUpdatesApp(EClient, EWrapper):
    """
    TWS API application for real-time account updates.
    
    This class demonstrates how to request, receive, and manage account
    update subscriptions with comprehensive data storage and event handling.
    
    Attributes:
        account_values (dict): Storage for account value updates
        portfolio_positions (dict): Storage for portfolio position updates
        account_time_updates (dict): Storage for account time updates
        active_accounts (set): Set of accounts with active subscriptions
        update_callbacks (dict): Callbacks for different update types
        account_value_keys (dict): Available account value keys and descriptions
    """
    
    def __init__(self):
        """Initialize the account updates application."""
        EClient.__init__(self, self)
        
        # Data storage
        self.account_values: Dict[str, Dict[str, Dict]] = {}
        self.portfolio_positions: Dict[str, Dict[str, Dict]] = {}
        self.account_time_updates: Dict[str, str] = {}
        
        # Subscription management
        self.active_accounts: Set[str] = set()
        
        # Callback management
        self.update_callbacks = {
            'account_value': [],
            'portfolio_update': [],
            'account_time': [],
            'account_download_end': []
        }
        
        # Common account value keys with descriptions
        self.account_value_keys = {
            'AccountCode': 'Account identifier',
            'AccountOrGroup': 'Account or group name',
            'AccountReady': 'Account ready status',
            'AccountType': 'Type of account',
            'AccruedCash': 'Accrued cash amount',
            'AccruedDividend': 'Accrued dividend amount',
            'AvailableFunds': 'Available funds for trading',
            'Billable': 'Billable amount',
            'BuyingPower': 'Current buying power',
            'CashBalance': 'Cash balance',
            'CorporateBondValue': 'Value of corporate bonds',
            'Currency': 'Account base currency',
            'Cushion': 'Cushion percentage',
            'DayTradesRemaining': 'Day trades remaining',
            'DayTradesRemainingT+1': 'Day trades remaining T+1',
            'DayTradesRemainingT+2': 'Day trades remaining T+2',
            'DayTradesRemainingT+3': 'Day trades remaining T+3',
            'DayTradesRemainingT+4': 'Day trades remaining T+4',
            'EquityWithLoanValue': 'Equity with loan value',
            'ExcessLiquidity': 'Excess liquidity',
            'ExchangeRate': 'Exchange rate',
            'FullAvailableFunds': 'Full available funds',
            'FullExcessLiquidity': 'Full excess liquidity',
            'FullInitMarginReq': 'Full initial margin requirement',
            'FullMaintMarginReq': 'Full maintenance margin requirement',
            'FundValue': 'Fund value',
            'FutureOptionValue': 'Future option value',
            'FuturesPNL': 'Futures P&L',
            'FxCashBalance': 'FX cash balance',
            'GrossPositionValue': 'Gross position value',
            'IndianStockHaircut': 'Indian stock haircut',
            'InitMarginReq': 'Initial margin requirement',
            'IssuerOptionValue': 'Issuer option value',
            'Leverage': 'Account leverage',
            'LookAheadAvailableFunds': 'Look ahead available funds',
            'LookAheadExcessLiquidity': 'Look ahead excess liquidity',
            'LookAheadInitMarginReq': 'Look ahead initial margin requirement',
            'LookAheadMaintMarginReq': 'Look ahead maintenance margin requirement',
            'LookAheadNextChange': 'Look ahead next change',
            'MaintMarginReq': 'Maintenance margin requirement',
            'MoneyMarketFundValue': 'Money market fund value',
            'MutualFundValue': 'Mutual fund value',
            'NetDividend': 'Net dividend',
            'NetLiquidation': 'Net liquidation value',
            'NetLiquidationByCurrency': 'Net liquidation by currency',
            'OptionMarketValue': 'Option market value',
            'PASharesValue': 'PA shares value',
            'PostExpirationExcess': 'Post expiration excess',
            'PostExpirationMargin': 'Post expiration margin',
            'PreviousDayEquityWithLoanValue': 'Previous day equity with loan value',
            'RealCurrency': 'Real currency',
            'RealizedPnL': 'Realized P&L',
            'RegTEquity': 'RegT equity',
            'RegTMargin': 'RegT margin',
            'SMA': 'Special Memorandum Account',
            'SegmentTitle': 'Segment title',
            'StockMarketValue': 'Stock market value',
            'TBondValue': 'T-Bond value',
            'TBillValue': 'T-Bill value',
            'TotalCashBalance': 'Total cash balance',
            'TotalCashValue': 'Total cash value',
            'TradingType': 'Trading type',
            'UnrealizedPnL': 'Unrealized P&L',
            'WarrantValue': 'Warrant value',
            'WhatIfPMEnabled': 'What-if portfolio margin enabled'
        }
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def connectAck(self):
        """Handle successful connection."""
        self.logger.info("Connected to TWS - Account Updates service ready")
    
    def nextValidId(self, orderId):
        """
        Receive next valid order ID.
        
        Args:
            orderId (int): Next valid order ID
        """
        self.logger.info(f"Connection ready. Next valid order ID: {orderId}")
    
    def updateAccountValue(self, key: str, val: str, currency: str, accountName: str):
        """
        Receive account value updates.
        
        This method is called whenever an account value changes. It provides
        real-time updates of various account metrics.
        
        Args:
            key (str): Account value key (e.g., 'NetLiquidation', 'BuyingPower')
            val (str): Current value
            currency (str): Currency of the value
            accountName (str): Account identifier
        """
        # Initialize account data structure if needed
        if accountName not in self.account_values:
            self.account_values[accountName] = {}
        
        # Store the account value update
        self.account_values[accountName][key] = {
            'value': val,
            'currency': currency,
            'timestamp': time.time()
        }
        
        self.logger.info(f"Account Value Update - Account: {accountName}, "
                        f"Key: {key}, Value: {val}, Currency: {currency}")
        
        # Trigger callbacks
        for callback in self.update_callbacks['account_value']:
            try:
                callback(key, val, currency, accountName)
            except Exception as e:
                self.logger.error(f"Account value callback error: {e}")
    
    def updatePortfolio(self, contract: Contract, position: float, marketPrice: float,
                       marketValue: float, averageCost: float, unrealizedPNL: float,
                       realizedPNL: float, accountName: str):
        """
        Receive portfolio position updates.
        
        This method is called for each position in the portfolio, providing
        real-time updates of position information.
        
        Args:
            contract (Contract): Contract object for the position
            position (float): Current position size
            marketPrice (float): Current market price
            marketValue (float): Current market value
            averageCost (float): Average cost of the position
            unrealizedPNL (float): Unrealized P&L
            realizedPNL (float): Realized P&L
            accountName (str): Account identifier
        """
        # Create position key from contract
        position_key = f"{contract.symbol}_{contract.secType}_{contract.exchange}_{contract.currency}"
        
        # Initialize account portfolio structure if needed
        if accountName not in self.portfolio_positions:
            self.portfolio_positions[accountName] = {}
        
        # Store the portfolio update
        self.portfolio_positions[accountName][position_key] = {
            'contract': contract,
            'position': position,
            'marketPrice': marketPrice,
            'marketValue': marketValue,
            'averageCost': averageCost,
            'unrealizedPNL': unrealizedPNL,
            'realizedPNL': realizedPNL,
            'timestamp': time.time()
        }
        
        self.logger.info(f"Portfolio Update - Account: {accountName}, "
                        f"Symbol: {contract.symbol}, Position: {position}, "
                        f"Market Value: {marketValue}, Unrealized P&L: {unrealizedPNL}")
        
        # Trigger callbacks
        for callback in self.update_callbacks['portfolio_update']:
            try:
                callback(contract, position, marketPrice, marketValue,
                        averageCost, unrealizedPNL, realizedPNL, accountName)
            except Exception as e:
                self.logger.error(f"Portfolio callback error: {e}")
    
    def updateAccountTime(self, timeStamp: str):
        """
        Receive account time updates.
        
        This method provides the timestamp of the last account update.
        
        Args:
            timeStamp (str): Timestamp of the last update
        """
        self.account_time_updates['last_update'] = timeStamp
        
        self.logger.debug(f"Account Time Update: {timeStamp}")
        
        # Trigger callbacks
        for callback in self.update_callbacks['account_time']:
            try:
                callback(timeStamp)
            except Exception as e:
                self.logger.error(f"Account time callback error: {e}")
    
    def accountDownloadEnd(self, accountName: str):
        """
        Handle end of account data download.
        
        This method is called when the initial download of account data
        is complete for a specific account.
        
        Args:
            accountName (str): Account identifier
        """
        self.logger.info(f"Account download complete for: {accountName}")
        
        # Trigger callbacks
        for callback in self.update_callbacks['account_download_end']:
            try:
                callback(accountName)
            except Exception as e:
                self.logger.error(f"Account download end callback error: {e}")
    
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors related to account updates.
        
        Args:
            reqId (int): Request ID
            errorCode (int): Error code
            errorString (str): Error description
            advancedOrderRejectJson (str): Additional error details
        """
        self.logger.error(f"Error {reqId} {errorCode} {errorString}")
    
    def request_account_updates(self, account: str, subscribe: bool = True) -> bool:
        """
        Request account updates for a specific account.
        
        Args:
            account (str): Account identifier
            subscribe (bool): True to subscribe, False to unsubscribe
            
        Returns:
            bool: True if request was sent successfully
        """
        try:
            self.reqAccountUpdates(subscribe, account)
            
            if subscribe:
                self.active_accounts.add(account)
                self.logger.info(f"Subscribed to account updates for: {account}")
            else:
                self.active_accounts.discard(account)
                self.logger.info(f"Unsubscribed from account updates for: {account}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to request account updates: {e}")
            return False
    
    def cancel_account_updates(self, account: str) -> bool:
        """
        Cancel account updates for a specific account.
        
        Args:
            account (str): Account identifier
            
        Returns:
            bool: True if cancellation was sent successfully
        """
        return self.request_account_updates(account, subscribe=False)
    
    def get_account_values(self, account: Optional[str] = None) -> Dict:
        """
        Get stored account values.
        
        Args:
            account (str, optional): Specific account to retrieve
            
        Returns:
            dict: Account values data
        """
        if account is not None:
            return self.account_values.get(account, {})
        else:
            return self.account_values.copy()
    
    def get_portfolio_positions(self, account: Optional[str] = None) -> Dict:
        """
        Get stored portfolio positions.
        
        Args:
            account (str, optional): Specific account to retrieve
            
        Returns:
            dict: Portfolio positions data
        """
        if account is not None:
            return self.portfolio_positions.get(account, {})
        else:
            return self.portfolio_positions.copy()
    
    def get_account_value(self, account: str, key: str) -> Optional[Dict]:
        """
        Get a specific account value by key.
        
        Args:
            account (str): Account identifier
            key (str): Account value key
            
        Returns:
            dict: Value information or None if not found
        """
        if (account in self.account_values and 
            key in self.account_values[account]):
            return self.account_values[account][key]
        return None
    
    def add_update_callback(self, event_type: str, callback: Callable):
        """
        Add a callback for account update events.
        
        Args:
            event_type (str): Type of event ('account_value', 'portfolio_update', etc.)
            callback: Callback function to add
        """
        if event_type in self.update_callbacks:
            self.update_callbacks[event_type].append(callback)
    
    def get_available_keys(self) -> Dict[str, str]:
        """
        Get available account value keys with descriptions.
        
        Returns:
            dict: Dictionary of key names and descriptions
        """
        return self.account_value_keys.copy()


def demonstrate_account_updates():
    """
    Demonstrate account updates functionality with practical examples.
    """
    print("=== Account Updates Demo ===")
    
    app = AccountUpdatesApp()
    
    # Add callbacks for demonstration
    def on_account_value(key, val, currency, account):
        if key in ['NetLiquidation', 'BuyingPower', 'AvailableFunds']:
            print(f"Key Update: {account} - {key}: {val} {currency}")
    
    def on_portfolio_update(contract, position, market_price, market_value,
                          avg_cost, unrealized_pnl, realized_pnl, account):
        if position != 0:  # Only show non-zero positions
            print(f"Position: {account} - {contract.symbol}: {position} @ {market_price}")
    
    def on_download_end(account):
        print(f"Initial download complete for account: {account}")
    
    app.add_update_callback('account_value', on_account_value)
    app.add_update_callback('portfolio_update', on_portfolio_update)
    app.add_update_callback('account_download_end', on_download_end)
    
    try:
        # Connect to TWS
        app.connect("127.0.0.1", 7497, 0)
        
        # Start processing thread
        api_thread = threading.Thread(target=app.run, daemon=True)
        api_thread.start()
        
        # Wait for connection
        time.sleep(3)
        
        if app.isConnected():
            print("Connected - requesting account updates")
            
            # Request account updates (use empty string for default account)
            if app.request_account_updates(""):
                print("Account updates requested successfully")
                
                # Monitor for updates
                print("Monitoring account updates for 15 seconds...")
                time.sleep(15)
                
                # Display summary of received data
                account_values = app.get_account_values()
                portfolio_positions = app.get_portfolio_positions()
                
                print("\n--- Account Summary ---")
                for account, values in account_values.items():
                    print(f"Account: {account}")
                    for key, info in values.items():
                        if key in ['NetLiquidation', 'BuyingPower', 'TotalCashValue']:
                            print(f"  {key}: {info['value']} {info['currency']}")
                
                print("\n--- Portfolio Positions ---")
                for account, positions in portfolio_positions.items():
                    print(f"Account: {account}")
                    for pos_key, pos_info in positions.items():
                        if pos_info['position'] != 0:
                            contract = pos_info['contract']
                            print(f"  {contract.symbol}: {pos_info['position']} "
                                  f"@ {pos_info['marketPrice']} = {pos_info['marketValue']}")
                
                # Cancel subscription
                app.cancel_account_updates("")
                print("Account updates subscription canceled")
        
        else:
            print("Failed to connect to TWS")
    
    except KeyboardInterrupt:
        print("\nShutting down...")
    
    finally:
        # Cancel all active subscriptions
        for account in list(app.active_accounts):
            app.cancel_account_updates(account)
        
        app.disconnect()


if __name__ == "__main__":
    """
    Main execution block for account updates demonstrations.
    """
    print("TWS API Account Updates Examples")
    print("=" * 40)
    
    # Show available account value keys
    app = AccountUpdatesApp()
    print("Available Account Value Keys (sample):")
    sample_keys = ['NetLiquidation', 'BuyingPower', 'AvailableFunds', 
                   'TotalCashValue', 'UnrealizedPnL', 'RealizedPnL']
    for key in sample_keys:
        if key in app.get_available_keys():
            print(f"  {key}: {app.get_available_keys()[key]}")
    
    print("\n" + "=" * 40)
    
    # Run the demonstration
    demonstrate_account_updates()
