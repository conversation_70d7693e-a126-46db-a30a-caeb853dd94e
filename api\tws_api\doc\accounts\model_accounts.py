"""
Account Updates by Model for Financial Advisors

This module demonstrates how to request and receive account updates by model
for Financial Advisor accounts. Model-based updates allow FAs to monitor
account data organized by allocation models.

Key Topics Covered:
- Requesting account updates by model
- Handling model-based account value updates
- Processing model-specific portfolio updates
- Managing model account subscriptions
- Understanding FA model structures
- Canceling model account subscriptions
- Best practices for FA account management

Model account updates are specifically designed for Financial Advisor
accounts that use allocation models to manage multiple client accounts.
"""

import threading
import time
import logging
from typing import Dict, List, Optional, Set, Callable
from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract


class ModelAccountsApp(EClient, EWrapper):
    """
    TWS API application for model-based account updates (Financial Advisor feature).
    
    This class demonstrates how to request, receive, and manage account
    updates organized by allocation models for Financial Advisor accounts.
    
    Attributes:
        model_account_values (dict): Storage for model account value updates
        model_portfolio_positions (dict): Storage for model portfolio updates
        active_model_subscriptions (set): Set of active model subscription request IDs
        model_callbacks (dict): Callbacks for model update events
    """
    
    def __init__(self):
        """Initialize the model accounts application."""
        EClient.__init__(self, self)
        
        # Data storage
        self.model_account_values: Dict[int, Dict[str, Dict[str, Dict]]] = {}
        self.model_portfolio_positions: Dict[int, Dict[str, Dict[str, Dict]]] = {}
        
        # Subscription management
        self.active_model_subscriptions: Set[int] = set()
        
        # Callback management
        self.model_callbacks = {
            'account_update_multi': [],
            'account_update_multi_end': [],
            'portfolio_update_multi': [],
            'portfolio_update_multi_end': []
        }
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def connectAck(self):
        """Handle successful connection."""
        self.logger.info("Connected to TWS - Model Accounts service ready")
    
    def nextValidId(self, orderId):
        """
        Receive next valid order ID.
        
        Args:
            orderId (int): Next valid order ID
        """
        self.logger.info(f"Connection ready. Next valid order ID: {orderId}")
    
    def accountUpdateMulti(self, reqId: int, account: str, modelCode: str, 
                          key: str, value: str, currency: str):
        """
        Receive account value updates by model.
        
        This method is called for account value updates organized by
        allocation models in Financial Advisor accounts.
        
        Args:
            reqId (int): Request ID for the subscription
            account (str): Account identifier
            modelCode (str): Model code for the allocation model
            key (str): Account value key
            value (str): Account value
            currency (str): Currency of the value
        """
        # Initialize data structure if needed
        if reqId not in self.model_account_values:
            self.model_account_values[reqId] = {}
        
        if account not in self.model_account_values[reqId]:
            self.model_account_values[reqId][account] = {}
        
        if modelCode not in self.model_account_values[reqId][account]:
            self.model_account_values[reqId][account][modelCode] = {}
        
        # Store the model account value update
        self.model_account_values[reqId][account][modelCode][key] = {
            'value': value,
            'currency': currency,
            'timestamp': time.time()
        }
        
        self.logger.info(f"Account Update Multi - ReqId: {reqId}, Account: {account}, "
                        f"Model: {modelCode}, Key: {key}, Value: {value}, Currency: {currency}")
        
        # Trigger callbacks
        for callback in self.model_callbacks['account_update_multi']:
            try:
                callback(reqId, account, modelCode, key, value, currency)
            except Exception as e:
                self.logger.error(f"Account update multi callback error: {e}")
    
    def accountUpdateMultiEnd(self, reqId: int):
        """
        Handle end of model account updates transmission.
        
        Args:
            reqId (int): Request ID for the completed subscription
        """
        self.logger.info(f"Account Update Multi End - ReqId: {reqId}")
        
        # Trigger end callbacks
        for callback in self.model_callbacks['account_update_multi_end']:
            try:
                callback(reqId)
            except Exception as e:
                self.logger.error(f"Account update multi end callback error: {e}")
    
    def portfolioUpdateMulti(self, reqId: int, account: str, modelCode: str,
                           contract: Contract, pos: float, marketPrice: float,
                           marketValue: float, averageCost: float, unrealizedPNL: float,
                           realizedPNL: float):
        """
        Receive portfolio updates by model.
        
        This method is called for portfolio position updates organized by
        allocation models in Financial Advisor accounts.
        
        Args:
            reqId (int): Request ID for the subscription
            account (str): Account identifier
            modelCode (str): Model code for the allocation model
            contract (Contract): Contract object for the position
            pos (float): Position size
            marketPrice (float): Current market price
            marketValue (float): Current market value
            averageCost (float): Average cost of the position
            unrealizedPNL (float): Unrealized P&L
            realizedPNL (float): Realized P&L
        """
        # Create position key from contract
        position_key = f"{contract.symbol}_{contract.secType}_{contract.exchange}_{contract.currency}"
        
        # Initialize data structure if needed
        if reqId not in self.model_portfolio_positions:
            self.model_portfolio_positions[reqId] = {}
        
        if account not in self.model_portfolio_positions[reqId]:
            self.model_portfolio_positions[reqId][account] = {}
        
        if modelCode not in self.model_portfolio_positions[reqId][account]:
            self.model_portfolio_positions[reqId][account][modelCode] = {}
        
        # Store the model portfolio update
        self.model_portfolio_positions[reqId][account][modelCode][position_key] = {
            'contract': contract,
            'position': pos,
            'marketPrice': marketPrice,
            'marketValue': marketValue,
            'averageCost': averageCost,
            'unrealizedPNL': unrealizedPNL,
            'realizedPNL': realizedPNL,
            'timestamp': time.time()
        }
        
        self.logger.info(f"Portfolio Update Multi - ReqId: {reqId}, Account: {account}, "
                        f"Model: {modelCode}, Symbol: {contract.symbol}, Position: {pos}, "
                        f"Market Value: {marketValue}")
        
        # Trigger callbacks
        for callback in self.model_callbacks['portfolio_update_multi']:
            try:
                callback(reqId, account, modelCode, contract, pos, marketPrice,
                        marketValue, averageCost, unrealizedPNL, realizedPNL)
            except Exception as e:
                self.logger.error(f"Portfolio update multi callback error: {e}")
    
    def portfolioUpdateMultiEnd(self, reqId: int):
        """
        Handle end of model portfolio updates transmission.
        
        Args:
            reqId (int): Request ID for the completed subscription
        """
        self.logger.info(f"Portfolio Update Multi End - ReqId: {reqId}")
        
        # Trigger end callbacks
        for callback in self.model_callbacks['portfolio_update_multi_end']:
            try:
                callback(reqId)
            except Exception as e:
                self.logger.error(f"Portfolio update multi end callback error: {e}")
    
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors related to model account requests.
        
        Args:
            reqId (int): Request ID
            errorCode (int): Error code
            errorString (str): Error description
            advancedOrderRejectJson (str): Additional error details
        """
        self.logger.error(f"Error {reqId} {errorCode} {errorString}")
        
        # Handle model subscription specific errors
        if reqId in self.active_model_subscriptions:
            self.logger.error(f"Model account subscription {reqId} error: {errorString}")
    
    def request_account_updates_multi(self, req_id: int, account: str, 
                                    model_code: str = "", ledger_and_nlv: bool = True) -> bool:
        """
        Request account updates by model.
        
        Args:
            req_id (int): Unique request identifier
            account (str): Account identifier
            model_code (str): Model code (empty string for all models)
            ledger_and_nlv (bool): Include ledger and net liquidation value
            
        Returns:
            bool: True if request was sent successfully
        """
        if req_id in self.active_model_subscriptions:
            self.logger.error(f"Request ID {req_id} already has an active model subscription")
            return False
        
        try:
            self.reqAccountUpdatesMulti(req_id, account, model_code, ledger_and_nlv)
            self.active_model_subscriptions.add(req_id)
            
            self.logger.info(f"Requested account updates multi - ReqId: {req_id}, "
                           f"Account: {account}, Model: {model_code}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to request account updates multi: {e}")
            return False
    
    def cancel_account_updates_multi(self, req_id: int) -> bool:
        """
        Cancel a model account updates subscription.
        
        Args:
            req_id (int): Request ID to cancel
            
        Returns:
            bool: True if cancellation was sent successfully
        """
        if req_id not in self.active_model_subscriptions:
            self.logger.warning(f"No active model subscription found for request ID {req_id}")
            return False
        
        try:
            self.cancelAccountUpdatesMulti(req_id)
            self.active_model_subscriptions.remove(req_id)
            
            self.logger.info(f"Canceled account updates multi subscription - ReqId: {req_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cancel account updates multi: {e}")
            return False
    
    def get_model_account_values(self, req_id: Optional[int] = None) -> Dict:
        """
        Get stored model account values.
        
        Args:
            req_id (int, optional): Specific request ID to retrieve
            
        Returns:
            dict: Model account values data
        """
        if req_id is not None:
            return self.model_account_values.get(req_id, {})
        else:
            return self.model_account_values.copy()
    
    def get_model_portfolio_positions(self, req_id: Optional[int] = None) -> Dict:
        """
        Get stored model portfolio positions.
        
        Args:
            req_id (int, optional): Specific request ID to retrieve
            
        Returns:
            dict: Model portfolio positions data
        """
        if req_id is not None:
            return self.model_portfolio_positions.get(req_id, {})
        else:
            return self.model_portfolio_positions.copy()
    
    def get_models_for_account(self, req_id: int, account: str) -> List[str]:
        """
        Get list of models for a specific account.
        
        Args:
            req_id (int): Request ID
            account (str): Account identifier
            
        Returns:
            list: List of model codes for the account
        """
        models = set()
        
        # Check account values
        if (req_id in self.model_account_values and 
            account in self.model_account_values[req_id]):
            models.update(self.model_account_values[req_id][account].keys())
        
        # Check portfolio positions
        if (req_id in self.model_portfolio_positions and 
            account in self.model_portfolio_positions[req_id]):
            models.update(self.model_portfolio_positions[req_id][account].keys())
        
        return list(models)
    
    def get_model_summary(self, req_id: int) -> Dict:
        """
        Get summary of model data for a request.
        
        Args:
            req_id (int): Request ID
            
        Returns:
            dict: Summary of model data
        """
        summary = {
            'accounts': set(),
            'models': set(),
            'account_values_count': 0,
            'portfolio_positions_count': 0
        }
        
        # Process account values
        if req_id in self.model_account_values:
            for account, models in self.model_account_values[req_id].items():
                summary['accounts'].add(account)
                for model_code, values in models.items():
                    summary['models'].add(model_code)
                    summary['account_values_count'] += len(values)
        
        # Process portfolio positions
        if req_id in self.model_portfolio_positions:
            for account, models in self.model_portfolio_positions[req_id].items():
                summary['accounts'].add(account)
                for model_code, positions in models.items():
                    summary['models'].add(model_code)
                    summary['portfolio_positions_count'] += len(positions)
        
        # Convert sets to lists
        summary['accounts'] = list(summary['accounts'])
        summary['models'] = list(summary['models'])
        
        return summary
    
    def add_model_callback(self, event_type: str, callback: Callable):
        """
        Add a callback for model update events.
        
        Args:
            event_type (str): Type of event ('account_update_multi', etc.)
            callback: Callback function to add
        """
        if event_type in self.model_callbacks:
            self.model_callbacks[event_type].append(callback)


def demonstrate_model_accounts():
    """
    Demonstrate model accounts functionality with practical examples.
    """
    print("=== Model Accounts Demo ===")
    print("Note: This demo requires a Financial Advisor account with allocation models")
    
    app = ModelAccountsApp()
    
    # Add callbacks for demonstration
    def on_account_update_multi(req_id, account, model_code, key, value, currency):
        if key in ['NetLiquidation', 'BuyingPower', 'TotalCashValue']:
            print(f"Model Account Update: {account}/{model_code} - {key}: {value} {currency}")
    
    def on_portfolio_update_multi(req_id, account, model_code, contract, pos, 
                                market_price, market_value, avg_cost, unrealized_pnl, realized_pnl):
        if pos != 0:
            print(f"Model Portfolio: {account}/{model_code} - {contract.symbol}: {pos}")
    
    def on_update_end(req_id):
        print(f"Model updates complete for request {req_id}")
    
    app.add_model_callback('account_update_multi', on_account_update_multi)
    app.add_model_callback('portfolio_update_multi', on_portfolio_update_multi)
    app.add_model_callback('account_update_multi_end', on_update_end)
    
    try:
        # Connect to TWS
        app.connect("127.0.0.1", 7497, 0)
        
        # Start processing thread
        api_thread = threading.Thread(target=app.run, daemon=True)
        api_thread.start()
        
        # Wait for connection
        time.sleep(3)
        
        if app.isConnected():
            print("Connected - requesting model account updates")
            
            # Request model account updates (use appropriate FA account)
            fa_account = "DU123456"  # Replace with actual FA account
            
            if app.request_account_updates_multi(9001, fa_account, ""):
                print("Model account updates requested successfully")
                
                # Wait for data
                time.sleep(15)
                
                # Display summary
                summary = app.get_model_summary(9001)
                print("\n--- Model Summary ---")
                for key, value in summary.items():
                    print(f"{key}: {value}")
                
                # Cancel subscription
                app.cancel_account_updates_multi(9001)
                print("Model account updates subscription canceled")
        
        else:
            print("Failed to connect to TWS")
    
    except KeyboardInterrupt:
        print("\nShutting down...")
    
    finally:
        # Cancel all active subscriptions
        for req_id in list(app.active_model_subscriptions):
            app.cancel_account_updates_multi(req_id)
        
        app.disconnect()


if __name__ == "__main__":
    """
    Main execution block for model accounts demonstrations.
    """
    print("TWS API Model Accounts Examples")
    print("=" * 40)
    
    # Run the demonstration
    demonstrate_model_accounts()
