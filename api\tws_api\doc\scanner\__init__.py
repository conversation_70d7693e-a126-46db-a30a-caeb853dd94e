"""
TWS API Market Scanner Documentation

This module provides comprehensive documentation and examples for using
the Market Scanner functionality in the Interactive Brokers TWS API.
The Market Scanner helps identify trading opportunities by scanning
markets based on various criteria.

The scanner module covers:
- scanner_subscriptions: Setting up and managing scanner subscriptions
- scanner_parameters: Understanding scanner parameters and filters
- scanner_results: Processing and analyzing scanner results
- custom_scans: Creating custom scanner configurations
- scanner_data: Working with scanner data and rankings
- scanner_filters: Advanced filtering and sorting options

Key Concepts:
- Scanner Subscriptions: Real-time scans that continuously update results
- Scanner Parameters: Criteria used to filter and rank securities
- Scan Codes: Predefined scan types (e.g., TOP_PERC_GAIN, MOST_ACTIVE)
- Filters: Additional criteria to narrow down results
- Rankings: How securities are ordered in scan results
- Instruments: Types of securities to include in scans

Usage:
    from api.tws_api.doc.scanner import scanner_subscriptions
    from api.tws_api.doc.scanner import scanner_parameters
    from api.tws_api.doc.scanner import scanner_results
    # ... import other modules as needed

Important Notes:
- Scanner functionality requires appropriate market data subscriptions
- Some scanner types may have limitations based on account type
- Scanner results are updated in real-time during market hours
- Custom scanner configurations allow for sophisticated screening
- Scanner data can be used for automated trading strategies
"""

# Import all scanner modules for easy access
from . import scanner_subscriptions
from . import scanner_parameters
from . import scanner_results
from . import custom_scans
from . import scanner_data
from . import scanner_filters

__all__ = [
    'scanner_subscriptions',
    'scanner_parameters', 
    'scanner_results',
    'custom_scans',
    'scanner_data',
    'scanner_filters'
]
