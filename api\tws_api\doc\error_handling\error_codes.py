"""
TWS API Error Codes Reference and Handling

This module provides a comprehensive reference for TWS API error codes
and their meanings. Understanding error codes is essential for proper
error handling and troubleshooting in trading applications.

Key Topics Covered:
- Complete error code reference with descriptions
- Error code categories and classifications
- Severity levels and appropriate responses
- Common error scenarios and solutions
- Error code lookup and interpretation utilities
- Best practices for error code handling

Error codes provide specific information about what went wrong and
often indicate the appropriate recovery action to take.
"""

import logging
from typing import Dict, List, Optional, Tuple
from enum import Enum


class ErrorSeverity(Enum):
    """Error severity levels for classification."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Categories of error types."""
    CONNECTION = "connection"
    AUTHENTICATION = "authentication"
    PERMISSIONS = "permissions"
    ORDER = "order"
    CONTRACT = "contract"
    DATA = "data"
    SYSTEM = "system"
    VALIDATION = "validation"


class TWSErrorCodes:
    """
    Comprehensive reference for TWS API error codes.
    
    This class provides detailed information about error codes,
    their meanings, categories, and recommended actions.
    
    Attributes:
        error_codes (dict): Complete mapping of error codes to information
        categories (dict): Error codes organized by category
    """
    
    def __init__(self):
        """Initialize the error codes reference."""
        self.logger = logging.getLogger(__name__)
        
        # Comprehensive error code definitions
        self.error_codes = {
            # Connection and System Errors (100-199)
            100: {
                'message': 'Max number of tickers reached',
                'description': 'Maximum number of market data subscriptions exceeded',
                'category': ErrorCategory.DATA,
                'severity': ErrorSeverity.ERROR,
                'action': 'Cancel some market data subscriptions before requesting new ones'
            },
            101: {
                'message': 'Max number of market data requests reached',
                'description': 'Too many simultaneous market data requests',
                'category': ErrorCategory.DATA,
                'severity': ErrorSeverity.ERROR,
                'action': 'Reduce the number of concurrent market data requests'
            },
            102: {
                'message': 'Max number of historical data requests reached',
                'description': 'Historical data request limit exceeded',
                'category': ErrorCategory.DATA,
                'severity': ErrorSeverity.ERROR,
                'action': 'Wait before making additional historical data requests'
            },
            103: {
                'message': 'Duplicate ticker ID',
                'description': 'Attempted to use a ticker ID that is already in use',
                'category': ErrorCategory.VALIDATION,
                'severity': ErrorSeverity.ERROR,
                'action': 'Use a unique ticker ID for each market data subscription'
            },
            104: {
                'message': 'Can\'t find EId with ticker Id',
                'description': 'Invalid ticker ID specified',
                'category': ErrorCategory.VALIDATION,
                'severity': ErrorSeverity.ERROR,
                'action': 'Verify the ticker ID is correct and active'
            },
            
            # Contract and Security Definition Errors (200-299)
            200: {
                'message': 'No security definition found',
                'description': 'Contract specification does not match any available security',
                'category': ErrorCategory.CONTRACT,
                'severity': ErrorSeverity.ERROR,
                'action': 'Verify contract details (symbol, exchange, currency, etc.)'
            },
            201: {
                'message': 'Order rejected - reason',
                'description': 'Order was rejected by the exchange or IB systems',
                'category': ErrorCategory.ORDER,
                'severity': ErrorSeverity.ERROR,
                'action': 'Check order parameters and account permissions'
            },
            202: {
                'message': 'Order cancelled',
                'description': 'Order was successfully cancelled',
                'category': ErrorCategory.ORDER,
                'severity': ErrorSeverity.INFO,
                'action': 'No action required - informational message'
            },
            203: {
                'message': 'The security type \'STK\' is ambiguous',
                'description': 'Multiple securities match the contract specification',
                'category': ErrorCategory.CONTRACT,
                'severity': ErrorSeverity.ERROR,
                'action': 'Add more specific contract details (primaryExchange, conId)'
            },
            
            # Permission and Authentication Errors (300-399)
            300: {
                'message': 'Can\'t find a contract with conId',
                'description': 'Contract ID does not exist or is not accessible',
                'category': ErrorCategory.CONTRACT,
                'severity': ErrorSeverity.ERROR,
                'action': 'Verify the contract ID is correct and accessible'
            },
            354: {
                'message': 'Requested market data is not subscribed',
                'description': 'No market data permissions for this security',
                'category': ErrorCategory.PERMISSIONS,
                'severity': ErrorSeverity.WARNING,
                'action': 'Subscribe to market data or use delayed data'
            },
            
            # Order Management Errors (400-499)
            400: {
                'message': 'Order size is too small',
                'description': 'Order quantity is below minimum requirements',
                'category': ErrorCategory.ORDER,
                'severity': ErrorSeverity.ERROR,
                'action': 'Increase order quantity to meet minimum requirements'
            },
            401: {
                'message': 'Order size is too large',
                'description': 'Order quantity exceeds maximum allowed size',
                'category': ErrorCategory.ORDER,
                'severity': ErrorSeverity.ERROR,
                'action': 'Reduce order quantity or split into multiple orders'
            },
            404: {
                'message': 'Order not found',
                'description': 'Specified order ID does not exist',
                'category': ErrorCategory.ORDER,
                'severity': ErrorSeverity.ERROR,
                'action': 'Verify the order ID is correct and the order exists'
            },
            
            # Account and Position Errors (500-599)
            500: {
                'message': 'Already connected',
                'description': 'Client is already connected to TWS',
                'category': ErrorCategory.CONNECTION,
                'severity': ErrorSeverity.WARNING,
                'action': 'Disconnect before attempting to reconnect'
            },
            501: {
                'message': 'Couldn\'t connect to TWS',
                'description': 'Failed to establish connection to TWS',
                'category': ErrorCategory.CONNECTION,
                'severity': ErrorSeverity.CRITICAL,
                'action': 'Check TWS is running and connection parameters'
            },
            502: {
                'message': 'Couldn\'t connect to TWS. Confirm that API is enabled',
                'description': 'TWS API is not enabled in configuration',
                'category': ErrorCategory.CONNECTION,
                'severity': ErrorSeverity.CRITICAL,
                'action': 'Enable API in TWS Global Configuration'
            },
            503: {
                'message': 'The TWS is out of date and must be upgraded',
                'description': 'TWS version is too old for API compatibility',
                'category': ErrorCategory.SYSTEM,
                'severity': ErrorSeverity.CRITICAL,
                'action': 'Update TWS to the latest version'
            },
            504: {
                'message': 'Not connected',
                'description': 'Client is not connected to TWS',
                'category': ErrorCategory.CONNECTION,
                'severity': ErrorSeverity.ERROR,
                'action': 'Establish connection to TWS before making requests'
            },
            
            # Data Feed Errors (1100-1199)
            1100: {
                'message': 'Connectivity between IB and TWS has been lost',
                'description': 'Connection between TWS and IB servers lost',
                'category': ErrorCategory.CONNECTION,
                'severity': ErrorSeverity.CRITICAL,
                'action': 'Wait for automatic reconnection or restart TWS'
            },
            1101: {
                'message': 'Connectivity between IB and TWS has been restored - data lost',
                'description': 'Connection restored but some data may be missing',
                'category': ErrorCategory.CONNECTION,
                'severity': ErrorSeverity.WARNING,
                'action': 'Re-request any critical data that may have been lost'
            },
            1102: {
                'message': 'Connectivity between IB and TWS has been restored - data maintained',
                'description': 'Connection restored and data integrity maintained',
                'category': ErrorCategory.CONNECTION,
                'severity': ErrorSeverity.INFO,
                'action': 'No action required - normal operation resumed'
            },
            
            # Historical Data Errors (1300-1399)
            1300: {
                'message': 'TWS socket port has been reset',
                'description': 'TWS socket connection was reset',
                'category': ErrorCategory.CONNECTION,
                'severity': ErrorSeverity.WARNING,
                'action': 'Reconnect to TWS with correct port settings'
            },
            
            # News Errors (1400-1499)
            1400: {
                'message': 'News feed is not available',
                'description': 'News data feed is not accessible',
                'category': ErrorCategory.DATA,
                'severity': ErrorSeverity.WARNING,
                'action': 'Check news data subscriptions and permissions'
            },
            
            # Scanner Errors (1500-1599)
            1500: {
                'message': 'Scanner subscription cancelled',
                'description': 'Market scanner subscription was cancelled',
                'category': ErrorCategory.DATA,
                'severity': ErrorSeverity.INFO,
                'action': 'No action required if cancellation was intentional'
            },
            
            # General System Messages (2100-2199)
            2100: {
                'message': 'New account data requested',
                'description': 'Account data update has been requested',
                'category': ErrorCategory.SYSTEM,
                'severity': ErrorSeverity.INFO,
                'action': 'No action required - informational message'
            },
            2101: {
                'message': 'Account data has been updated',
                'description': 'Account information has been refreshed',
                'category': ErrorCategory.SYSTEM,
                'severity': ErrorSeverity.INFO,
                'action': 'No action required - informational message'
            },
            2102: {
                'message': 'Account data update complete',
                'description': 'Account data update process finished',
                'category': ErrorCategory.SYSTEM,
                'severity': ErrorSeverity.INFO,
                'action': 'No action required - informational message'
            },
            2103: {
                'message': 'Market data farm connection is OK',
                'description': 'Market data connection is functioning normally',
                'category': ErrorCategory.SYSTEM,
                'severity': ErrorSeverity.INFO,
                'action': 'No action required - status message'
            },
            2104: {
                'message': 'Market data farm connection is broken',
                'description': 'Market data connection has been interrupted',
                'category': ErrorCategory.CONNECTION,
                'severity': ErrorSeverity.WARNING,
                'action': 'Monitor for automatic reconnection'
            },
            2105: {
                'message': 'Historical data farm connection is OK',
                'description': 'Historical data connection is functioning normally',
                'category': ErrorCategory.SYSTEM,
                'severity': ErrorSeverity.INFO,
                'action': 'No action required - status message'
            },
            2106: {
                'message': 'Historical data farm connection is broken',
                'description': 'Historical data connection has been interrupted',
                'category': ErrorCategory.CONNECTION,
                'severity': ErrorSeverity.WARNING,
                'action': 'Wait for reconnection before requesting historical data'
            },
            2107: {
                'message': 'Historical data request pacing violation',
                'description': 'Historical data requests are being made too frequently',
                'category': ErrorCategory.DATA,
                'severity': ErrorSeverity.WARNING,
                'action': 'Reduce frequency of historical data requests'
            },
            2108: {
                'message': 'Market data farm connection is inactive but should be available',
                'description': 'Market data connection is inactive',
                'category': ErrorCategory.CONNECTION,
                'severity': ErrorSeverity.WARNING,
                'action': 'Check market data subscriptions and connection'
            }
        }
        
        # Organize error codes by category
        self.categories = {}
        for error_code, error_info in self.error_codes.items():
            category = error_info['category']
            if category not in self.categories:
                self.categories[category] = {}
            self.categories[category][error_code] = error_info
    
    def get_error_info(self, error_code: int) -> Optional[Dict]:
        """
        Get detailed information about an error code.
        
        Args:
            error_code (int): Error code to look up
            
        Returns:
            dict: Error information or None if not found
        """
        return self.error_codes.get(error_code)
    
    def get_error_message(self, error_code: int) -> str:
        """
        Get the error message for an error code.
        
        Args:
            error_code (int): Error code
            
        Returns:
            str: Error message or generic message if not found
        """
        error_info = self.get_error_info(error_code)
        return error_info['message'] if error_info else f"Unknown error code: {error_code}"
    
    def get_error_severity(self, error_code: int) -> ErrorSeverity:
        """
        Get the severity level of an error code.
        
        Args:
            error_code (int): Error code
            
        Returns:
            ErrorSeverity: Severity level
        """
        error_info = self.get_error_info(error_code)
        return error_info['severity'] if error_info else ErrorSeverity.ERROR
    
    def get_error_category(self, error_code: int) -> ErrorCategory:
        """
        Get the category of an error code.
        
        Args:
            error_code (int): Error code
            
        Returns:
            ErrorCategory: Error category
        """
        error_info = self.get_error_info(error_code)
        return error_info['category'] if error_info else ErrorCategory.SYSTEM
    
    def get_recommended_action(self, error_code: int) -> str:
        """
        Get the recommended action for an error code.
        
        Args:
            error_code (int): Error code
            
        Returns:
            str: Recommended action
        """
        error_info = self.get_error_info(error_code)
        return error_info['action'] if error_info else "Contact support for assistance"
    
    def is_critical_error(self, error_code: int) -> bool:
        """
        Check if an error code represents a critical error.
        
        Args:
            error_code (int): Error code
            
        Returns:
            bool: True if error is critical
        """
        severity = self.get_error_severity(error_code)
        return severity == ErrorSeverity.CRITICAL
    
    def is_recoverable_error(self, error_code: int) -> bool:
        """
        Check if an error code represents a recoverable error.
        
        Args:
            error_code (int): Error code
            
        Returns:
            bool: True if error is recoverable
        """
        # Critical errors and some specific errors are not easily recoverable
        if self.is_critical_error(error_code):
            return False
        
        # Connection errors are often recoverable
        category = self.get_error_category(error_code)
        if category == ErrorCategory.CONNECTION:
            return error_code not in [501, 502, 503]  # These require manual intervention
        
        return True
    
    def get_errors_by_category(self, category: ErrorCategory) -> Dict[int, Dict]:
        """
        Get all error codes in a specific category.
        
        Args:
            category (ErrorCategory): Category to filter by
            
        Returns:
            dict: Error codes in the category
        """
        return self.categories.get(category, {})
    
    def get_connection_errors(self) -> Dict[int, Dict]:
        """Get all connection-related error codes."""
        return self.get_errors_by_category(ErrorCategory.CONNECTION)
    
    def get_order_errors(self) -> Dict[int, Dict]:
        """Get all order-related error codes."""
        return self.get_errors_by_category(ErrorCategory.ORDER)
    
    def get_data_errors(self) -> Dict[int, Dict]:
        """Get all data-related error codes."""
        return self.get_errors_by_category(ErrorCategory.DATA)
    
    def format_error_report(self, error_code: int, error_string: str = "", req_id: int = -1) -> str:
        """
        Format a comprehensive error report.
        
        Args:
            error_code (int): Error code
            error_string (str): Additional error message
            req_id (int): Request ID associated with error
            
        Returns:
            str: Formatted error report
        """
        error_info = self.get_error_info(error_code)
        
        if not error_info:
            return f"Unknown Error {error_code}: {error_string}"
        
        report_lines = [
            f"Error {error_code}: {error_info['message']}",
            f"Category: {error_info['category'].value}",
            f"Severity: {error_info['severity'].value}",
            f"Description: {error_info['description']}"
        ]
        
        if error_string:
            report_lines.append(f"Additional Info: {error_string}")
        
        if req_id >= 0:
            report_lines.append(f"Request ID: {req_id}")
        
        report_lines.append(f"Recommended Action: {error_info['action']}")
        
        return "\n".join(report_lines)
    
    def print_error_reference(self, category: Optional[ErrorCategory] = None):
        """
        Print a reference of error codes.
        
        Args:
            category (ErrorCategory, optional): Specific category to print
        """
        if category:
            errors = self.get_errors_by_category(category)
            print(f"=== {category.value.upper()} ERRORS ===")
        else:
            errors = self.error_codes
            print("=== TWS API ERROR CODES REFERENCE ===")
        
        for error_code, error_info in sorted(errors.items()):
            print(f"\n{error_code}: {error_info['message']}")
            print(f"  Category: {error_info['category'].value}")
            print(f"  Severity: {error_info['severity'].value}")
            print(f"  Action: {error_info['action']}")


def demonstrate_error_codes():
    """
    Demonstrate error code functionality and reference information.
    """
    print("=== Error Codes Demo ===")
    
    error_codes = TWSErrorCodes()
    
    # Show examples of different error types
    example_errors = [200, 354, 501, 1100, 2104]
    
    print("Example Error Code Analysis:")
    for error_code in example_errors:
        print(f"\n--- Error {error_code} ---")
        info = error_codes.get_error_info(error_code)
        if info:
            print(f"Message: {info['message']}")
            print(f"Category: {info['category'].value}")
            print(f"Severity: {info['severity'].value}")
            print(f"Critical: {error_codes.is_critical_error(error_code)}")
            print(f"Recoverable: {error_codes.is_recoverable_error(error_code)}")
            print(f"Action: {info['action']}")
    
    print("\n" + "="*50)
    
    # Show error categories
    print("\nError Categories Summary:")
    for category in ErrorCategory:
        errors = error_codes.get_errors_by_category(category)
        print(f"  {category.value}: {len(errors)} error codes")
    
    print("\n" + "="*50)
    
    # Demonstrate error report formatting
    print("\nSample Error Reports:")
    
    sample_errors = [
        (200, "Contract 'AAPL STK SMART USD' not found", 9001),
        (1100, "", -1),
        (354, "Market data for TSLA", 9002)
    ]
    
    for error_code, error_string, req_id in sample_errors:
        print(f"\n{error_codes.format_error_report(error_code, error_string, req_id)}")
        print("-" * 40)


if __name__ == "__main__":
    """
    Main execution block for error codes demonstrations.
    """
    print("TWS API Error Codes Reference")
    print("=" * 35)
    
    # Run the demonstration
    demonstrate_error_codes()
    
    # Optionally print full reference
    print("\n" + "="*60)
    response = input("Print complete error codes reference? (y/n): ")
    if response.lower() == 'y':
        error_codes = TWSErrorCodes()
        error_codes.print_error_reference()
