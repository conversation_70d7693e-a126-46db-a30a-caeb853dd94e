"""
Contract Validation and Ambiguity Resolution

This module demonstrates how to validate contract definitions and resolve
contract ambiguities in the TWS API. Proper contract validation prevents
errors and ensures successful API operations.

Key Topics Covered:
- Contract field validation and requirements
- Handling contract ambiguity errors
- Resolving multiple contract matches
- Best practices for contract specification
- Error prevention and troubleshooting
- Contract uniqueness verification

Contract validation is essential for reliable API operations and helps
prevent common errors related to incomplete or ambiguous contract definitions.
"""

import logging
from typing import List, Dict, Optional, Tuple, Any
from ibapi.contract import Contract


class ContractValidator:
    """
    Utility class for validating contract definitions and resolving ambiguities.
    
    This class provides comprehensive validation methods for contract objects
    and helps identify potential issues before sending requests to TWS.
    
    Attributes:
        logger: Logger instance for validation messages
        validation_rules: Dictionary of validation rules by security type
    """
    
    def __init__(self):
        """Initialize the contract validator."""
        self.logger = logging.getLogger(__name__)
        
        # Define validation rules for different security types
        self.validation_rules = {
            'STK': {
                'required': ['symbol', 'secType', 'exchange', 'currency'],
                'recommended': ['primaryExchange'],
                'optional': ['conId', 'localSymbol', 'tradingClass']
            },
            'OPT': {
                'required': ['symbol', 'secType', 'exchange', 'currency', 
                           'lastTradeDateOrContractMonth', 'strike', 'right'],
                'recommended': ['multiplier', 'tradingClass'],
                'optional': ['conId', 'localSymbol']
            },
            'FUT': {
                'required': ['symbol', 'secType', 'exchange', 'currency', 
                           'lastTradeDateOrContractMonth'],
                'recommended': ['multiplier'],
                'optional': ['conId', 'localSymbol', 'tradingClass']
            },
            'CASH': {
                'required': ['symbol', 'secType', 'exchange', 'currency'],
                'recommended': [],
                'optional': ['conId', 'localSymbol']
            },
            'IND': {
                'required': ['symbol', 'secType', 'exchange', 'currency'],
                'recommended': [],
                'optional': ['conId', 'localSymbol']
            },
            'BOND': {
                'required': ['symbol', 'secType', 'exchange', 'currency'],
                'recommended': [],
                'optional': ['conId', 'localSymbol']
            },
            'CFD': {
                'required': ['symbol', 'secType', 'exchange', 'currency'],
                'recommended': [],
                'optional': ['conId', 'localSymbol']
            }
        }
    
    def validate_contract(self, contract: Contract) -> Dict[str, Any]:
        """
        Perform comprehensive validation of a contract object.
        
        Args:
            contract (Contract): Contract to validate
            
        Returns:
            dict: Validation results with errors, warnings, and recommendations
        """
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'recommendations': [],
            'missing_required': [],
            'missing_recommended': [],
            'security_type': contract.secType,
            'validation_score': 0
        }
        
        # Check if contract has conId (most precise identification)
        if contract.conId and contract.conId > 0:
            validation_result['validation_score'] = 100
            validation_result['recommendations'].append("Using conId provides most precise contract identification")
            return validation_result
        
        # Validate based on security type
        if not contract.secType:
            validation_result['errors'].append("Security type (secType) is required")
            validation_result['is_valid'] = False
            return validation_result
        
        sec_type = contract.secType.upper()
        if sec_type not in self.validation_rules:
            validation_result['warnings'].append(f"Unknown security type: {sec_type}")
            sec_type = 'STK'  # Default to stock validation
        
        rules = self.validation_rules[sec_type]
        
        # Check required fields
        for field in rules['required']:
            value = getattr(contract, field, None)
            if not value:
                validation_result['errors'].append(f"Required field '{field}' is missing or empty")
                validation_result['missing_required'].append(field)
                validation_result['is_valid'] = False
        
        # Check recommended fields
        for field in rules['recommended']:
            value = getattr(contract, field, None)
            if not value:
                validation_result['recommendations'].append(f"Consider specifying '{field}' for better contract identification")
                validation_result['missing_recommended'].append(field)
        
        # Security type specific validations
        if sec_type == 'OPT':
            self._validate_option_contract(contract, validation_result)
        elif sec_type == 'FUT':
            self._validate_futures_contract(contract, validation_result)
        elif sec_type == 'CASH':
            self._validate_forex_contract(contract, validation_result)
        elif sec_type == 'STK':
            self._validate_stock_contract(contract, validation_result)
        
        # Calculate validation score
        validation_result['validation_score'] = self._calculate_validation_score(contract, validation_result)
        
        return validation_result
    
    def _validate_option_contract(self, contract: Contract, result: Dict):
        """Validate option-specific fields."""
        # Validate expiration date format
        if contract.lastTradeDateOrContractMonth:
            expiry = contract.lastTradeDateOrContractMonth
            if len(expiry) == 8 and expiry.isdigit():
                # YYYYMMDD format
                year = int(expiry[:4])
                month = int(expiry[4:6])
                day = int(expiry[6:8])
                
                if year < 2020 or year > 2030:
                    result['warnings'].append(f"Unusual expiration year: {year}")
                if month < 1 or month > 12:
                    result['errors'].append(f"Invalid expiration month: {month}")
                    result['is_valid'] = False
                if day < 1 or day > 31:
                    result['errors'].append(f"Invalid expiration day: {day}")
                    result['is_valid'] = False
            else:
                result['warnings'].append("Expiration date should be in YYYYMMDD format")
        
        # Validate strike price
        if contract.strike and contract.strike <= 0:
            result['errors'].append("Strike price must be positive")
            result['is_valid'] = False
        
        # Validate option right
        if contract.right:
            right = contract.right.upper()
            if right not in ['C', 'P', 'CALL', 'PUT']:
                result['errors'].append(f"Invalid option right: {contract.right}")
                result['is_valid'] = False
        
        # Validate multiplier
        if contract.multiplier and not contract.multiplier.isdigit():
            result['warnings'].append("Multiplier should be numeric")
    
    def _validate_futures_contract(self, contract: Contract, result: Dict):
        """Validate futures-specific fields."""
        # Validate expiration format (usually YYYYMM)
        if contract.lastTradeDateOrContractMonth:
            expiry = contract.lastTradeDateOrContractMonth
            if len(expiry) == 6 and expiry.isdigit():
                year = int(expiry[:4])
                month = int(expiry[4:6])
                
                if year < 2020 or year > 2030:
                    result['warnings'].append(f"Unusual expiration year: {year}")
                if month < 1 or month > 12:
                    result['errors'].append(f"Invalid expiration month: {month}")
                    result['is_valid'] = False
            elif len(expiry) == 8 and expiry.isdigit():
                # Some futures use YYYYMMDD format
                result['recommendations'].append("Futures typically use YYYYMM format for expiration")
    
    def _validate_forex_contract(self, contract: Contract, result: Dict):
        """Validate forex-specific fields."""
        # Common currency codes
        common_currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CHF', 'CAD', 'AUD', 'NZD']
        
        if contract.symbol and contract.symbol not in common_currencies:
            result['warnings'].append(f"Uncommon base currency: {contract.symbol}")
        
        if contract.currency and contract.currency not in common_currencies:
            result['warnings'].append(f"Uncommon quote currency: {contract.currency}")
        
        # Check for same base and quote currency
        if contract.symbol == contract.currency:
            result['errors'].append("Base and quote currencies cannot be the same")
            result['is_valid'] = False
    
    def _validate_stock_contract(self, contract: Contract, result: Dict):
        """Validate stock-specific fields."""
        # Check for common exchanges
        common_exchanges = ['SMART', 'NYSE', 'NASDAQ', 'ARCA', 'BATS']
        if contract.exchange and contract.exchange not in common_exchanges:
            result['recommendations'].append(f"Consider using SMART routing instead of {contract.exchange}")
        
        # Validate currency for different regions
        if contract.currency:
            if contract.primaryExchange in ['NYSE', 'NASDAQ', 'ARCA'] and contract.currency != 'USD':
                result['warnings'].append("US exchanges typically trade in USD")
    
    def _calculate_validation_score(self, contract: Contract, result: Dict) -> int:
        """Calculate a validation score (0-100) for the contract."""
        if not result['is_valid']:
            return 0
        
        score = 50  # Base score for valid contract
        
        # Add points for completeness
        if contract.conId:
            score += 50  # ConId is most precise
        else:
            # Points for having recommended fields
            sec_type = contract.secType.upper() if contract.secType else 'STK'
            if sec_type in self.validation_rules:
                recommended_fields = self.validation_rules[sec_type]['recommended']
                for field in recommended_fields:
                    if getattr(contract, field, None):
                        score += 10
        
        # Deduct points for warnings
        score -= len(result['warnings']) * 5
        
        return min(max(score, 0), 100)
    
    def suggest_contract_improvements(self, contract: Contract) -> List[str]:
        """
        Suggest improvements for contract specification.
        
        Args:
            contract (Contract): Contract to analyze
            
        Returns:
            list: List of improvement suggestions
        """
        suggestions = []
        validation = self.validate_contract(contract)
        
        # Add specific suggestions based on validation results
        if validation['missing_recommended']:
            for field in validation['missing_recommended']:
                if field == 'primaryExchange' and contract.secType == 'STK':
                    suggestions.append("Specify primaryExchange to avoid routing ambiguity")
                elif field == 'multiplier' and contract.secType in ['OPT', 'FUT']:
                    suggestions.append("Specify multiplier for precise contract identification")
                elif field == 'tradingClass':
                    suggestions.append("Specify tradingClass to distinguish between contract variants")
        
        # Exchange-specific suggestions
        if contract.exchange == 'SMART' and contract.secType in ['OPT', 'FUT']:
            suggestions.append("Consider using specific exchange instead of SMART for derivatives")
        
        # Security type specific suggestions
        if contract.secType == 'STK' and not contract.primaryExchange:
            suggestions.append("Add primaryExchange to prevent ambiguity with ADRs or multiple listings")
        
        return suggestions
    
    def compare_contracts(self, contract1: Contract, contract2: Contract) -> Dict[str, Any]:
        """
        Compare two contracts and identify differences.
        
        Args:
            contract1 (Contract): First contract
            contract2 (Contract): Second contract
            
        Returns:
            dict: Comparison results
        """
        comparison = {
            'are_identical': True,
            'differences': [],
            'similarities': [],
            'recommendation': ''
        }
        
        # Fields to compare
        fields_to_compare = [
            'conId', 'symbol', 'secType', 'exchange', 'primaryExchange', 'currency',
            'lastTradeDateOrContractMonth', 'strike', 'right', 'multiplier', 'tradingClass'
        ]
        
        for field in fields_to_compare:
            val1 = getattr(contract1, field, None)
            val2 = getattr(contract2, field, None)
            
            if val1 != val2:
                comparison['are_identical'] = False
                comparison['differences'].append({
                    'field': field,
                    'contract1_value': val1,
                    'contract2_value': val2
                })
            elif val1 is not None:  # Both have the same non-None value
                comparison['similarities'].append(field)
        
        # Generate recommendation
        if comparison['are_identical']:
            comparison['recommendation'] = "Contracts are identical"
        elif len(comparison['differences']) == 1:
            diff = comparison['differences'][0]
            comparison['recommendation'] = f"Contracts differ only in {diff['field']}"
        else:
            comparison['recommendation'] = f"Contracts have {len(comparison['differences'])} differences"
        
        return comparison


def demonstrate_contract_validation():
    """
    Demonstrate contract validation functionality with examples.
    """
    print("=== Contract Validation Demo ===")
    
    validator = ContractValidator()
    
    # Create test contracts with various issues
    test_contracts = []
    
    # Valid stock contract
    valid_stock = Contract()
    valid_stock.symbol = "AAPL"
    valid_stock.secType = "STK"
    valid_stock.exchange = "SMART"
    valid_stock.currency = "USD"
    valid_stock.primaryExchange = "NASDAQ"
    test_contracts.append(("Valid Stock Contract", valid_stock))
    
    # Invalid option contract (missing fields)
    invalid_option = Contract()
    invalid_option.symbol = "AAPL"
    invalid_option.secType = "OPT"
    invalid_option.exchange = "SMART"
    # Missing currency, expiry, strike, right
    test_contracts.append(("Invalid Option Contract", invalid_option))
    
    # Incomplete futures contract
    incomplete_future = Contract()
    incomplete_future.symbol = "ES"
    incomplete_future.secType = "FUT"
    incomplete_future.exchange = "CME"
    incomplete_future.currency = "USD"
    # Missing expiration
    test_contracts.append(("Incomplete Futures Contract", incomplete_future))
    
    # Contract with conId (most precise)
    conid_contract = Contract()
    conid_contract.conId = 265598
    conid_contract.exchange = "SMART"
    test_contracts.append(("Contract with ConId", conid_contract))
    
    # Validate each contract
    print("\n--- Contract Validation Results ---")
    for name, contract in test_contracts:
        print(f"\n{name}:")
        validation = validator.validate_contract(contract)
        
        print(f"  Valid: {validation['is_valid']}")
        print(f"  Score: {validation['validation_score']}/100")
        
        if validation['errors']:
            print(f"  Errors: {validation['errors']}")
        
        if validation['warnings']:
            print(f"  Warnings: {validation['warnings']}")
        
        if validation['recommendations']:
            print(f"  Recommendations: {validation['recommendations']}")
        
        # Show improvement suggestions
        suggestions = validator.suggest_contract_improvements(contract)
        if suggestions:
            print(f"  Suggestions: {suggestions}")
    
    # Demonstrate contract comparison
    print(f"\n--- Contract Comparison ---")
    contract1 = test_contracts[0][1]  # Valid stock
    contract2 = Contract()
    contract2.symbol = "AAPL"
    contract2.secType = "STK"
    contract2.exchange = "NYSE"  # Different exchange
    contract2.currency = "USD"
    
    comparison = validator.compare_contracts(contract1, contract2)
    print(f"Contracts identical: {comparison['are_identical']}")
    print(f"Differences: {comparison['differences']}")
    print(f"Recommendation: {comparison['recommendation']}")


if __name__ == "__main__":
    """
    Main execution block for contract validation demonstrations.
    """
    print("TWS API Contract Validation Examples")
    print("=" * 45)
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Run the demonstration
    demonstrate_contract_validation()
