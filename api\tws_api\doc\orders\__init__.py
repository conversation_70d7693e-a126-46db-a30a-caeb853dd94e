"""
TWS API Orders and Order Management Documentation

This module provides comprehensive documentation and examples for order
placement, management, and monitoring in the Interactive Brokers TWS API.
Order management is a core functionality for trading applications.

The orders module covers:
- order_placement: Creating and placing different types of orders
- order_management: Managing active orders, modifications, and cancellations
- order_types: Examples of various order types (market, limit, stop, etc.)
- order_conditions: Conditional orders and advanced order features
- order_status: Order status tracking and execution monitoring
- bracket_orders: Parent-child order relationships and bracket strategies

Key Concepts:
- Order Objects: Core structure for defining trading instructions
- Order Types: Market, limit, stop, stop-limit, and advanced order types
- Order Status: Tracking order lifecycle from submission to execution
- Order Modifications: Changing order parameters after submission
- Executions: Trade confirmations and execution details
- Commission Reports: Commission and fee information
- Order Conditions: Complex conditional logic for order execution

Usage:
    from api.tws_api.doc.orders import order_placement
    from api.tws_api.doc.orders import order_management
    from api.tws_api.doc.orders import order_types
    # ... import other modules as needed

Important Notes:
- Orders require valid contracts and proper account permissions
- Order IDs must be unique and obtained from nextValidId
- Some order types require specific account configurations
- Paper trading and live trading have different behaviors
- Order modifications may not always be accepted by exchanges
"""

# Import all order modules for easy access
from . import order_placement
from . import order_management
from . import order_types
from . import order_conditions
from . import order_status
from . import bracket_orders

__all__ = [
    'order_placement',
    'order_management',
    'order_types',
    'order_conditions',
    'order_status',
    'bracket_orders'
]
