# Interactive Brokers TWS API Connectivity Documentation

## Overview

The Interactive Brokers TWS API connectivity functionality enables Python applications to establish, maintain, and manage connections with the Trader Workstation (TWS) or IB Gateway. This documentation provides comprehensive guidance on implementing robust connectivity solutions using the Python TWS API.

A socket connection between the API client application and TWS is established through the EClient interface. TWS acts as a server to receive requests from the API application (the client) and responds by taking appropriate actions. Each TWS session can receive up to 32 different client applications simultaneously, with the client ID field used to distinguish different API clients.

## Establishing an API Connection

### Basic Connection Setup

To establish a connection with the TWS API, you need to create a class that inherits from both `EClient` and `EWrapper`, then use the `connect()` method to establish the socket connection.

```python
from ibapi.client import EClient
from ibapi.wrapper import EWrapper
import threading
import time

class IBApp(EClient, EWrapper):
    def __init__(self):
        EClient.__init__(self, self)
        self.nextValidOrderId = None
        self.connected = False

    def connectAck(self):
        """Callback signifying completion of successful connection"""
        print("API Connection Established.")
        self.connected = True

    def nextValidId(self, orderId):
        """Called when connection is established and provides next valid order ID"""
        self.nextValidOrderId = orderId
        print(f"Connected. Next valid order ID: {orderId}")
        # Connection is ready - you can start making requests here

# Create and connect the application
app = IBApp()
app.connect("127.0.0.1", 7497, clientId=0)

# Start the message processing loop in a separate thread
api_thread = threading.Thread(target=app.run, daemon=True)
api_thread.start()

# Wait for connection to be established
while not app.connected:
    time.sleep(0.1)
```

### Connection Parameters

The `connect()` method requires three essential parameters:

- **host** (str): The IP address of the machine running TWS/IB Gateway
  - Use `"127.0.0.1"` for local connections
  - Use the actual IP address for remote connections
- **port** (int): The socket port configured in TWS/IB Gateway
  - TWS Paper Trading: 7497
  - TWS Live Trading: 7496
  - IB Gateway Paper Trading: 4002
  - IB Gateway Live Trading: 4001
- **clientId** (int): Unique identifier for your application
  - Must be unique across all connected API clients
  - Range: 0-31 (32 total client connections supported)

### Connection Options

You can set additional connection options using `setConnectOptions()` before connecting:

```python
# Enable pacing API to handle message rate limiting
app.setConnectOptions("+PACEAPI")
app.connect("127.0.0.1", 7497, clientId=0)
```

### Connection Process Flow

1. **Socket Creation**: The API requests a TCP socket to the specified IP and port
2. **Initial Handshake**: Version negotiation between TWS and API client
3. **Authentication**: TWS validates the connection and client ID
4. **Session Data**: TWS provides account numbers, next valid order ID, and connection time
5. **Ready State**: The `nextValidId()` callback indicates the connection is ready for requests

### Error Handling During Connection

Common connection errors and their resolutions:

```python
def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
    """Handle connection and other errors"""
    print(f"Error {reqId} {errorCode} {errorString}")

    # Handle specific connection error codes
    if errorCode == 502:
        print("Couldn't connect to TWS. Check:")
        print("1. TWS/IB Gateway is running")
        print("2. API connections are enabled in TWS settings")
        print("3. Correct port number is being used")
        print("4. No firewall blocking the connection")
    elif errorCode == 504:
        print("Not connected - connection was lost or never established")
        self.connected = False
    elif errorCode == 1100:
        print("Connectivity between IB and TWS has been lost")
        self.connected = False
    elif errorCode == 1101:
        print("Connectivity restored - data lost, need to re-subscribe")
        self.connected = True
    elif errorCode == 1102:
        print("Connectivity restored - data maintained")
        self.connected = True
```

## Verify API Connection

### Connection Status Checking

You can verify the connection status at any time using the `isConnected()` method:

```python
# Check if currently connected
if app.isConnected():
    print("API is connected to TWS")
    # Safe to make API requests
    app.reqCurrentTime()
else:
    print("API is not connected")
    # Need to reconnect before making requests
```

### Connection Information

Retrieve connection details and server information:

```python
class IBApp(EClient, EWrapper):
    def __init__(self):
        EClient.__init__(self, self)
        self.server_version = None
        self.connection_time = None

    def connectAck(self):
        """Get connection details when connected"""
        self.server_version = self.serverVersion()
        self.connection_time = self.twsConnectionTime()
        print(f"Connected to TWS Server Version: {self.server_version}")
        print(f"Connection Time: {self.connection_time}")

    def currentTime(self, time):
        """Receive current server time - confirms connection is working"""
        print(f"Current server time: {time}")
```

### Heartbeat and Keep-Alive

Implement a heartbeat mechanism to monitor connection health:

```python
import time
import threading

class IBApp(EClient, EWrapper):
    def __init__(self):
        EClient.__init__(self, self)
        self.last_heartbeat = time.time()
        self.heartbeat_interval = 30  # seconds

    def start_heartbeat(self):
        """Start heartbeat monitoring"""
        def heartbeat_monitor():
            while self.isConnected():
                current_time = time.time()
                if current_time - self.last_heartbeat > self.heartbeat_interval:
                    # Request current time as heartbeat
                    self.reqCurrentTime()
                    self.last_heartbeat = current_time
                time.sleep(10)

        heartbeat_thread = threading.Thread(target=heartbeat_monitor, daemon=True)
        heartbeat_thread.start()

    def currentTime(self, time):
        """Update heartbeat timestamp"""
        self.last_heartbeat = time.time()
        print(f"Heartbeat received: {time}")
```

## The EReader Thread

### Understanding the EReader Architecture

The EReader thread is a critical component of the TWS API that handles message reading and processing. Python API programs use a two-thread design:

1. **Main Thread**: Sends messages to TWS and processes the message queue
2. **EReader Thread**: Reads incoming messages from the socket and adds them to a queue

```python
from ibapi.reader import EReader
import queue
import threading

class IBApp(EClient, EWrapper):
    def __init__(self):
        EClient.__init__(self, self)
        self.msg_queue = queue.Queue()
        self.reader = None

    def connect(self, host, port, clientId):
        """Enhanced connect method with EReader setup"""
        # Establish the socket connection
        super().connect(host, port, clientId)

        # Create and start the EReader thread
        self.reader = EReader(self.conn, self.msg_queue)
        self.reader.start()

    def run(self):
        """Main message processing loop"""
        while self.isConnected():
            try:
                # Process messages from the queue
                if not self.msg_queue.empty():
                    msg = self.msg_queue.get(timeout=1)
                    if msg is not None:
                        self.decoder.interpret(msg)
                else:
                    time.sleep(0.01)  # Small delay to prevent busy waiting
            except queue.Empty:
                pass
            except Exception as e:
                print(f"Error processing message: {e}")
                break
```

### Python Implementation Details

The Python implementation uses a simplified two-thread model compared to other languages:

```python
import threading
import time
from ibapi.client import EClient
from ibapi.wrapper import EWrapper

class TWSConnection(EClient, EWrapper):
    def __init__(self):
        EClient.__init__(self, self)
        self.connected_event = threading.Event()
        self.reader_thread = None
        self.main_thread = None

    def connectAck(self):
        """Signal that connection is established"""
        print("Connection acknowledged")
        self.connected_event.set()

    def connect_and_run(self, host="127.0.0.1", port=7497, clientId=0):
        """Connect and start the message processing threads"""
        # Connect to TWS
        self.connect(host, port, clientId)

        # Start the main processing thread
        self.main_thread = threading.Thread(target=self.run, daemon=True)
        self.main_thread.start()

        # Wait for connection to be established
        if self.connected_event.wait(timeout=10):
            print("Successfully connected to TWS")
            return True
        else:
            print("Failed to connect to TWS within timeout")
            return False

    def disconnect_and_cleanup(self):
        """Properly disconnect and cleanup threads"""
        if self.isConnected():
            self.disconnect()

        # Wait for threads to finish
        if self.main_thread and self.main_thread.is_alive():
            self.main_thread.join(timeout=5)
```

### Message Queue Management

Implement robust message queue handling with error recovery:

```python
import logging
from collections import deque

class RobustIBApp(EClient, EWrapper):
    def __init__(self):
        EClient.__init__(self, self)
        self.message_queue = deque()
        self.queue_lock = threading.Lock()
        self.processing = True

    def run(self):
        """Enhanced message processing with error handling"""
        while self.processing:
            try:
                if self.isConnected():
                    # Process pending messages
                    with self.queue_lock:
                        while self.message_queue:
                            try:
                                msg = self.message_queue.popleft()
                                self.decoder.interpret(msg)
                            except Exception as e:
                                logging.error(f"Error processing message: {e}")
                                continue

                    # Check for new messages
                    if not self.msg_queue.empty():
                        try:
                            msg = self.msg_queue.get_nowait()
                            with self.queue_lock:
                                self.message_queue.append(msg)
                        except queue.Empty:
                            pass
                else:
                    time.sleep(0.1)  # Wait if not connected

            except Exception as e:
                logging.error(f"Critical error in message loop: {e}")
                time.sleep(1)  # Prevent rapid error loops

    def stop_processing(self):
        """Stop the message processing loop"""
        self.processing = False
```

## Remote TWS API Connections with Trader Workstation

### Configuring Remote Connections

To connect to TWS running on a remote machine, you need to configure both the TWS settings and your Python application.

#### TWS Configuration for Remote Access

1. **Enable API Connections**: Go to TWS → Global Configuration → API → Settings
2. **Set Socket Port**: Configure the port (default 7497 for paper, 7496 for live)
3. **Configure Trusted IPs**: Add the IP addresses that should be allowed to connect
4. **Enable Socket Clients**: Check "Enable ActiveX and Socket Clients"

#### Python Remote Connection Setup

```python
class RemoteIBApp(EClient, EWrapper):
    def __init__(self):
        EClient.__init__(self, self)
        self.connection_params = {}

    def connect_remote(self, remote_host, port=7497, clientId=0, timeout=30):
        """Connect to remote TWS with enhanced error handling"""
        self.connection_params = {
            'host': remote_host,
            'port': port,
            'clientId': clientId
        }

        try:
            print(f"Attempting to connect to {remote_host}:{port}")
            self.connect(remote_host, port, clientId)

            # Wait for connection with timeout
            start_time = time.time()
            while not self.isConnected() and (time.time() - start_time) < timeout:
                time.sleep(0.1)

            if self.isConnected():
                print(f"Successfully connected to remote TWS at {remote_host}:{port}")
                return True
            else:
                print(f"Failed to connect to {remote_host}:{port} within {timeout} seconds")
                return False

        except Exception as e:
            print(f"Connection error: {e}")
            return False

    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """Enhanced error handling for remote connections"""
        print(f"Error {reqId} {errorCode} {errorString}")

        if errorCode == 502:
            print("Remote connection failed. Check:")
            print(f"1. TWS is running on {self.connection_params.get('host', 'remote host')}")
            print("2. API is enabled in TWS settings")
            print("3. Your IP is in the Trusted IPs list")
            print("4. No firewall blocking the connection")
            print(f"5. Port {self.connection_params.get('port', 'N/A')} is correct")

# Example usage
app = RemoteIBApp()
if app.connect_remote("*************", 7497, 1):
    # Start processing thread
    api_thread = threading.Thread(target=app.run, daemon=True)
    api_thread.start()
```

### Network Security Considerations

Implement secure remote connections with proper authentication and encryption:

```python
import ssl
import socket

class SecureIBApp(EClient, EWrapper):
    def __init__(self):
        EClient.__init__(self, self)
        self.use_ssl = False

    def connect_secure(self, host, port, clientId, use_ssl=False):
        """Connect with optional SSL encryption"""
        if use_ssl:
            # Note: TWS doesn't natively support SSL, this is for custom implementations
            print("Warning: SSL not natively supported by TWS API")

        # Validate connection parameters
        if not self._validate_connection_params(host, port, clientId):
            return False

        return self.connect(host, port, clientId)

    def _validate_connection_params(self, host, port, clientId):
        """Validate connection parameters before attempting connection"""
        # Validate host
        try:
            socket.inet_aton(host)  # Valid IP address
        except socket.error:
            try:
                socket.gethostbyname(host)  # Valid hostname
            except socket.gaierror:
                print(f"Invalid host: {host}")
                return False

        # Validate port
        if not (1 <= port <= 65535):
            print(f"Invalid port: {port}")
            return False

        # Validate client ID
        if not (0 <= clientId <= 31):
            print(f"Invalid client ID: {clientId} (must be 0-31)")
            return False

        return True
```

### Connection Pooling and Load Balancing

For applications requiring multiple connections, implement connection pooling:

```python
import random
from typing import List, Dict

class IBConnectionPool:
    def __init__(self):
        self.connections: Dict[int, EClient] = {}
        self.available_clients = list(range(32))  # Client IDs 0-31
        self.hosts = []

    def add_host(self, host: str, port: int):
        """Add a TWS host to the connection pool"""
        self.hosts.append({'host': host, 'port': port})

    def get_connection(self) -> tuple:
        """Get an available connection from the pool"""
        if not self.available_clients:
            raise Exception("No available client IDs")

        if not self.hosts:
            raise Exception("No hosts configured")

        # Select random host for load balancing
        host_config = random.choice(self.hosts)
        client_id = self.available_clients.pop(0)

        # Create new connection
        app = IBApp()
        if app.connect(host_config['host'], host_config['port'], client_id):
            self.connections[client_id] = app
            return client_id, app
        else:
            # Return client ID to available pool if connection failed
            self.available_clients.insert(0, client_id)
            raise Exception(f"Failed to connect to {host_config}")

    def release_connection(self, client_id: int):
        """Release a connection back to the pool"""
        if client_id in self.connections:
            self.connections[client_id].disconnect()
            del self.connections[client_id]
            self.available_clients.append(client_id)
            self.available_clients.sort()

# Example usage
pool = IBConnectionPool()
pool.add_host("*************", 7497)
pool.add_host("*************", 7497)

try:
    client_id, connection = pool.get_connection()
    # Use the connection
    connection.reqCurrentTime()
finally:
    pool.release_connection(client_id)
```

## Accepting an API Connection from TWS

### Reverse Connection Setup

In some scenarios, you may want TWS to initiate the connection to your Python application. This requires setting up your application as a server that accepts incoming connections.

```python
import socket
import threading
from ibapi.server import EServer
from ibapi.wrapper import EWrapper

class IBServerApp(EWrapper):
    def __init__(self, port=7497):
        self.server_port = port
        self.server_socket = None
        self.client_connections = {}
        self.running = False

    def start_server(self):
        """Start the server to accept TWS connections"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('localhost', self.server_port))
            self.server_socket.listen(5)
            self.running = True

            print(f"Server listening on port {self.server_port}")

            while self.running:
                try:
                    client_socket, address = self.server_socket.accept()
                    print(f"Connection from {address}")

                    # Handle client in separate thread
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket, address),
                        daemon=True
                    )
                    client_thread.start()

                except socket.error as e:
                    if self.running:
                        print(f"Socket error: {e}")

        except Exception as e:
            print(f"Server error: {e}")
        finally:
            self.stop_server()

    def handle_client(self, client_socket, address):
        """Handle individual client connections"""
        try:
            # Implement TWS protocol handling here
            # This is a simplified example
            while self.running:
                data = client_socket.recv(1024)
                if not data:
                    break

                # Process TWS messages
                self.process_tws_message(data, client_socket)

        except Exception as e:
            print(f"Client handling error: {e}")
        finally:
            client_socket.close()
            print(f"Connection to {address} closed")

    def process_tws_message(self, data, client_socket):
        """Process incoming TWS messages"""
        # Implement message processing logic
        # This would include parsing TWS protocol messages
        pass

    def stop_server(self):
        """Stop the server"""
        self.running = False
        if self.server_socket:
            self.server_socket.close()

# Example usage
server = IBServerApp(port=7498)
server_thread = threading.Thread(target=server.start_server, daemon=True)
server_thread.start()
```

### Bidirectional Communication

Implement bidirectional communication between TWS and your application:

```python
class BidirectionalIBApp(EClient, EWrapper):
    def __init__(self):
        EClient.__init__(self, self)
        self.server_mode = False
        self.client_connections = []

    def setup_bidirectional(self, host="127.0.0.1", port=7497, clientId=0, server_port=7498):
        """Setup both client and server connections"""
        # Connect as client to TWS
        client_success = self.connect(host, port, clientId)

        # Start server for incoming TWS connections
        if client_success:
            self.start_server(server_port)

        return client_success

    def start_server(self, port):
        """Start server for incoming connections"""
        def server_loop():
            server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            server_socket.bind(('localhost', port))
            server_socket.listen(1)

            while self.isConnected():
                try:
                    client_socket, address = server_socket.accept()
                    self.client_connections.append(client_socket)
                    print(f"Incoming connection from {address}")
                except:
                    break

            server_socket.close()

        server_thread = threading.Thread(target=server_loop, daemon=True)
        server_thread.start()

    def send_to_clients(self, message):
        """Send message to all connected clients"""
        for client_socket in self.client_connections[:]:  # Copy list to avoid modification during iteration
            try:
                client_socket.send(message.encode())
            except:
                self.client_connections.remove(client_socket)
```

## Logging into Multiple Applications

### Multi-Client Management

Interactive Brokers allows up to 32 simultaneous API connections per TWS session. Here's how to manage multiple client applications:

```python
class MultiClientManager:
    def __init__(self, host="127.0.0.1", port=7497):
        self.host = host
        self.port = port
        self.clients = {}
        self.max_clients = 32

    def create_client(self, client_id, app_name=""):
        """Create a new client connection"""
        if client_id in self.clients:
            raise ValueError(f"Client ID {client_id} already in use")

        if len(self.clients) >= self.max_clients:
            raise ValueError("Maximum number of clients reached")

        # Create new application instance
        app = IBApp()
        app.app_name = app_name or f"Client_{client_id}"

        # Connect to TWS
        if app.connect(self.host, self.port, client_id):
            self.clients[client_id] = app

            # Start processing thread
            thread = threading.Thread(target=app.run, daemon=True)
            thread.start()

            print(f"Client {client_id} ({app.app_name}) connected successfully")
            return app
        else:
            raise ConnectionError(f"Failed to connect client {client_id}")

    def disconnect_client(self, client_id):
        """Disconnect a specific client"""
        if client_id in self.clients:
            self.clients[client_id].disconnect()
            del self.clients[client_id]
            print(f"Client {client_id} disconnected")

    def disconnect_all(self):
        """Disconnect all clients"""
        for client_id in list(self.clients.keys()):
            self.disconnect_client(client_id)

    def get_client(self, client_id):
        """Get a specific client instance"""
        return self.clients.get(client_id)

    def list_clients(self):
        """List all connected clients"""
        return {cid: app.app_name for cid, app in self.clients.items()}

# Example usage
manager = MultiClientManager()

# Create multiple clients for different purposes
market_data_client = manager.create_client(0, "MarketDataClient")
order_client = manager.create_client(1, "OrderClient")
account_client = manager.create_client(2, "AccountClient")

# Use clients for specific purposes
market_data_client.reqMktData(1, contract, "", False, False, [])
order_client.placeOrder(order_id, contract, order)
account_client.reqAccountUpdates(True, account)
```

## Broken API Socket Connection

### Connection Recovery and Resilience

Implement robust connection recovery mechanisms to handle broken socket connections:

```python
import time
import logging
from enum import Enum

class ConnectionState(Enum):
    DISCONNECTED = 0
    CONNECTING = 1
    CONNECTED = 2
    RECONNECTING = 3

class ResilientIBApp(EClient, EWrapper):
    def __init__(self):
        EClient.__init__(self, self)
        self.connection_state = ConnectionState.DISCONNECTED
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 5  # seconds
        self.connection_params = {}
        self.subscriptions = {}  # Store active subscriptions for recovery

    def connect_with_retry(self, host, port, clientId, max_attempts=5):
        """Connect with automatic retry logic"""
        self.connection_params = {'host': host, 'port': port, 'clientId': clientId}
        self.max_reconnect_attempts = max_attempts

        return self._attempt_connection()

    def _attempt_connection(self):
        """Attempt to establish connection"""
        self.connection_state = ConnectionState.CONNECTING

        try:
            self.connect(
                self.connection_params['host'],
                self.connection_params['port'],
                self.connection_params['clientId']
            )

            # Wait for connection confirmation
            start_time = time.time()
            while (self.connection_state == ConnectionState.CONNECTING and
                   time.time() - start_time < 10):
                time.sleep(0.1)

            if self.connection_state == ConnectionState.CONNECTED:
                self.reconnect_attempts = 0
                self._restore_subscriptions()
                return True
            else:
                return self._handle_connection_failure()

        except Exception as e:
            logging.error(f"Connection attempt failed: {e}")
            return self._handle_connection_failure()

    def _handle_connection_failure(self):
        """Handle connection failure and retry if appropriate"""
        self.reconnect_attempts += 1

        if self.reconnect_attempts < self.max_reconnect_attempts:
            logging.info(f"Connection failed. Retrying in {self.reconnect_delay} seconds "
                        f"(attempt {self.reconnect_attempts}/{self.max_reconnect_attempts})")

            time.sleep(self.reconnect_delay)
            self.reconnect_delay = min(self.reconnect_delay * 2, 60)  # Exponential backoff

            return self._attempt_connection()
        else:
            logging.error("Maximum reconnection attempts reached")
            self.connection_state = ConnectionState.DISCONNECTED
            return False

    def connectAck(self):
        """Handle successful connection"""
        self.connection_state = ConnectionState.CONNECTED
        self.reconnect_delay = 5  # Reset delay
        logging.info("Connection established successfully")

    def connectionClosed(self):
        """Handle connection closure"""
        logging.warning("Connection closed by TWS")
        self.connection_state = ConnectionState.DISCONNECTED
        self._initiate_reconnection()

    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """Enhanced error handling with reconnection logic"""
        logging.error(f"Error {reqId} {errorCode} {errorString}")

        # Handle connection-related errors
        if errorCode in [502, 504, 1100]:  # Connection lost errors
            self.connection_state = ConnectionState.DISCONNECTED
            self._initiate_reconnection()
        elif errorCode in [1101, 1102]:  # Connection restored
            self.connection_state = ConnectionState.CONNECTED
            if errorCode == 1101:  # Data lost, need to resubscribe
                self._restore_subscriptions()

    def _initiate_reconnection(self):
        """Start reconnection process"""
        if self.connection_state != ConnectionState.RECONNECTING:
            self.connection_state = ConnectionState.RECONNECTING

            def reconnect_thread():
                time.sleep(2)  # Brief delay before reconnecting
                self._attempt_connection()

            threading.Thread(target=reconnect_thread, daemon=True).start()

    def _store_subscription(self, req_id, subscription_type, params):
        """Store subscription for recovery"""
        self.subscriptions[req_id] = {
            'type': subscription_type,
            'params': params
        }

    def _restore_subscriptions(self):
        """Restore all active subscriptions after reconnection"""
        logging.info("Restoring subscriptions after reconnection")

        for req_id, subscription in self.subscriptions.items():
            try:
                if subscription['type'] == 'market_data':
                    self.reqMktData(req_id, *subscription['params'])
                elif subscription['type'] == 'account_updates':
                    self.reqAccountUpdates(*subscription['params'])
                # Add more subscription types as needed

            except Exception as e:
                logging.error(f"Failed to restore subscription {req_id}: {e}")

    # Override request methods to store subscriptions
    def reqMktData(self, reqId, contract, genericTickList, snapshot, regulatorySnapshot, mktDataOptions):
        """Request market data with subscription storage"""
        self._store_subscription(reqId, 'market_data',
                               (contract, genericTickList, snapshot, regulatorySnapshot, mktDataOptions))
        super().reqMktData(reqId, contract, genericTickList, snapshot, regulatorySnapshot, mktDataOptions)
```

### Connection Monitoring and Diagnostics

Implement comprehensive connection monitoring:

```python
import psutil
import platform

class ConnectionMonitor:
    def __init__(self, app):
        self.app = app
        self.monitoring = False
        self.stats = {
            'connection_count': 0,
            'disconnection_count': 0,
            'last_connection_time': None,
            'total_uptime': 0,
            'error_count': 0
        }

    def start_monitoring(self):
        """Start connection monitoring"""
        self.monitoring = True

        def monitor_loop():
            while self.monitoring:
                self._check_connection_health()
                self._log_system_stats()
                time.sleep(30)  # Check every 30 seconds

        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()

    def _check_connection_health(self):
        """Check connection health and log statistics"""
        if self.app.isConnected():
            if self.stats['last_connection_time']:
                uptime = time.time() - self.stats['last_connection_time']
                self.stats['total_uptime'] += uptime

            # Check network connectivity
            self._check_network_connectivity()
        else:
            logging.warning("Connection health check: Not connected")

    def _check_network_connectivity(self):
        """Check network connectivity to TWS host"""
        try:
            host = self.app.connection_params.get('host', '127.0.0.1')
            port = self.app.connection_params.get('port', 7497)

            # Test socket connection
            test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            test_socket.settimeout(5)
            result = test_socket.connect_ex((host, port))
            test_socket.close()

            if result != 0:
                logging.warning(f"Network connectivity issue to {host}:{port}")

        except Exception as e:
            logging.error(f"Network connectivity check failed: {e}")

    def _log_system_stats(self):
        """Log system performance statistics"""
        try:
            # CPU and memory usage
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()

            # Network statistics
            network = psutil.net_io_counters()

            logging.info(f"System Stats - CPU: {cpu_percent}%, "
                        f"Memory: {memory.percent}%, "
                        f"Network Sent: {network.bytes_sent}, "
                        f"Network Recv: {network.bytes_recv}")

        except Exception as e:
            logging.error(f"Failed to collect system stats: {e}")

    def get_connection_stats(self):
        """Get connection statistics"""
        return self.stats.copy()

# Example usage with monitoring
app = ResilientIBApp()
monitor = ConnectionMonitor(app)

if app.connect_with_retry("127.0.0.1", 7497, 0):
    monitor.start_monitoring()

    # Start main processing
    api_thread = threading.Thread(target=app.run, daemon=True)
    api_thread.start()

    # Keep application running
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        app.disconnect()
        monitor.monitoring = False
```

## Best Practices and Troubleshooting

### Common Connection Issues and Solutions

1. **Error 502 - Couldn't connect to TWS**
   - Ensure TWS/IB Gateway is running
   - Check API settings are enabled
   - Verify correct port number
   - Check firewall settings

2. **Error 504 - Not connected**
   - Connection was lost or never established
   - Implement reconnection logic
   - Check network stability

3. **Error 1100 - Connectivity lost**
   - Temporary network issue
   - Wait for automatic reconnection
   - Implement subscription recovery

### Performance Optimization

```python
# Optimize message processing
class OptimizedIBApp(EClient, EWrapper):
    def __init__(self):
        EClient.__init__(self, self)
        self.message_buffer = []
        self.buffer_size = 100

    def run(self):
        """Optimized message processing with batching"""
        while self.isConnected():
            # Process messages in batches for better performance
            messages_processed = 0

            while not self.msg_queue.empty() and messages_processed < self.buffer_size:
                try:
                    msg = self.msg_queue.get_nowait()
                    self.message_buffer.append(msg)
                    messages_processed += 1
                except queue.Empty:
                    break

            # Process buffered messages
            for msg in self.message_buffer:
                try:
                    self.decoder.interpret(msg)
                except Exception as e:
                    logging.error(f"Message processing error: {e}")

            self.message_buffer.clear()
            time.sleep(0.001)  # Minimal delay
```

This comprehensive documentation covers all aspects of TWS API connectivity in Python, providing developers with the tools and knowledge needed to build robust, reliable trading applications.