"""
Position Data Retrieval and Monitoring

This module demonstrates how to request and receive position data from the
TWS API. Position data provides information about current holdings across
all accounts accessible to the user.

Key Topics Covered:
- Requesting position data for all accounts
- Handling position updates and changes
- Processing position-related information
- Managing position subscriptions
- Understanding position data structure
- Canceling position subscriptions
- Best practices for position monitoring

Position data is essential for portfolio management and provides real-time
information about current holdings, market values, and P&L calculations.
"""

import threading
import time
import logging
from typing import Dict, List, Optional, Callable
from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract


class PositionsApp(EClient, EWrapper):
    """
    TWS API application for position data retrieval and monitoring.
    
    This class demonstrates how to request, receive, and manage position
    data with comprehensive storage and event handling capabilities.
    
    Attributes:
        positions_data (dict): Storage for position information
        position_subscriptions (set): Set of active position subscriptions
        position_callbacks (dict): Callbacks for position events
        subscription_active (bool): Flag indicating if position subscription is active
    """
    
    def __init__(self):
        """Initialize the positions application."""
        EClient.__init__(self, self)
        
        # Data storage
        self.positions_data: Dict[str, Dict] = {}
        
        # Subscription management
        self.subscription_active = False
        
        # Callback management
        self.position_callbacks = {
            'position_update': [],
            'position_end': [],
            'position_multi_update': [],
            'position_multi_end': []
        }
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def connectAck(self):
        """Handle successful connection."""
        self.logger.info("Connected to TWS - Positions service ready")
    
    def nextValidId(self, orderId):
        """
        Receive next valid order ID.
        
        Args:
            orderId (int): Next valid order ID
        """
        self.logger.info(f"Connection ready. Next valid order ID: {orderId}")
    
    def position(self, account: str, contract: Contract, position: float, avgCost: float):
        """
        Receive position data for a specific account and contract.
        
        This method is called for each position held in the account,
        providing detailed information about the position.
        
        Args:
            account (str): Account identifier
            contract (Contract): Contract object for the position
            position (float): Position size (positive for long, negative for short)
            avgCost (float): Average cost of the position
        """
        # Create position key from contract details
        position_key = f"{account}_{contract.symbol}_{contract.secType}_{contract.exchange}_{contract.currency}"
        
        # Store position data
        self.positions_data[position_key] = {
            'account': account,
            'contract': contract,
            'position': position,
            'avgCost': avgCost,
            'timestamp': time.time(),
            'symbol': contract.symbol,
            'secType': contract.secType,
            'exchange': contract.exchange,
            'currency': contract.currency,
            'conId': contract.conId
        }
        
        self.logger.info(f"Position Update - Account: {account}, "
                        f"Symbol: {contract.symbol}, Position: {position}, "
                        f"Avg Cost: {avgCost}")
        
        # Trigger callbacks
        for callback in self.position_callbacks['position_update']:
            try:
                callback(account, contract, position, avgCost)
            except Exception as e:
                self.logger.error(f"Position callback error: {e}")
    
    def positionEnd(self):
        """
        Handle end of position data transmission.
        
        This method is called when all position data has been transmitted
        for the current request.
        """
        self.logger.info("Position data transmission complete")
        
        # Trigger end callbacks
        for callback in self.position_callbacks['position_end']:
            try:
                callback()
            except Exception as e:
                self.logger.error(f"Position end callback error: {e}")
    
    def positionMulti(self, reqId: int, account: str, modelCode: str, 
                     contract: Contract, pos: float, avgCost: float):
        """
        Receive position data for multiple accounts (Financial Advisor feature).
        
        This method is used for Financial Advisor accounts to receive
        position data across multiple sub-accounts.
        
        Args:
            reqId (int): Request ID
            account (str): Account identifier
            modelCode (str): Model code for the position
            contract (Contract): Contract object
            pos (float): Position size
            avgCost (float): Average cost
        """
        # Create position key including model code
        position_key = f"{reqId}_{account}_{modelCode}_{contract.symbol}_{contract.secType}"
        
        # Store multi-position data
        self.positions_data[position_key] = {
            'reqId': reqId,
            'account': account,
            'modelCode': modelCode,
            'contract': contract,
            'position': pos,
            'avgCost': avgCost,
            'timestamp': time.time(),
            'symbol': contract.symbol,
            'secType': contract.secType,
            'exchange': contract.exchange,
            'currency': contract.currency,
            'conId': contract.conId,
            'type': 'multi'
        }
        
        self.logger.info(f"Position Multi Update - ReqId: {reqId}, Account: {account}, "
                        f"Model: {modelCode}, Symbol: {contract.symbol}, Position: {pos}")
        
        # Trigger callbacks
        for callback in self.position_callbacks['position_multi_update']:
            try:
                callback(reqId, account, modelCode, contract, pos, avgCost)
            except Exception as e:
                self.logger.error(f"Position multi callback error: {e}")
    
    def positionMultiEnd(self, reqId: int):
        """
        Handle end of multi-position data transmission.
        
        Args:
            reqId (int): Request ID for the completed multi-position request
        """
        self.logger.info(f"Position Multi data transmission complete - ReqId: {reqId}")
        
        # Trigger end callbacks
        for callback in self.position_callbacks['position_multi_end']:
            try:
                callback(reqId)
            except Exception as e:
                self.logger.error(f"Position multi end callback error: {e}")
    
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors related to position requests.
        
        Args:
            reqId (int): Request ID
            errorCode (int): Error code
            errorString (str): Error description
            advancedOrderRejectJson (str): Additional error details
        """
        self.logger.error(f"Error {reqId} {errorCode} {errorString}")
    
    def request_positions(self) -> bool:
        """
        Request position data for all accounts.
        
        This method requests position data for all accounts accessible
        to the user. Only one position subscription can be active at a time.
        
        Returns:
            bool: True if request was sent successfully
        """
        if self.subscription_active:
            self.logger.warning("Position subscription already active")
            return False
        
        try:
            self.reqPositions()
            self.subscription_active = True
            
            self.logger.info("Position data requested")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to request positions: {e}")
            return False
    
    def cancel_positions(self) -> bool:
        """
        Cancel the active position subscription.
        
        Returns:
            bool: True if cancellation was sent successfully
        """
        if not self.subscription_active:
            self.logger.warning("No active position subscription to cancel")
            return False
        
        try:
            self.cancelPositions()
            self.subscription_active = False
            
            self.logger.info("Position subscription canceled")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cancel positions: {e}")
            return False
    
    def request_positions_multi(self, req_id: int, account: str, model_code: str = "") -> bool:
        """
        Request position data for multiple accounts (Financial Advisor feature).
        
        Args:
            req_id (int): Unique request identifier
            account (str): Account identifier
            model_code (str): Model code (empty for all models)
            
        Returns:
            bool: True if request was sent successfully
        """
        try:
            self.reqPositionsMulti(req_id, account, model_code)
            
            self.logger.info(f"Position Multi data requested - ReqId: {req_id}, "
                           f"Account: {account}, Model: {model_code}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to request positions multi: {e}")
            return False
    
    def cancel_positions_multi(self, req_id: int) -> bool:
        """
        Cancel a multi-position subscription.
        
        Args:
            req_id (int): Request ID to cancel
            
        Returns:
            bool: True if cancellation was sent successfully
        """
        try:
            self.cancelPositionsMulti(req_id)
            
            self.logger.info(f"Position Multi subscription canceled - ReqId: {req_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cancel positions multi: {e}")
            return False
    
    def get_positions_data(self, account: Optional[str] = None) -> Dict:
        """
        Get stored position data.
        
        Args:
            account (str, optional): Filter by specific account
            
        Returns:
            dict: Position data
        """
        if account is not None:
            # Filter positions by account
            filtered_positions = {}
            for key, position in self.positions_data.items():
                if position.get('account') == account:
                    filtered_positions[key] = position
            return filtered_positions
        else:
            return self.positions_data.copy()
    
    def get_position_by_symbol(self, symbol: str, account: Optional[str] = None) -> List[Dict]:
        """
        Get positions for a specific symbol.
        
        Args:
            symbol (str): Symbol to search for
            account (str, optional): Filter by specific account
            
        Returns:
            list: List of matching positions
        """
        matching_positions = []
        
        for position in self.positions_data.values():
            if position.get('symbol') == symbol:
                if account is None or position.get('account') == account:
                    matching_positions.append(position)
        
        return matching_positions
    
    def get_accounts_with_positions(self) -> List[str]:
        """
        Get list of accounts that have positions.
        
        Returns:
            list: List of account identifiers
        """
        accounts = set()
        for position in self.positions_data.values():
            if 'account' in position:
                accounts.add(position['account'])
        
        return list(accounts)
    
    def calculate_position_summary(self, account: Optional[str] = None) -> Dict:
        """
        Calculate summary statistics for positions.
        
        Args:
            account (str, optional): Filter by specific account
            
        Returns:
            dict: Position summary statistics
        """
        positions = self.get_positions_data(account)
        
        summary = {
            'total_positions': len(positions),
            'long_positions': 0,
            'short_positions': 0,
            'zero_positions': 0,
            'symbols': set(),
            'currencies': set(),
            'exchanges': set(),
            'security_types': set()
        }
        
        for position in positions.values():
            pos_size = position.get('position', 0)
            
            if pos_size > 0:
                summary['long_positions'] += 1
            elif pos_size < 0:
                summary['short_positions'] += 1
            else:
                summary['zero_positions'] += 1
            
            summary['symbols'].add(position.get('symbol', ''))
            summary['currencies'].add(position.get('currency', ''))
            summary['exchanges'].add(position.get('exchange', ''))
            summary['security_types'].add(position.get('secType', ''))
        
        # Convert sets to lists for JSON serialization
        summary['symbols'] = list(summary['symbols'])
        summary['currencies'] = list(summary['currencies'])
        summary['exchanges'] = list(summary['exchanges'])
        summary['security_types'] = list(summary['security_types'])
        
        return summary
    
    def add_position_callback(self, event_type: str, callback: Callable):
        """
        Add a callback for position events.
        
        Args:
            event_type (str): Type of event ('position_update', 'position_end', etc.)
            callback: Callback function to add
        """
        if event_type in self.position_callbacks:
            self.position_callbacks[event_type].append(callback)


def demonstrate_positions():
    """
    Demonstrate position data functionality with practical examples.
    """
    print("=== Positions Demo ===")
    
    app = PositionsApp()
    
    # Add callbacks for demonstration
    def on_position_update(account, contract, position, avg_cost):
        if position != 0:  # Only show non-zero positions
            print(f"Position: {account} - {contract.symbol} ({contract.secType}): "
                  f"{position} @ {avg_cost}")
    
    def on_position_end():
        print("Position data download complete")
    
    app.add_position_callback('position_update', on_position_update)
    app.add_position_callback('position_end', on_position_end)
    
    try:
        # Connect to TWS
        app.connect("127.0.0.1", 7497, 0)
        
        # Start processing thread
        api_thread = threading.Thread(target=app.run, daemon=True)
        api_thread.start()
        
        # Wait for connection
        time.sleep(3)
        
        if app.isConnected():
            print("Connected - requesting position data")
            
            # Request position data
            if app.request_positions():
                print("Position data requested successfully")
                
                # Wait for data
                time.sleep(10)
                
                # Display summary of received data
                positions_data = app.get_positions_data()
                summary = app.calculate_position_summary()
                
                print("\n--- Position Summary ---")
                print(f"Total positions: {summary['total_positions']}")
                print(f"Long positions: {summary['long_positions']}")
                print(f"Short positions: {summary['short_positions']}")
                print(f"Zero positions: {summary['zero_positions']}")
                print(f"Unique symbols: {len(summary['symbols'])}")
                print(f"Currencies: {summary['currencies']}")
                print(f"Security types: {summary['security_types']}")
                
                print("\n--- Non-Zero Positions ---")
                for key, position in positions_data.items():
                    if position['position'] != 0:
                        print(f"{position['account']}: {position['symbol']} "
                              f"({position['secType']}) = {position['position']} "
                              f"@ {position['avgCost']}")
                
                # Cancel subscription
                app.cancel_positions()
                print("Position subscription canceled")
        
        else:
            print("Failed to connect to TWS")
    
    except KeyboardInterrupt:
        print("\nShutting down...")
    
    finally:
        # Cancel subscription if active
        if app.subscription_active:
            app.cancel_positions()
        
        app.disconnect()


if __name__ == "__main__":
    """
    Main execution block for position demonstrations.
    """
    print("TWS API Positions Examples")
    print("=" * 30)
    
    # Run the demonstration
    demonstrate_positions()
