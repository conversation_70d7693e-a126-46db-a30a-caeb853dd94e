"""
Managed Accounts Information

This module demonstrates how to retrieve information about managed accounts
from the TWS API. Managed accounts are accounts that the user has access to
and can trade on behalf of.

Key Topics Covered:
- Requesting managed accounts information
- Handling managed accounts responses
- Understanding account access permissions
- Processing account lists and identifiers
- Best practices for multi-account management

Managed accounts information is automatically provided upon connection and
is essential for applications that need to work with multiple accounts.
"""

import threading
import time
import logging
from typing import List, Optional, Callable
from ibapi.client import EClient
from ibapi.wrapper import EWrapper


class ManagedAccountsApp(EClient, EWrapper):
    """
    TWS API application for managed accounts information retrieval.
    
    This class demonstrates how to receive and manage information about
    accounts that the user has access to trade.
    
    Attributes:
        managed_accounts (list): List of managed account identifiers
        account_callbacks (dict): Callbacks for account-related events
        accounts_received (bool): Flag indicating if accounts have been received
    """
    
    def __init__(self):
        """Initialize the managed accounts application."""
        EClient.__init__(self, self)
        
        # Data storage
        self.managed_accounts: List[str] = []
        self.accounts_received = False
        
        # Callback management
        self.account_callbacks = {
            'managed_accounts_received': [],
            'account_access_changed': []
        }
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def connectAck(self):
        """Handle successful connection."""
        self.logger.info("Connected to TWS - Managed Accounts service ready")
    
    def nextValidId(self, orderId):
        """
        Receive next valid order ID.
        
        Args:
            orderId (int): Next valid order ID
        """
        self.logger.info(f"Connection ready. Next valid order ID: {orderId}")
    
    def managedAccounts(self, accountsList: str):
        """
        Receive the list of managed accounts.
        
        This method is automatically called upon connection and provides
        a comma-separated list of account identifiers that the user can access.
        
        Args:
            accountsList (str): Comma-separated list of account identifiers
        """
        # Parse the accounts list
        if accountsList:
            self.managed_accounts = [acc.strip() for acc in accountsList.split(',') if acc.strip()]
        else:
            self.managed_accounts = []
        
        self.accounts_received = True
        
        self.logger.info(f"Managed Accounts received: {self.managed_accounts}")
        
        # Trigger callbacks
        for callback in self.account_callbacks['managed_accounts_received']:
            try:
                callback(self.managed_accounts)
            except Exception as e:
                self.logger.error(f"Managed accounts callback error: {e}")
    
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors related to account access.
        
        Args:
            reqId (int): Request ID
            errorCode (int): Error code
            errorString (str): Error description
            advancedOrderRejectJson (str): Additional error details
        """
        self.logger.error(f"Error {reqId} {errorCode} {errorString}")
        
        # Handle account access related errors
        if errorCode in [1100, 1101, 1102]:
            self.logger.warning("Account access may have changed due to connectivity issues")
            
            # Trigger access change callbacks
            for callback in self.account_callbacks['account_access_changed']:
                try:
                    callback(errorCode, errorString)
                except Exception as e:
                    self.logger.error(f"Account access callback error: {e}")
    
    def get_managed_accounts(self) -> List[str]:
        """
        Get the list of managed accounts.
        
        Returns:
            list: List of managed account identifiers
        """
        return self.managed_accounts.copy()
    
    def has_account_access(self, account: str) -> bool:
        """
        Check if the user has access to a specific account.
        
        Args:
            account (str): Account identifier to check
            
        Returns:
            bool: True if user has access to the account
        """
        return account in self.managed_accounts
    
    def get_primary_account(self) -> Optional[str]:
        """
        Get the primary (first) managed account.
        
        Returns:
            str: Primary account identifier or None if no accounts
        """
        return self.managed_accounts[0] if self.managed_accounts else None
    
    def get_account_count(self) -> int:
        """
        Get the number of managed accounts.
        
        Returns:
            int: Number of managed accounts
        """
        return len(self.managed_accounts)
    
    def is_multi_account(self) -> bool:
        """
        Check if the user has access to multiple accounts.
        
        Returns:
            bool: True if user has access to multiple accounts
        """
        return len(self.managed_accounts) > 1
    
    def wait_for_accounts(self, timeout: float = 10.0) -> bool:
        """
        Wait for managed accounts information to be received.
        
        Args:
            timeout (float): Maximum time to wait in seconds
            
        Returns:
            bool: True if accounts were received within timeout
        """
        start_time = time.time()
        
        while not self.accounts_received and (time.time() - start_time) < timeout:
            time.sleep(0.1)
        
        return self.accounts_received
    
    def validate_account_for_trading(self, account: str) -> bool:
        """
        Validate that an account can be used for trading operations.
        
        Args:
            account (str): Account identifier to validate
            
        Returns:
            bool: True if account is valid for trading
        """
        if not account:
            self.logger.error("Empty account identifier provided")
            return False
        
        if not self.accounts_received:
            self.logger.error("Managed accounts not yet received")
            return False
        
        if not self.has_account_access(account):
            self.logger.error(f"No access to account: {account}")
            return False
        
        return True
    
    def get_account_info(self) -> dict:
        """
        Get comprehensive account information.
        
        Returns:
            dict: Account information summary
        """
        return {
            'managed_accounts': self.managed_accounts.copy(),
            'account_count': self.get_account_count(),
            'is_multi_account': self.is_multi_account(),
            'primary_account': self.get_primary_account(),
            'accounts_received': self.accounts_received
        }
    
    def add_account_callback(self, event_type: str, callback: Callable):
        """
        Add a callback for account-related events.
        
        Args:
            event_type (str): Type of event ('managed_accounts_received', 'account_access_changed')
            callback: Callback function to add
        """
        if event_type in self.account_callbacks:
            self.account_callbacks[event_type].append(callback)


class AccountValidator:
    """
    Utility class for validating account operations.
    
    This class provides methods for validating account-related operations
    and ensuring proper account access before performing trading activities.
    """
    
    def __init__(self, managed_accounts_app: ManagedAccountsApp):
        """
        Initialize the account validator.
        
        Args:
            managed_accounts_app: ManagedAccountsApp instance
        """
        self.app = managed_accounts_app
        self.logger = logging.getLogger(__name__)
    
    def validate_account_for_orders(self, account: str) -> bool:
        """
        Validate account for order placement.
        
        Args:
            account (str): Account to validate
            
        Returns:
            bool: True if account is valid for orders
        """
        if not self.app.validate_account_for_trading(account):
            return False
        
        # Additional order-specific validations could be added here
        self.logger.info(f"Account {account} validated for order placement")
        return True
    
    def validate_account_for_data(self, account: str) -> bool:
        """
        Validate account for data requests.
        
        Args:
            account (str): Account to validate
            
        Returns:
            bool: True if account is valid for data requests
        """
        if not self.app.validate_account_for_trading(account):
            return False
        
        # Additional data-specific validations could be added here
        self.logger.info(f"Account {account} validated for data requests")
        return True
    
    def get_default_account(self) -> Optional[str]:
        """
        Get the default account for operations.
        
        Returns:
            str: Default account identifier or None
        """
        if self.app.get_account_count() == 1:
            return self.app.get_primary_account()
        elif self.app.get_account_count() > 1:
            self.logger.warning("Multiple accounts available - explicit account selection recommended")
            return self.app.get_primary_account()
        else:
            self.logger.error("No managed accounts available")
            return None


def demonstrate_managed_accounts():
    """
    Demonstrate managed accounts functionality with practical examples.
    """
    print("=== Managed Accounts Demo ===")
    
    app = ManagedAccountsApp()
    
    # Add callbacks for demonstration
    def on_accounts_received(accounts):
        print(f"Managed accounts received: {accounts}")
        print(f"Account count: {len(accounts)}")
        print(f"Multi-account setup: {len(accounts) > 1}")
    
    def on_access_changed(error_code, error_string):
        print(f"Account access changed: {error_code} - {error_string}")
    
    app.add_account_callback('managed_accounts_received', on_accounts_received)
    app.add_account_callback('account_access_changed', on_access_changed)
    
    try:
        # Connect to TWS
        app.connect("127.0.0.1", 7497, 0)
        
        # Start processing thread
        api_thread = threading.Thread(target=app.run, daemon=True)
        api_thread.start()
        
        # Wait for connection and accounts
        time.sleep(3)
        
        if app.isConnected():
            print("Connected - waiting for managed accounts...")
            
            # Wait for accounts to be received
            if app.wait_for_accounts(timeout=10):
                print("Managed accounts received successfully")
                
                # Display account information
                account_info = app.get_account_info()
                print("\n--- Account Information ---")
                for key, value in account_info.items():
                    print(f"{key}: {value}")
                
                # Demonstrate account validation
                validator = AccountValidator(app)
                
                print("\n--- Account Validation ---")
                for account in app.get_managed_accounts():
                    print(f"Account {account}:")
                    print(f"  Valid for orders: {validator.validate_account_for_orders(account)}")
                    print(f"  Valid for data: {validator.validate_account_for_data(account)}")
                
                # Show default account
                default_account = validator.get_default_account()
                print(f"\nDefault account: {default_account}")
                
                # Test account access
                print("\n--- Account Access Tests ---")
                test_accounts = app.get_managed_accounts() + ["INVALID123"]
                for test_account in test_accounts:
                    has_access = app.has_account_access(test_account)
                    print(f"Access to {test_account}: {has_access}")
            
            else:
                print("Failed to receive managed accounts within timeout")
        
        else:
            print("Failed to connect to TWS")
    
    except KeyboardInterrupt:
        print("\nShutting down...")
    
    finally:
        app.disconnect()


if __name__ == "__main__":
    """
    Main execution block for managed accounts demonstrations.
    """
    print("TWS API Managed Accounts Examples")
    print("=" * 40)
    
    # Run the demonstration
    demonstrate_managed_accounts()
