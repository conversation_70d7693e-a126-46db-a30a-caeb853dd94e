"""
Interactive Brokers TWS API Documentation System

This package contains comprehensive Python-based documentation for the Interactive Brokers
Trader Workstation (TWS) API. Each module provides working code examples with detailed
explanations and best practices.

The documentation is organized by topic areas:
- connectivity: Connection management, authentication, and socket handling
- market_data: Real-time and historical market data functionality
- orders: Order placement, management, and execution
- accounts: Account information and portfolio management
- contracts: Financial instrument definitions and contract details

Each topic contains multiple modules with practical, executable examples that developers
can use as starting points for their own applications.

Author: Interactive Brokers TWS API Documentation Team
Version: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "Interactive Brokers TWS API Documentation Team"

# Import main topic modules for easy access
from . import connectivity
from . import accounts
from . import contracts
from . import market_data
from . import orders
from . import news
from . import scanner
from . import error_handling
from . import financial_advisors
from . import bulletins
from . import event_trading

__all__ = [
    'connectivity',
    'accounts',
    'contracts',
    'market_data',
    'orders',
    'news',
    'scanner',
    'error_handling',
    'financial_advisors',
    'bulletins',
    'event_trading',
]
