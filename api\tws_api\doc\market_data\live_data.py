"""
Live Market Data Streaming

This module demonstrates how to request and receive real-time market data
from the TWS API. Live market data provides continuous updates of prices,
volumes, and other market information for financial instruments.

Key Topics Covered:
- Requesting real-time market data subscriptions
- Handling tick-by-tick data updates
- Processing different tick types and their meanings
- Managing market data subscriptions
- Understanding generic ticks and additional data fields
- Canceling market data subscriptions
- Best practices for live data handling

Live market data is essential for trading applications that need current
market information for decision making and order management.
"""

import threading
import time
import logging
from typing import Dict, List, Optional, Set, Callable
from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract
from ibapi.ticktype import TickType


class LiveMarketDataApp(EClient, EWrapper):
    """
    TWS API application for live market data streaming.
    
    This class demonstrates how to request, receive, and manage real-time
    market data subscriptions with comprehensive tick processing and storage.
    
    Attributes:
        market_data (dict): Storage for live market data by request ID
        active_subscriptions (dict): Active market data subscriptions
        tick_callbacks (dict): Callbacks for different tick types
        data_quality (dict): Data quality tracking for subscriptions
    """
    
    def __init__(self):
        """Initialize the live market data application."""
        EClient.__init__(self, self)
        
        # Data storage
        self.market_data: Dict[int, Dict] = {}
        self.active_subscriptions: Dict[int, Contract] = {}
        
        # Data quality tracking
        self.data_quality: Dict[int, str] = {}
        
        # Callback management
        self.tick_callbacks = {
            'price_tick': [],
            'size_tick': [],
            'string_tick': [],
            'generic_tick': [],
            'market_data_type': []
        }
        
        # Tick type mappings for easy reference
        self.tick_types = {
            0: 'BID_SIZE',
            1: 'BID',
            2: 'ASK',
            3: 'ASK_SIZE',
            4: 'LAST',
            5: 'LAST_SIZE',
            6: 'HIGH',
            7: 'LOW',
            8: 'VOLUME',
            9: 'CLOSE',
            10: 'BID_OPTION_COMPUTATION',
            11: 'ASK_OPTION_COMPUTATION',
            12: 'LAST_OPTION_COMPUTATION',
            13: 'MODEL_OPTION',
            14: 'OPEN',
            15: 'LOW_13_WEEK',
            16: 'HIGH_13_WEEK',
            17: 'LOW_26_WEEK',
            18: 'HIGH_26_WEEK',
            19: 'LOW_52_WEEK',
            20: 'HIGH_52_WEEK',
            21: 'AVG_VOLUME',
            22: 'OPEN_INTEREST',
            23: 'OPTION_HISTORICAL_VOL',
            24: 'OPTION_IMPLIED_VOL',
            25: 'OPTION_BID_EXCH',
            26: 'OPTION_ASK_EXCH',
            27: 'OPTION_CALL_OPEN_INTEREST',
            28: 'OPTION_PUT_OPEN_INTEREST',
            29: 'OPTION_CALL_VOLUME',
            30: 'OPTION_PUT_VOLUME',
            31: 'INDEX_FUTURE_PREMIUM',
            32: 'BID_EXCH',
            33: 'ASK_EXCH',
            34: 'AUCTION_VOLUME',
            35: 'AUCTION_PRICE',
            36: 'AUCTION_IMBALANCE',
            37: 'MARK_PRICE',
            38: 'BID_EFP_COMPUTATION',
            39: 'ASK_EFP_COMPUTATION',
            40: 'LAST_EFP_COMPUTATION',
            41: 'OPEN_EFP_COMPUTATION',
            42: 'HIGH_EFP_COMPUTATION',
            43: 'LOW_EFP_COMPUTATION',
            44: 'CLOSE_EFP_COMPUTATION',
            45: 'LAST_TIMESTAMP',
            46: 'SHORTABLE',
            47: 'FUNDAMENTAL_RATIOS',
            48: 'RT_VOLUME',
            49: 'HALTED',
            50: 'BID_YIELD',
            51: 'ASK_YIELD',
            52: 'LAST_YIELD',
            53: 'CUST_OPTION_COMPUTATION',
            54: 'TRADE_COUNT',
            55: 'TRADE_RATE',
            56: 'VOLUME_RATE',
            57: 'LAST_RTH_TRADE',
            58: 'RT_HISTORICAL_VOL',
            59: 'IB_DIVIDENDS',
            60: 'BOND_FACTOR_MULTIPLIER',
            61: 'REGULATORY_IMBALANCE',
            62: 'NEWS_TICK',
            63: 'SHORT_TERM_VOLUME_3_MIN',
            64: 'SHORT_TERM_VOLUME_5_MIN',
            65: 'SHORT_TERM_VOLUME_10_MIN',
            66: 'DELAYED_BID',
            67: 'DELAYED_ASK',
            68: 'DELAYED_LAST',
            69: 'DELAYED_BID_SIZE',
            70: 'DELAYED_ASK_SIZE',
            71: 'DELAYED_LAST_SIZE',
            72: 'DELAYED_HIGH',
            73: 'DELAYED_LOW',
            74: 'DELAYED_VOLUME',
            75: 'DELAYED_CLOSE',
            76: 'DELAYED_OPEN',
            77: 'RT_TRD_VOLUME',
            78: 'CREDITMAN_MARK_PRICE',
            79: 'CREDITMAN_SLOW_MARK_PRICE',
            80: 'DELAYED_BID_OPTION_COMPUTATION',
            81: 'DELAYED_ASK_OPTION_COMPUTATION',
            82: 'DELAYED_LAST_OPTION_COMPUTATION',
            83: 'DELAYED_MODEL_OPTION_COMPUTATION',
            84: 'LAST_EXCH',
            85: 'LAST_REG_TIME',
            86: 'FUTURES_OPEN_INTEREST',
            87: 'AVG_OPT_VOLUME',
            88: 'DELAYED_LAST_TIMESTAMP',
            89: 'SHORTABLE_SHARES'
        }
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def connectAck(self):
        """Handle successful connection."""
        self.logger.info("Connected to TWS - Live Market Data service ready")
    
    def nextValidId(self, orderId):
        """
        Receive next valid order ID.
        
        Args:
            orderId (int): Next valid order ID
        """
        self.logger.info(f"Connection ready. Next valid order ID: {orderId}")
    
    def tickPrice(self, reqId: int, tickType: int, price: float, attrib):
        """
        Receive price tick updates.
        
        Args:
            reqId (int): Request ID for the market data subscription
            tickType (int): Type of price tick
            price (float): Price value
            attrib: Tick attributes
        """
        # Initialize data structure if needed
        if reqId not in self.market_data:
            self.market_data[reqId] = {}
        
        tick_name = self.tick_types.get(tickType, f"UNKNOWN_{tickType}")
        
        # Store the price tick
        self.market_data[reqId][tick_name] = {
            'value': price,
            'timestamp': time.time(),
            'type': 'price'
        }
        
        self.logger.debug(f"Price Tick - ReqId: {reqId}, {tick_name}: {price}")
        
        # Trigger callbacks
        for callback in self.tick_callbacks['price_tick']:
            try:
                callback(reqId, tickType, price, attrib)
            except Exception as e:
                self.logger.error(f"Price tick callback error: {e}")
    
    def tickSize(self, reqId: int, tickType: int, size: int):
        """
        Receive size tick updates.
        
        Args:
            reqId (int): Request ID for the market data subscription
            tickType (int): Type of size tick
            size (int): Size value
        """
        # Initialize data structure if needed
        if reqId not in self.market_data:
            self.market_data[reqId] = {}
        
        tick_name = self.tick_types.get(tickType, f"UNKNOWN_{tickType}")
        
        # Store the size tick
        self.market_data[reqId][tick_name] = {
            'value': size,
            'timestamp': time.time(),
            'type': 'size'
        }
        
        self.logger.debug(f"Size Tick - ReqId: {reqId}, {tick_name}: {size}")
        
        # Trigger callbacks
        for callback in self.tick_callbacks['size_tick']:
            try:
                callback(reqId, tickType, size)
            except Exception as e:
                self.logger.error(f"Size tick callback error: {e}")
    
    def tickString(self, reqId: int, tickType: int, value: str):
        """
        Receive string tick updates.
        
        Args:
            reqId (int): Request ID for the market data subscription
            tickType (int): Type of string tick
            value (str): String value
        """
        # Initialize data structure if needed
        if reqId not in self.market_data:
            self.market_data[reqId] = {}
        
        tick_name = self.tick_types.get(tickType, f"UNKNOWN_{tickType}")
        
        # Store the string tick
        self.market_data[reqId][tick_name] = {
            'value': value,
            'timestamp': time.time(),
            'type': 'string'
        }
        
        self.logger.debug(f"String Tick - ReqId: {reqId}, {tick_name}: {value}")
        
        # Trigger callbacks
        for callback in self.tick_callbacks['string_tick']:
            try:
                callback(reqId, tickType, value)
            except Exception as e:
                self.logger.error(f"String tick callback error: {e}")
    
    def tickGeneric(self, reqId: int, tickType: int, value: float):
        """
        Receive generic tick updates.
        
        Args:
            reqId (int): Request ID for the market data subscription
            tickType (int): Type of generic tick
            value (float): Generic tick value
        """
        # Initialize data structure if needed
        if reqId not in self.market_data:
            self.market_data[reqId] = {}
        
        tick_name = self.tick_types.get(tickType, f"UNKNOWN_{tickType}")
        
        # Store the generic tick
        self.market_data[reqId][tick_name] = {
            'value': value,
            'timestamp': time.time(),
            'type': 'generic'
        }
        
        self.logger.debug(f"Generic Tick - ReqId: {reqId}, {tick_name}: {value}")
        
        # Trigger callbacks
        for callback in self.tick_callbacks['generic_tick']:
            try:
                callback(reqId, tickType, value)
            except Exception as e:
                self.logger.error(f"Generic tick callback error: {e}")
    
    def marketDataType(self, reqId: int, marketDataType: int):
        """
        Receive market data type updates.
        
        Args:
            reqId (int): Request ID
            marketDataType (int): Market data type (1=Live, 2=Frozen, 3=Delayed, 4=Delayed-Frozen)
        """
        data_type_names = {
            1: 'Live',
            2: 'Frozen',
            3: 'Delayed',
            4: 'Delayed-Frozen'
        }
        
        data_type_name = data_type_names.get(marketDataType, f"Unknown_{marketDataType}")
        self.data_quality[reqId] = data_type_name
        
        self.logger.info(f"Market Data Type - ReqId: {reqId}, Type: {data_type_name}")
        
        # Trigger callbacks
        for callback in self.tick_callbacks['market_data_type']:
            try:
                callback(reqId, marketDataType)
            except Exception as e:
                self.logger.error(f"Market data type callback error: {e}")
    
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors related to market data requests.
        
        Args:
            reqId (int): Request ID
            errorCode (int): Error code
            errorString (str): Error description
            advancedOrderRejectJson (str): Additional error details
        """
        self.logger.error(f"Error {reqId} {errorCode} {errorString}")
        
        # Handle market data specific errors
        if reqId in self.active_subscriptions:
            if errorCode in [200, 354]:  # No security definition, no market data permissions
                self.logger.error(f"Market data subscription {reqId} failed: {errorString}")
    
    def request_market_data(self, req_id: int, contract: Contract, 
                          generic_tick_list: str = "", snapshot: bool = False,
                          regulatory_snapshot: bool = False, 
                          market_data_options: List = None) -> bool:
        """
        Request live market data for a contract.
        
        Args:
            req_id (int): Unique request identifier
            contract (Contract): Contract to get market data for
            generic_tick_list (str): Comma-separated list of generic tick types
            snapshot (bool): True for snapshot, False for streaming
            regulatory_snapshot (bool): True for regulatory snapshot
            market_data_options (list): Additional market data options
            
        Returns:
            bool: True if request was sent successfully
        """
        if req_id in self.active_subscriptions:
            self.logger.error(f"Request ID {req_id} already has an active market data subscription")
            return False
        
        try:
            if market_data_options is None:
                market_data_options = []
            
            self.reqMktData(req_id, contract, generic_tick_list, snapshot, 
                          regulatory_snapshot, market_data_options)
            
            self.active_subscriptions[req_id] = contract
            
            self.logger.info(f"Requested market data - ReqId: {req_id}, "
                           f"Symbol: {contract.symbol}, Snapshot: {snapshot}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to request market data: {e}")
            return False
    
    def cancel_market_data(self, req_id: int) -> bool:
        """
        Cancel a market data subscription.
        
        Args:
            req_id (int): Request ID to cancel
            
        Returns:
            bool: True if cancellation was sent successfully
        """
        if req_id not in self.active_subscriptions:
            self.logger.warning(f"No active market data subscription found for request ID {req_id}")
            return False
        
        try:
            self.cancelMktData(req_id)
            del self.active_subscriptions[req_id]
            
            self.logger.info(f"Canceled market data subscription - ReqId: {req_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cancel market data: {e}")
            return False
    
    def get_market_data(self, req_id: Optional[int] = None) -> Dict:
        """
        Get stored market data.
        
        Args:
            req_id (int, optional): Specific request ID to retrieve
            
        Returns:
            dict: Market data
        """
        if req_id is not None:
            return self.market_data.get(req_id, {})
        else:
            return self.market_data.copy()
    
    def get_current_quote(self, req_id: int) -> Dict:
        """
        Get current bid/ask quote for a subscription.
        
        Args:
            req_id (int): Request ID
            
        Returns:
            dict: Current quote information
        """
        data = self.get_market_data(req_id)
        
        quote = {
            'bid': data.get('BID', {}).get('value'),
            'ask': data.get('ASK', {}).get('value'),
            'bid_size': data.get('BID_SIZE', {}).get('value'),
            'ask_size': data.get('ASK_SIZE', {}).get('value'),
            'last': data.get('LAST', {}).get('value'),
            'last_size': data.get('LAST_SIZE', {}).get('value'),
            'volume': data.get('VOLUME', {}).get('value'),
            'data_quality': self.data_quality.get(req_id, 'Unknown')
        }
        
        return quote
    
    def add_tick_callback(self, event_type: str, callback: Callable):
        """
        Add a callback for tick events.
        
        Args:
            event_type (str): Type of event ('price_tick', 'size_tick', etc.)
            callback: Callback function to add
        """
        if event_type in self.tick_callbacks:
            self.tick_callbacks[event_type].append(callback)


def demonstrate_live_market_data():
    """
    Demonstrate live market data functionality with practical examples.
    """
    print("=== Live Market Data Demo ===")
    
    app = LiveMarketDataApp()
    
    # Add callbacks for demonstration
    def on_price_tick(req_id, tick_type, price, attrib):
        tick_name = app.tick_types.get(tick_type, f"UNKNOWN_{tick_type}")
        if tick_type in [1, 2, 4]:  # BID, ASK, LAST
            print(f"Price Update: {tick_name} = {price}")
    
    def on_size_tick(req_id, tick_type, size):
        tick_name = app.tick_types.get(tick_type, f"UNKNOWN_{tick_type}")
        if tick_type in [0, 3, 5]:  # BID_SIZE, ASK_SIZE, LAST_SIZE
            print(f"Size Update: {tick_name} = {size}")
    
    def on_market_data_type(req_id, data_type):
        type_names = {1: 'Live', 2: 'Frozen', 3: 'Delayed', 4: 'Delayed-Frozen'}
        print(f"Data Type: {type_names.get(data_type, data_type)}")
    
    app.add_tick_callback('price_tick', on_price_tick)
    app.add_tick_callback('size_tick', on_size_tick)
    app.add_tick_callback('market_data_type', on_market_data_type)
    
    try:
        # Connect to TWS
        app.connect("127.0.0.1", 7497, 0)
        
        # Start processing thread
        api_thread = threading.Thread(target=app.run, daemon=True)
        api_thread.start()
        
        # Wait for connection
        time.sleep(3)
        
        if app.isConnected():
            print("Connected - requesting live market data")
            
            # Create sample contracts
            from doc.contracts.contract_creation import ContractBuilder
            builder = ContractBuilder()
            
            # Request market data for Apple stock
            aapl_contract = builder.create_stock_contract("AAPL")
            if app.request_market_data(9001, aapl_contract):
                print("AAPL market data requested")
            
            # Request market data for EUR/USD with generic ticks
            eurusd_contract = builder.create_forex_contract("EUR", "USD")
            if app.request_market_data(9002, eurusd_contract, "100,101,104"):
                print("EUR/USD market data requested with generic ticks")
            
            # Monitor for updates
            print("Monitoring market data for 15 seconds...")
            time.sleep(15)
            
            # Display current quotes
            print("\n--- Current Quotes ---")
            for req_id in [9001, 9002]:
                quote = app.get_current_quote(req_id)
                contract = app.active_subscriptions.get(req_id)
                if contract:
                    print(f"{contract.symbol}:")
                    print(f"  Bid: {quote['bid']} x {quote['bid_size']}")
                    print(f"  Ask: {quote['ask']} x {quote['ask_size']}")
                    print(f"  Last: {quote['last']} x {quote['last_size']}")
                    print(f"  Volume: {quote['volume']}")
                    print(f"  Data Quality: {quote['data_quality']}")
            
            # Cancel subscriptions
            for req_id in list(app.active_subscriptions.keys()):
                app.cancel_market_data(req_id)
            print("Market data subscriptions canceled")
        
        else:
            print("Failed to connect to TWS")
    
    except KeyboardInterrupt:
        print("\nShutting down...")
    
    finally:
        # Cancel all active subscriptions
        for req_id in list(app.active_subscriptions.keys()):
            app.cancel_market_data(req_id)
        
        app.disconnect()


if __name__ == "__main__":
    """
    Main execution block for live market data demonstrations.
    """
    print("TWS API Live Market Data Examples")
    print("=" * 40)
    
    # Run the demonstration
    demonstrate_live_market_data()
