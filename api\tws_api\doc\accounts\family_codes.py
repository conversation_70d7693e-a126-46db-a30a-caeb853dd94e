"""
Family Codes Management

This module demonstrates how to request and receive family codes from the
TWS API. Family codes are used to group related accounts and manage
account relationships within the Interactive Brokers system.

Key Topics Covered:
- Requesting family codes information
- Handling family codes responses
- Understanding account family relationships
- Processing family code data structures
- Managing family code subscriptions
- Best practices for family code usage

Family codes are particularly useful for institutional accounts and
Financial Advisor setups where account relationships need to be managed.
"""

import threading
import time
import logging
from typing import Dict, List, Optional, Callable
from ibapi.client import EClient
from ibapi.wrapper import EWrapper


class FamilyCodesApp(EClient, EWrapper):
    """
    TWS API application for family codes information retrieval.
    
    This class demonstrates how to request, receive, and manage family
    codes data with comprehensive storage and event handling.
    
    Attributes:
        family_codes_data (dict): Storage for family codes information
        active_family_requests (set): Set of active family code request IDs
        family_callbacks (dict): Callbacks for family code events
    """
    
    def __init__(self):
        """Initialize the family codes application."""
        EClient.__init__(self, self)
        
        # Data storage
        self.family_codes_data: Dict[int, List[Dict]] = {}
        
        # Request management
        self.active_family_requests: set = set()
        
        # Callback management
        self.family_callbacks = {
            'family_codes_received': [],
            'family_codes_end': []
        }
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def connectAck(self):
        """Handle successful connection."""
        self.logger.info("Connected to TWS - Family Codes service ready")
    
    def nextValidId(self, orderId):
        """
        Receive next valid order ID.
        
        Args:
            orderId (int): Next valid order ID
        """
        self.logger.info(f"Connection ready. Next valid order ID: {orderId}")
    
    def familyCodes(self, reqId: int, familyCodesList: List):
        """
        Receive family codes information.
        
        This method is called to provide family codes data in response
        to a family codes request.
        
        Args:
            reqId (int): Request ID for the family codes request
            familyCodesList (list): List of family code objects
        """
        # Initialize data structure if needed
        if reqId not in self.family_codes_data:
            self.family_codes_data[reqId] = []
        
        # Process and store family codes
        for family_code in familyCodesList:
            family_info = {
                'accountID': getattr(family_code, 'accountID', ''),
                'familyCodeStr': getattr(family_code, 'familyCodeStr', ''),
                'timestamp': time.time()
            }
            
            self.family_codes_data[reqId].append(family_info)
            
            self.logger.info(f"Family Code - ReqId: {reqId}, "
                           f"Account: {family_info['accountID']}, "
                           f"Family Code: {family_info['familyCodeStr']}")
        
        # Trigger callbacks
        for callback in self.family_callbacks['family_codes_received']:
            try:
                callback(reqId, familyCodesList)
            except Exception as e:
                self.logger.error(f"Family codes callback error: {e}")
    
    def familyCodesEnd(self, reqId: int):
        """
        Handle end of family codes data transmission.
        
        Args:
            reqId (int): Request ID for the completed family codes request
        """
        self.logger.info(f"Family Codes End - ReqId: {reqId}")
        
        # Remove from active requests
        self.active_family_requests.discard(reqId)
        
        # Trigger end callbacks
        for callback in self.family_callbacks['family_codes_end']:
            try:
                callback(reqId)
            except Exception as e:
                self.logger.error(f"Family codes end callback error: {e}")
    
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors related to family codes requests.
        
        Args:
            reqId (int): Request ID
            errorCode (int): Error code
            errorString (str): Error description
            advancedOrderRejectJson (str): Additional error details
        """
        self.logger.error(f"Error {reqId} {errorCode} {errorString}")
        
        # Handle family codes specific errors
        if reqId in self.active_family_requests:
            self.logger.error(f"Family codes request {reqId} error: {errorString}")
            self.active_family_requests.discard(reqId)
    
    def request_family_codes(self, req_id: int) -> bool:
        """
        Request family codes information.
        
        Args:
            req_id (int): Unique request identifier
            
        Returns:
            bool: True if request was sent successfully
        """
        if req_id in self.active_family_requests:
            self.logger.error(f"Request ID {req_id} already has an active family codes request")
            return False
        
        try:
            self.reqFamilyCodes(req_id)
            self.active_family_requests.add(req_id)
            
            self.logger.info(f"Requested family codes - ReqId: {req_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to request family codes: {e}")
            return False
    
    def get_family_codes_data(self, req_id: Optional[int] = None) -> Dict:
        """
        Get stored family codes data.
        
        Args:
            req_id (int, optional): Specific request ID to retrieve
            
        Returns:
            dict: Family codes data
        """
        if req_id is not None:
            return self.family_codes_data.get(req_id, [])
        else:
            return self.family_codes_data.copy()
    
    def get_family_code_for_account(self, account: str, req_id: Optional[int] = None) -> Optional[str]:
        """
        Get family code for a specific account.
        
        Args:
            account (str): Account identifier
            req_id (int, optional): Specific request ID to search
            
        Returns:
            str: Family code for the account or None if not found
        """
        if req_id is not None:
            req_ids = [req_id]
        else:
            req_ids = list(self.family_codes_data.keys())
        
        for rid in req_ids:
            if rid in self.family_codes_data:
                for family_info in self.family_codes_data[rid]:
                    if family_info['accountID'] == account:
                        return family_info['familyCodeStr']
        
        return None
    
    def get_accounts_by_family_code(self, family_code: str, req_id: Optional[int] = None) -> List[str]:
        """
        Get accounts that belong to a specific family code.
        
        Args:
            family_code (str): Family code to search for
            req_id (int, optional): Specific request ID to search
            
        Returns:
            list: List of account identifiers with the family code
        """
        accounts = []
        
        if req_id is not None:
            req_ids = [req_id]
        else:
            req_ids = list(self.family_codes_data.keys())
        
        for rid in req_ids:
            if rid in self.family_codes_data:
                for family_info in self.family_codes_data[rid]:
                    if family_info['familyCodeStr'] == family_code:
                        accounts.append(family_info['accountID'])
        
        return accounts
    
    def get_unique_family_codes(self, req_id: Optional[int] = None) -> List[str]:
        """
        Get list of unique family codes.
        
        Args:
            req_id (int, optional): Specific request ID to search
            
        Returns:
            list: List of unique family codes
        """
        family_codes = set()
        
        if req_id is not None:
            req_ids = [req_id]
        else:
            req_ids = list(self.family_codes_data.keys())
        
        for rid in req_ids:
            if rid in self.family_codes_data:
                for family_info in self.family_codes_data[rid]:
                    family_codes.add(family_info['familyCodeStr'])
        
        return list(family_codes)
    
    def get_family_summary(self, req_id: Optional[int] = None) -> Dict:
        """
        Get summary of family codes data.
        
        Args:
            req_id (int, optional): Specific request ID to analyze
            
        Returns:
            dict: Summary of family codes information
        """
        if req_id is not None:
            req_ids = [req_id]
        else:
            req_ids = list(self.family_codes_data.keys())
        
        summary = {
            'total_accounts': 0,
            'unique_family_codes': set(),
            'family_groups': {},
            'accounts_without_family': []
        }
        
        for rid in req_ids:
            if rid in self.family_codes_data:
                for family_info in self.family_codes_data[rid]:
                    account = family_info['accountID']
                    family_code = family_info['familyCodeStr']
                    
                    summary['total_accounts'] += 1
                    
                    if family_code:
                        summary['unique_family_codes'].add(family_code)
                        
                        if family_code not in summary['family_groups']:
                            summary['family_groups'][family_code] = []
                        summary['family_groups'][family_code].append(account)
                    else:
                        summary['accounts_without_family'].append(account)
        
        # Convert set to list
        summary['unique_family_codes'] = list(summary['unique_family_codes'])
        summary['family_code_count'] = len(summary['unique_family_codes'])
        
        return summary
    
    def add_family_callback(self, event_type: str, callback: Callable):
        """
        Add a callback for family codes events.
        
        Args:
            event_type (str): Type of event ('family_codes_received', 'family_codes_end')
            callback: Callback function to add
        """
        if event_type in self.family_callbacks:
            self.family_callbacks[event_type].append(callback)


def demonstrate_family_codes():
    """
    Demonstrate family codes functionality with practical examples.
    """
    print("=== Family Codes Demo ===")
    print("Note: Family codes are typically used with institutional or FA accounts")
    
    app = FamilyCodesApp()
    
    # Add callbacks for demonstration
    def on_family_codes_received(req_id, family_codes_list):
        print(f"Family codes received for request {req_id}: {len(family_codes_list)} entries")
        for family_code in family_codes_list:
            account_id = getattr(family_code, 'accountID', 'N/A')
            family_str = getattr(family_code, 'familyCodeStr', 'N/A')
            print(f"  Account: {account_id}, Family Code: {family_str}")
    
    def on_family_codes_end(req_id):
        print(f"Family codes transmission complete for request {req_id}")
    
    app.add_family_callback('family_codes_received', on_family_codes_received)
    app.add_family_callback('family_codes_end', on_family_codes_end)
    
    try:
        # Connect to TWS
        app.connect("127.0.0.1", 7497, 0)
        
        # Start processing thread
        api_thread = threading.Thread(target=app.run, daemon=True)
        api_thread.start()
        
        # Wait for connection
        time.sleep(3)
        
        if app.isConnected():
            print("Connected - requesting family codes")
            
            # Request family codes
            if app.request_family_codes(9001):
                print("Family codes requested successfully")
                
                # Wait for data
                time.sleep(10)
                
                # Display received data
                family_data = app.get_family_codes_data(9001)
                summary = app.get_family_summary(9001)
                
                print("\n--- Family Codes Summary ---")
                for key, value in summary.items():
                    if isinstance(value, list) and len(value) > 10:
                        print(f"{key}: {len(value)} items")
                    else:
                        print(f"{key}: {value}")
                
                # Show family groupings
                if summary['family_groups']:
                    print("\n--- Family Groupings ---")
                    for family_code, accounts in summary['family_groups'].items():
                        print(f"Family '{family_code}': {accounts}")
                
                # Test lookup functions
                unique_codes = app.get_unique_family_codes(9001)
                print(f"\nUnique family codes: {unique_codes}")
                
                if family_data:
                    # Test account lookup
                    first_account = family_data[0]['accountID']
                    family_for_account = app.get_family_code_for_account(first_account, 9001)
                    print(f"Family code for account {first_account}: {family_for_account}")
        
        else:
            print("Failed to connect to TWS")
    
    except KeyboardInterrupt:
        print("\nShutting down...")
    
    finally:
        app.disconnect()


if __name__ == "__main__":
    """
    Main execution block for family codes demonstrations.
    """
    print("TWS API Family Codes Examples")
    print("=" * 35)
    
    # Run the demonstration
    demonstrate_family_codes()
