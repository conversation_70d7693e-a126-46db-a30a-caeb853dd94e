"""
TWS API Account & Portfolio Data Documentation

This module provides comprehensive documentation and examples for retrieving
account and portfolio information from the Interactive Brokers TWS API.

The accounts module covers:
- account_summary: Account summary data and subscription management
- account_updates: Real-time account value updates and portfolio changes
- model_accounts: Account updates by model for multi-account structures
- family_codes: Family code management for account grouping
- managed_accounts: Managed account information and access
- positions: Position data retrieval and monitoring

Key Concepts:
- Account Summary: Aggregated account information similar to TWS Account Summary window
- Account Updates: Real-time updates of account values and portfolio positions
- Model Accounts: Support for Financial Advisor account models
- Family Codes: Account grouping and family relationship management
- Managed Accounts: Access to accounts managed by the user
- Positions: Current position information across all accounts

Usage:
    from doc.accounts import account_summary
    from doc.accounts import account_updates
    from doc.accounts import positions
    # ... import other modules as needed

Important Notes:
- Only two active account summary subscriptions are allowed at a time
- Account updates have a 3-minute update frequency (same as TWS)
- Some features require specific account permissions or configurations
- Financial Advisor accounts have additional capabilities and restrictions
"""

# Import all account modules for easy access
from . import account_summary
from . import account_updates
from . import model_accounts
from . import family_codes
from . import managed_accounts
from . import positions

__all__ = [
    'account_summary',
    'account_updates',
    'model_accounts',
    'family_codes',
    'managed_accounts',
    'positions'
]
