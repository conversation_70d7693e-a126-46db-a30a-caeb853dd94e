"""
Order Placement and Submission

This module demonstrates how to create and place orders using the TWS API.
Order placement is fundamental to trading applications and requires proper
order construction, validation, and submission procedures.

Key Topics Covered:
- Creating basic order objects for different order types
- Setting order parameters and attributes
- Validating orders before submission
- Placing orders with proper error handling
- Understanding order acknowledgments and confirmations
- Managing order IDs and sequencing
- Best practices for order placement

Order placement is the core functionality that enables trading through
the TWS API and requires careful attention to order construction and validation.
"""

import threading
import time
import logging
from typing import Dict, List, Optional, Callable
from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract
from ibapi.order import Order
from ibapi.order_state import OrderState


class OrderPlacementApp(EClient, EWrapper):
    """
    TWS API application for order placement and management.
    
    This class demonstrates how to create, validate, and place orders
    with comprehensive order tracking and status monitoring.
    
    Attributes:
        next_order_id (int): Next valid order ID from TWS
        placed_orders (dict): Storage for placed orders by order ID
        order_status (dict): Order status tracking
        executions (dict): Execution details by order ID
        order_callbacks (dict): Callbacks for order events
    """
    
    def __init__(self):
        """Initialize the order placement application."""
        EClient.__init__(self, self)
        
        # Order management
        self.next_order_id = None
        self.placed_orders: Dict[int, Dict] = {}
        self.order_status: Dict[int, str] = {}
        self.executions: Dict[int, List] = {}
        
        # Callback management
        self.order_callbacks = {
            'order_status': [],
            'open_order': [],
            'execution_details': [],
            'commission_report': []
        }
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def connectAck(self):
        """Handle successful connection."""
        self.logger.info("Connected to TWS - Order Placement service ready")
    
    def nextValidId(self, orderId: int):
        """
        Receive next valid order ID from TWS.
        
        Args:
            orderId (int): Next valid order ID
        """
        self.next_order_id = orderId
        self.logger.info(f"Next valid order ID: {orderId}")
    
    def orderStatus(self, orderId: int, status: str, filled: float, remaining: float,
                   avgFillPrice: float, permId: int, parentId: int, lastFillPrice: float,
                   clientId: int, whyHeld: str, mktCapPrice: float):
        """
        Receive order status updates.
        
        Args:
            orderId (int): Order ID
            status (str): Order status
            filled (float): Filled quantity
            remaining (float): Remaining quantity
            avgFillPrice (float): Average fill price
            permId (int): Permanent order ID
            parentId (int): Parent order ID
            lastFillPrice (float): Last fill price
            clientId (int): Client ID
            whyHeld (str): Reason why order is held
            mktCapPrice (float): Market cap price
        """
        self.order_status[orderId] = status
        
        # Update order information
        if orderId in self.placed_orders:
            self.placed_orders[orderId].update({
                'status': status,
                'filled': filled,
                'remaining': remaining,
                'avg_fill_price': avgFillPrice,
                'last_fill_price': lastFillPrice,
                'why_held': whyHeld,
                'last_update': time.time()
            })
        
        self.logger.info(f"Order Status - ID: {orderId}, Status: {status}, "
                        f"Filled: {filled}, Remaining: {remaining}, "
                        f"Avg Price: {avgFillPrice}")
        
        # Trigger callbacks
        for callback in self.order_callbacks['order_status']:
            try:
                callback(orderId, status, filled, remaining, avgFillPrice,
                        permId, parentId, lastFillPrice, clientId, whyHeld, mktCapPrice)
            except Exception as e:
                self.logger.error(f"Order status callback error: {e}")
    
    def openOrder(self, orderId: int, contract: Contract, order: Order, orderState: OrderState):
        """
        Receive open order information.
        
        Args:
            orderId (int): Order ID
            contract (Contract): Contract for the order
            order (Order): Order object
            orderState (OrderState): Order state information
        """
        self.logger.info(f"Open Order - ID: {orderId}, Symbol: {contract.symbol}, "
                        f"Action: {order.action}, Quantity: {order.totalQuantity}, "
                        f"Type: {order.orderType}")
        
        # Store order information
        self.placed_orders[orderId] = {
            'contract': contract,
            'order': order,
            'order_state': orderState,
            'timestamp': time.time()
        }
        
        # Trigger callbacks
        for callback in self.order_callbacks['open_order']:
            try:
                callback(orderId, contract, order, orderState)
            except Exception as e:
                self.logger.error(f"Open order callback error: {e}")
    
    def execDetails(self, reqId: int, contract: Contract, execution):
        """
        Receive execution details.
        
        Args:
            reqId (int): Request ID
            contract (Contract): Contract that was executed
            execution: Execution details
        """
        order_id = execution.orderId
        
        # Initialize executions list if needed
        if order_id not in self.executions:
            self.executions[order_id] = []
        
        # Store execution details
        execution_info = {
            'exec_id': execution.execId,
            'time': execution.time,
            'account': execution.acctNumber,
            'exchange': execution.exchange,
            'side': execution.side,
            'shares': execution.shares,
            'price': execution.price,
            'perm_id': execution.permId,
            'client_id': execution.clientId,
            'liquidation': execution.liquidation,
            'cum_qty': execution.cumQty,
            'avg_price': execution.avgPrice,
            'order_ref': execution.orderRef,
            'ev_rule': execution.evRule,
            'ev_multiplier': execution.evMultiplier,
            'model_code': execution.modelCode,
            'last_liquidity': execution.lastLiquidity
        }
        
        self.executions[order_id].append(execution_info)
        
        self.logger.info(f"Execution - Order ID: {order_id}, Exec ID: {execution.execId}, "
                        f"Shares: {execution.shares}, Price: {execution.price}")
        
        # Trigger callbacks
        for callback in self.order_callbacks['execution_details']:
            try:
                callback(reqId, contract, execution)
            except Exception as e:
                self.logger.error(f"Execution details callback error: {e}")
    
    def commissionReport(self, commissionReport):
        """
        Receive commission report.
        
        Args:
            commissionReport: Commission report details
        """
        self.logger.info(f"Commission Report - Exec ID: {commissionReport.execId}, "
                        f"Commission: {commissionReport.commission}, "
                        f"Currency: {commissionReport.currency}")
        
        # Trigger callbacks
        for callback in self.order_callbacks['commission_report']:
            try:
                callback(commissionReport)
            except Exception as e:
                self.logger.error(f"Commission report callback error: {e}")
    
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors related to order placement.
        
        Args:
            reqId (int): Request ID (order ID for order-related errors)
            errorCode (int): Error code
            errorString (str): Error description
            advancedOrderRejectJson (str): Additional error details
        """
        self.logger.error(f"Error {reqId} {errorCode} {errorString}")
        
        # Handle order-specific errors
        if reqId in self.placed_orders:
            self.logger.error(f"Order {reqId} error: {errorString}")
            self.order_status[reqId] = f"ERROR: {errorString}"
    
    def get_next_order_id(self) -> Optional[int]:
        """
        Get the next available order ID.
        
        Returns:
            int: Next order ID or None if not available
        """
        if self.next_order_id is None:
            self.logger.warning("Next order ID not yet received from TWS")
            return None
        
        order_id = self.next_order_id
        self.next_order_id += 1
        return order_id
    
    def create_market_order(self, action: str, quantity: float, account: str = "") -> Order:
        """
        Create a market order.
        
        Args:
            action (str): Order action ("BUY" or "SELL")
            quantity (float): Order quantity
            account (str): Account for the order (optional)
            
        Returns:
            Order: Configured market order
        """
        order = Order()
        order.action = action.upper()
        order.totalQuantity = quantity
        order.orderType = "MKT"
        
        if account:
            order.account = account
        
        return order
    
    def create_limit_order(self, action: str, quantity: float, limit_price: float,
                          account: str = "") -> Order:
        """
        Create a limit order.
        
        Args:
            action (str): Order action ("BUY" or "SELL")
            quantity (float): Order quantity
            limit_price (float): Limit price
            account (str): Account for the order (optional)
            
        Returns:
            Order: Configured limit order
        """
        order = Order()
        order.action = action.upper()
        order.totalQuantity = quantity
        order.orderType = "LMT"
        order.lmtPrice = limit_price
        
        if account:
            order.account = account
        
        return order
    
    def create_stop_order(self, action: str, quantity: float, stop_price: float,
                         account: str = "") -> Order:
        """
        Create a stop order.
        
        Args:
            action (str): Order action ("BUY" or "SELL")
            quantity (float): Order quantity
            stop_price (float): Stop price
            account (str): Account for the order (optional)
            
        Returns:
            Order: Configured stop order
        """
        order = Order()
        order.action = action.upper()
        order.totalQuantity = quantity
        order.orderType = "STP"
        order.auxPrice = stop_price
        
        if account:
            order.account = account
        
        return order
    
    def create_stop_limit_order(self, action: str, quantity: float, stop_price: float,
                               limit_price: float, account: str = "") -> Order:
        """
        Create a stop-limit order.
        
        Args:
            action (str): Order action ("BUY" or "SELL")
            quantity (float): Order quantity
            stop_price (float): Stop price
            limit_price (float): Limit price
            account (str): Account for the order (optional)
            
        Returns:
            Order: Configured stop-limit order
        """
        order = Order()
        order.action = action.upper()
        order.totalQuantity = quantity
        order.orderType = "STP LMT"
        order.auxPrice = stop_price
        order.lmtPrice = limit_price
        
        if account:
            order.account = account
        
        return order
    
    def validate_order(self, contract: Contract, order: Order) -> Dict:
        """
        Validate an order before placement.
        
        Args:
            contract (Contract): Contract for the order
            order (Order): Order to validate
            
        Returns:
            dict: Validation results
        """
        validation = {
            'is_valid': True,
            'errors': [],
            'warnings': []
        }
        
        # Validate contract
        if not contract.symbol:
            validation['errors'].append("Contract symbol is required")
            validation['is_valid'] = False
        
        if not contract.secType:
            validation['errors'].append("Contract security type is required")
            validation['is_valid'] = False
        
        # Validate order
        if not order.action:
            validation['errors'].append("Order action is required")
            validation['is_valid'] = False
        elif order.action not in ['BUY', 'SELL']:
            validation['errors'].append("Order action must be BUY or SELL")
            validation['is_valid'] = False
        
        if not order.totalQuantity or order.totalQuantity <= 0:
            validation['errors'].append("Order quantity must be positive")
            validation['is_valid'] = False
        
        if not order.orderType:
            validation['errors'].append("Order type is required")
            validation['is_valid'] = False
        
        # Order type specific validations
        if order.orderType == "LMT" and not order.lmtPrice:
            validation['errors'].append("Limit price required for limit orders")
            validation['is_valid'] = False
        
        if order.orderType in ["STP", "STP LMT"] and not order.auxPrice:
            validation['errors'].append("Stop price required for stop orders")
            validation['is_valid'] = False
        
        if order.orderType == "STP LMT" and not order.lmtPrice:
            validation['errors'].append("Limit price required for stop-limit orders")
            validation['is_valid'] = False
        
        return validation
    
    def place_order(self, contract: Contract, order: Order) -> Optional[int]:
        """
        Place an order after validation.
        
        Args:
            contract (Contract): Contract for the order
            order (Order): Order to place
            
        Returns:
            int: Order ID if successful, None if failed
        """
        # Get next order ID
        order_id = self.get_next_order_id()
        if order_id is None:
            self.logger.error("Cannot place order: No valid order ID available")
            return None
        
        # Validate order
        validation = self.validate_order(contract, order)
        if not validation['is_valid']:
            self.logger.error(f"Order validation failed: {validation['errors']}")
            return None
        
        try:
            # Place the order
            self.placeOrder(order_id, contract, order)
            
            # Store order information
            self.placed_orders[order_id] = {
                'contract': contract,
                'order': order,
                'timestamp': time.time(),
                'status': 'SUBMITTED'
            }
            
            self.logger.info(f"Order placed - ID: {order_id}, Symbol: {contract.symbol}, "
                           f"Action: {order.action}, Quantity: {order.totalQuantity}, "
                           f"Type: {order.orderType}")
            
            return order_id
            
        except Exception as e:
            self.logger.error(f"Failed to place order: {e}")
            return None
    
    def cancel_order(self, order_id: int) -> bool:
        """
        Cancel an order.
        
        Args:
            order_id (int): Order ID to cancel
            
        Returns:
            bool: True if cancellation request sent successfully
        """
        if order_id not in self.placed_orders:
            self.logger.warning(f"Order ID {order_id} not found in placed orders")
            return False
        
        try:
            self.cancelOrder(order_id)
            self.logger.info(f"Cancel request sent for order {order_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cancel order {order_id}: {e}")
            return False
    
    def get_order_status(self, order_id: int) -> Optional[str]:
        """
        Get the current status of an order.
        
        Args:
            order_id (int): Order ID
            
        Returns:
            str: Order status or None if not found
        """
        return self.order_status.get(order_id)
    
    def get_order_executions(self, order_id: int) -> List:
        """
        Get execution details for an order.
        
        Args:
            order_id (int): Order ID
            
        Returns:
            list: List of execution details
        """
        return self.executions.get(order_id, [])
    
    def add_order_callback(self, event_type: str, callback: Callable):
        """
        Add a callback for order events.
        
        Args:
            event_type (str): Type of event
            callback: Callback function to add
        """
        if event_type in self.order_callbacks:
            self.order_callbacks[event_type].append(callback)


def demonstrate_order_placement():
    """
    Demonstrate order placement functionality with practical examples.
    """
    print("=== Order Placement Demo ===")
    print("WARNING: This demo places actual orders in paper trading mode!")
    
    app = OrderPlacementApp()
    
    # Add callbacks for demonstration
    def on_order_status(order_id, status, filled, remaining, avg_fill_price, *args):
        print(f"Order {order_id}: {status} - Filled: {filled}, Remaining: {remaining}")
    
    def on_execution(req_id, contract, execution):
        print(f"Execution: Order {execution.orderId} - {execution.shares} shares @ {execution.price}")
    
    app.add_order_callback('order_status', on_order_status)
    app.add_order_callback('execution_details', on_execution)
    
    try:
        # Connect to TWS
        app.connect("127.0.0.1", 7497, 0)  # Paper trading port
        
        # Start processing thread
        api_thread = threading.Thread(target=app.run, daemon=True)
        api_thread.start()
        
        # Wait for connection and next valid ID
        time.sleep(5)
        
        if app.isConnected() and app.next_order_id is not None:
            print("Connected - placing sample orders")
            
            # Create sample contracts
            from api.tws_api.doc.contracts.contract_creation import ContractBuilder
            builder = ContractBuilder()
            
            # Place a market order for Apple
            aapl_contract = builder.create_stock_contract("AAPL")
            market_order = app.create_market_order("BUY", 1)  # 1 share
            
            order_id1 = app.place_order(aapl_contract, market_order)
            if order_id1:
                print(f"Market order placed: {order_id1}")
            
            # Place a limit order for Microsoft
            msft_contract = builder.create_stock_contract("MSFT")
            limit_order = app.create_limit_order("BUY", 1, 300.00)  # $300 limit
            
            order_id2 = app.place_order(msft_contract, limit_order)
            if order_id2:
                print(f"Limit order placed: {order_id2}")
            
            # Monitor orders for a while
            print("Monitoring orders for 15 seconds...")
            time.sleep(15)
            
            # Show order status
            print("\n--- Order Status Summary ---")
            for order_id in [order_id1, order_id2]:
                if order_id:
                    status = app.get_order_status(order_id)
                    executions = app.get_order_executions(order_id)
                    print(f"Order {order_id}: Status = {status}, Executions = {len(executions)}")
            
            # Cancel any remaining orders
            for order_id in [order_id1, order_id2]:
                if order_id and app.get_order_status(order_id) not in ['Filled', 'Cancelled']:
                    app.cancel_order(order_id)
                    print(f"Cancelled order {order_id}")
        
        else:
            print("Failed to connect to TWS or get next valid order ID")
    
    except KeyboardInterrupt:
        print("\nShutting down...")
    
    finally:
        app.disconnect()


if __name__ == "__main__":
    """
    Main execution block for order placement demonstrations.
    """
    print("TWS API Order Placement Examples")
    print("=" * 40)
    print("Note: This connects to paper trading by default")
    
    # Run the demonstration
    demonstrate_order_placement()
