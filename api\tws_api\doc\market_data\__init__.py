"""
TWS API Market Data Documentation

This module provides comprehensive documentation and examples for retrieving
market data from the Interactive Brokers TWS API. Market data is fundamental
to trading applications and includes real-time quotes, historical data,
and various market information.

The market_data module covers:
- live_data: Real-time market data streaming and tick processing
- historical_data: Historical bars, ticks, and time series data
- delayed_data: Delayed market data for non-subscribed users
- market_depth: Level II market depth and order book data
- tick_types: Understanding different tick types and their meanings
- data_subscriptions: Managing market data subscriptions and permissions

Key Concepts:
- Market Data Types: Real-time, delayed, frozen, and historical data
- Tick Data: Individual price and volume updates
- Bar Data: OHLCV aggregated data over time periods
- Generic Ticks: Additional market data fields beyond basic quotes
- Market Depth: Order book information showing bid/ask levels
- Data Permissions: Subscription requirements for different data types

Usage:
    from doc.market_data import live_data
    from doc.market_data import historical_data
    from doc.market_data import delayed_data
    # ... import other modules as needed

Important Notes:
- Most market data requires paid subscriptions
- Delayed data is available for non-subscribed users
- Market data permissions vary by exchange and instrument type
- Rate limiting applies to market data requests
- Different tick types provide different information
"""

# Import all market data modules for easy access
from . import live_data
from . import historical_data
from . import delayed_data
from . import market_depth
from . import tick_types
from . import data_subscriptions

__all__ = [
    'live_data',
    'historical_data',
    'delayed_data',
    'market_depth',
    'tick_types',
    'data_subscriptions'
]
