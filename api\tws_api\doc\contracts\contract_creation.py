"""
Contract Creation and Definition

This module demonstrates how to create and define contract objects for
different types of financial instruments in the TWS API. Contract objects
are the fundamental building blocks used throughout the API.

Key Topics Covered:
- Creating basic contract objects
- Setting required and optional contract fields
- Understanding contract field meanings and usage
- Creating contracts for different security types
- Best practices for contract definition
- Contract field validation and error prevention

Contract objects are used for market data requests, order placement,
executions, and virtually all TWS API operations that involve financial instruments.
"""

import logging
from typing import Optional, Dict, Any
from ibapi.contract import Contract


class ContractBuilder:
    """
    Utility class for building and validating contract objects.
    
    This class provides methods for creating properly formatted contract
    objects for different types of financial instruments with validation
    and error checking.
    
    Attributes:
        logger: Logger instance for debugging and error reporting
    """
    
    def __init__(self):
        """Initialize the contract builder."""
        self.logger = logging.getLogger(__name__)
    
    def create_stock_contract(self, symbol: str, exchange: str = "SMART", 
                            currency: str = "USD", primary_exchange: str = "") -> Contract:
        """
        Create a stock contract.
        
        Args:
            symbol (str): Stock symbol (e.g., "AAPL", "MSFT")
            exchange (str): Exchange for routing (default: "SMART")
            currency (str): Currency (default: "USD")
            primary_exchange (str): Primary listing exchange (optional)
            
        Returns:
            Contract: Configured stock contract
        """
        contract = Contract()
        contract.symbol = symbol
        contract.secType = "STK"
        contract.exchange = exchange
        contract.currency = currency
        
        if primary_exchange:
            contract.primaryExchange = primary_exchange
        
        self.logger.info(f"Created stock contract: {symbol} {currency} {exchange}")
        return contract
    
    def create_option_contract(self, symbol: str, expiry: str, strike: float, 
                             right: str, exchange: str = "SMART", 
                             currency: str = "USD", multiplier: str = "100") -> Contract:
        """
        Create an option contract.
        
        Args:
            symbol (str): Underlying symbol
            expiry (str): Expiration date (YYYYMMDD format)
            strike (float): Strike price
            right (str): Option right ("C" for call, "P" for put)
            exchange (str): Exchange for routing (default: "SMART")
            currency (str): Currency (default: "USD")
            multiplier (str): Contract multiplier (default: "100")
            
        Returns:
            Contract: Configured option contract
        """
        contract = Contract()
        contract.symbol = symbol
        contract.secType = "OPT"
        contract.exchange = exchange
        contract.currency = currency
        contract.lastTradeDateOrContractMonth = expiry
        contract.strike = strike
        contract.right = right.upper()
        contract.multiplier = multiplier
        
        self.logger.info(f"Created option contract: {symbol} {expiry} {strike} {right}")
        return contract
    
    def create_future_contract(self, symbol: str, expiry: str, 
                             exchange: str, currency: str = "USD",
                             multiplier: str = "") -> Contract:
        """
        Create a futures contract.
        
        Args:
            symbol (str): Futures symbol
            expiry (str): Expiration date (YYYYMM format)
            exchange (str): Exchange
            currency (str): Currency (default: "USD")
            multiplier (str): Contract multiplier (optional)
            
        Returns:
            Contract: Configured futures contract
        """
        contract = Contract()
        contract.symbol = symbol
        contract.secType = "FUT"
        contract.exchange = exchange
        contract.currency = currency
        contract.lastTradeDateOrContractMonth = expiry
        
        if multiplier:
            contract.multiplier = multiplier
        
        self.logger.info(f"Created futures contract: {symbol} {expiry} {exchange}")
        return contract
    
    def create_forex_contract(self, base_currency: str, quote_currency: str,
                            exchange: str = "IDEALPRO") -> Contract:
        """
        Create a forex contract.
        
        Args:
            base_currency (str): Base currency (e.g., "EUR")
            quote_currency (str): Quote currency (e.g., "USD")
            exchange (str): Exchange (default: "IDEALPRO")
            
        Returns:
            Contract: Configured forex contract
        """
        contract = Contract()
        contract.symbol = base_currency
        contract.secType = "CASH"
        contract.exchange = exchange
        contract.currency = quote_currency
        
        self.logger.info(f"Created forex contract: {base_currency}.{quote_currency}")
        return contract
    
    def create_index_contract(self, symbol: str, exchange: str, 
                            currency: str = "USD") -> Contract:
        """
        Create an index contract.
        
        Args:
            symbol (str): Index symbol (e.g., "SPX")
            exchange (str): Exchange
            currency (str): Currency (default: "USD")
            
        Returns:
            Contract: Configured index contract
        """
        contract = Contract()
        contract.symbol = symbol
        contract.secType = "IND"
        contract.exchange = exchange
        contract.currency = currency
        
        self.logger.info(f"Created index contract: {symbol} {exchange}")
        return contract
    
    def create_bond_contract(self, symbol: str, exchange: str, 
                           currency: str = "USD") -> Contract:
        """
        Create a bond contract.
        
        Args:
            symbol (str): Bond symbol
            exchange (str): Exchange
            currency (str): Currency (default: "USD")
            
        Returns:
            Contract: Configured bond contract
        """
        contract = Contract()
        contract.symbol = symbol
        contract.secType = "BOND"
        contract.exchange = exchange
        contract.currency = currency
        
        self.logger.info(f"Created bond contract: {symbol} {exchange}")
        return contract
    
    def create_cfd_contract(self, symbol: str, exchange: str = "SMART",
                          currency: str = "USD") -> Contract:
        """
        Create a CFD contract.
        
        Args:
            symbol (str): CFD symbol
            exchange (str): Exchange (default: "SMART")
            currency (str): Currency (default: "USD")
            
        Returns:
            Contract: Configured CFD contract
        """
        contract = Contract()
        contract.symbol = symbol
        contract.secType = "CFD"
        contract.exchange = exchange
        contract.currency = currency
        
        self.logger.info(f"Created CFD contract: {symbol} {currency}")
        return contract
    
    def create_contract_by_conid(self, con_id: int, exchange: str = "SMART") -> Contract:
        """
        Create a contract using contract ID (most precise method).
        
        Args:
            con_id (int): Contract ID
            exchange (str): Exchange (default: "SMART")
            
        Returns:
            Contract: Contract with specified conId
        """
        contract = Contract()
        contract.conId = con_id
        contract.exchange = exchange
        
        self.logger.info(f"Created contract by conId: {con_id}")
        return contract
    
    def validate_contract_fields(self, contract: Contract) -> Dict[str, Any]:
        """
        Validate contract fields and return validation results.
        
        Args:
            contract (Contract): Contract to validate
            
        Returns:
            dict: Validation results with errors and warnings
        """
        validation = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'missing_fields': [],
            'recommendations': []
        }
        
        # Check for minimum required fields
        if not contract.conId:
            # If no conId, need symbol + secType + exchange + currency
            if not contract.symbol:
                validation['errors'].append("Symbol is required when conId is not specified")
                validation['is_valid'] = False
            
            if not contract.secType:
                validation['errors'].append("SecType is required when conId is not specified")
                validation['is_valid'] = False
            
            if not contract.exchange:
                validation['errors'].append("Exchange is required")
                validation['is_valid'] = False
            
            if not contract.currency and contract.secType != "CASH":
                validation['errors'].append("Currency is required for non-CASH instruments")
                validation['is_valid'] = False
        
        # Security type specific validations
        if contract.secType == "OPT":
            if not contract.lastTradeDateOrContractMonth:
                validation['errors'].append("Expiration date required for options")
                validation['is_valid'] = False
            
            if not contract.strike:
                validation['errors'].append("Strike price required for options")
                validation['is_valid'] = False
            
            if not contract.right:
                validation['errors'].append("Right (C/P) required for options")
                validation['is_valid'] = False
            elif contract.right not in ["C", "P", "CALL", "PUT"]:
                validation['errors'].append("Right must be 'C', 'P', 'CALL', or 'PUT'")
                validation['is_valid'] = False
        
        elif contract.secType == "FUT":
            if not contract.lastTradeDateOrContractMonth:
                validation['errors'].append("Expiration date required for futures")
                validation['is_valid'] = False
        
        # Recommendations
        if contract.secType == "STK" and not contract.primaryExchange:
            validation['recommendations'].append("Consider specifying primaryExchange for stocks")
        
        if contract.exchange == "SMART" and contract.secType in ["OPT", "FUT"]:
            validation['warnings'].append("SMART routing may not be optimal for derivatives")
        
        return validation
    
    def get_contract_description(self, contract: Contract) -> str:
        """
        Generate a human-readable description of the contract.
        
        Args:
            contract (Contract): Contract to describe
            
        Returns:
            str: Human-readable contract description
        """
        if contract.conId:
            return f"Contract ID: {contract.conId} on {contract.exchange}"
        
        desc_parts = []
        
        if contract.symbol:
            desc_parts.append(contract.symbol)
        
        if contract.secType:
            desc_parts.append(f"({contract.secType})")
        
        if contract.secType == "OPT":
            if contract.lastTradeDateOrContractMonth:
                desc_parts.append(f"exp:{contract.lastTradeDateOrContractMonth}")
            if contract.strike:
                desc_parts.append(f"strike:{contract.strike}")
            if contract.right:
                desc_parts.append(f"right:{contract.right}")
        
        elif contract.secType == "FUT":
            if contract.lastTradeDateOrContractMonth:
                desc_parts.append(f"exp:{contract.lastTradeDateOrContractMonth}")
        
        if contract.currency:
            desc_parts.append(f"curr:{contract.currency}")
        
        if contract.exchange:
            desc_parts.append(f"exch:{contract.exchange}")
        
        return " ".join(desc_parts)


def demonstrate_contract_creation():
    """
    Demonstrate contract creation for different instrument types.
    """
    print("=== Contract Creation Demo ===")
    
    builder = ContractBuilder()
    
    # Create various contract types
    contracts = []
    
    # Stock contract
    aapl_stock = builder.create_stock_contract("AAPL", "SMART", "USD", "NASDAQ")
    contracts.append(("Apple Stock", aapl_stock))
    
    # Option contract
    aapl_call = builder.create_option_contract("AAPL", "20241220", 150.0, "C")
    contracts.append(("Apple Call Option", aapl_call))
    
    # Futures contract
    es_future = builder.create_future_contract("ES", "202412", "CME", "USD")
    contracts.append(("E-mini S&P 500 Future", es_future))
    
    # Forex contract
    eurusd = builder.create_forex_contract("EUR", "USD")
    contracts.append(("EUR/USD Forex", eurusd))
    
    # Index contract
    spx_index = builder.create_index_contract("SPX", "CBOE", "USD")
    contracts.append(("S&P 500 Index", spx_index))
    
    # Contract by conId
    contract_by_id = builder.create_contract_by_conid(265598, "SMART")
    contracts.append(("Contract by ID", contract_by_id))
    
    # Display contracts and validate them
    print("\n--- Created Contracts ---")
    for name, contract in contracts:
        print(f"\n{name}:")
        print(f"  Description: {builder.get_contract_description(contract)}")
        
        # Validate contract
        validation = builder.validate_contract_fields(contract)
        print(f"  Valid: {validation['is_valid']}")
        
        if validation['errors']:
            print(f"  Errors: {validation['errors']}")
        
        if validation['warnings']:
            print(f"  Warnings: {validation['warnings']}")
        
        if validation['recommendations']:
            print(f"  Recommendations: {validation['recommendations']}")
    
    # Demonstrate invalid contract
    print("\n--- Invalid Contract Example ---")
    invalid_contract = Contract()
    invalid_contract.symbol = "AAPL"
    # Missing required fields
    
    validation = builder.validate_contract_fields(invalid_contract)
    print(f"Invalid contract validation: {validation}")


if __name__ == "__main__":
    """
    Main execution block for contract creation demonstrations.
    """
    print("TWS API Contract Creation Examples")
    print("=" * 40)
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Run the demonstration
    demonstrate_contract_creation()
