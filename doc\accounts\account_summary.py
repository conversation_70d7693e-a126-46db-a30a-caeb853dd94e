"""
Account Summary Data Retrieval

This module demonstrates how to request and receive account summary data from
the TWS API. Account summary provides aggregated account information similar
to what's displayed in the TWS Account Summary window.

Key Topics Covered:
- Requesting account summary with specific tags
- Handling account summary responses
- Managing account summary subscriptions
- Available account summary tags and their meanings
- Canceling account summary subscriptions
- Best practices for account summary usage

The account summary is commonly used with multiple-account structures and
provides a comprehensive overview of account metrics with automatic updates
every 3 minutes.
"""

import threading
import time
import logging
from typing import Dict, List, Optional, Set
from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.account_summary_tags import AccountSummaryTags


class AccountSummaryApp(EClient, EWrapper):
    """
    TWS API application for account summary data retrieval.
    
    This class demonstrates how to request, receive, and manage account
    summary subscriptions with comprehensive error handling and data storage.
    
    Attributes:
        account_summary_data (dict): Storage for received account summary data
        active_subscriptions (set): Set of active subscription request IDs
        subscription_callbacks (dict): Callbacks for subscription events
        summary_tags (dict): Available account summary tags and descriptions
    """
    
    def __init__(self):
        """Initialize the account summary application."""
        EClient.__init__(self, self)
        
        # Data storage
        self.account_summary_data: Dict[str, Dict[str, Dict[str, str]]] = {}
        self.active_subscriptions: Set[int] = set()
        
        # Callback management
        self.subscription_callbacks = {
            'summary_received': [],
            'summary_end': [],
            'subscription_error': []
        }
        
        # Available account summary tags with descriptions
        self.summary_tags = {
            'AccountType': 'The account type',
            'NetLiquidation': 'Net liquidation value',
            'TotalCashValue': 'Total cash value',
            'SettledCash': 'Settled cash',
            'AccruedCash': 'Accrued cash',
            'BuyingPower': 'Buying power',
            'EquityWithLoanValue': 'Equity with loan value',
            'PreviousEquityWithLoanValue': 'Previous day equity with loan value',
            'GrossPositionValue': 'Gross position value',
            'ReqTEquity': 'Required equity for T+1',
            'ReqTMargin': 'Required margin for T+1',
            'SMA': 'Special Memorandum Account',
            'InitMarginReq': 'Initial margin requirement',
            'MaintMarginReq': 'Maintenance margin requirement',
            'AvailableFunds': 'Available funds',
            'ExcessLiquidity': 'Excess liquidity',
            'Cushion': 'Cushion percentage',
            'FullInitMarginReq': 'Full initial margin requirement',
            'FullMaintMarginReq': 'Full maintenance margin requirement',
            'FullAvailableFunds': 'Full available funds',
            'FullExcessLiquidity': 'Full excess liquidity',
            'LookAheadNextChange': 'Look ahead next change',
            'LookAheadInitMarginReq': 'Look ahead initial margin requirement',
            'LookAheadMaintMarginReq': 'Look ahead maintenance margin requirement',
            'LookAheadAvailableFunds': 'Look ahead available funds',
            'LookAheadExcessLiquidity': 'Look ahead excess liquidity',
            'HighestSeverity': 'Highest severity level',
            'DayTradesRemaining': 'Day trades remaining',
            'Leverage': 'Leverage ratio'
        }
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def connectAck(self):
        """Handle successful connection."""
        self.logger.info("Connected to TWS - Account Summary service ready")
    
    def nextValidId(self, orderId):
        """
        Receive next valid order ID.
        
        Args:
            orderId (int): Next valid order ID
        """
        self.logger.info(f"Connection ready. Next valid order ID: {orderId}")
    
    def accountSummary(self, reqId: int, account: str, tag: str, value: str, currency: str):
        """
        Receive account summary data.
        
        This method is called for each account summary item received from TWS.
        Data is organized by request ID, account, and tag for easy access.
        
        Args:
            reqId (int): Request ID for the subscription
            account (str): Account identifier
            tag (str): Summary tag name
            value (str): Tag value
            currency (str): Currency for the value
        """
        # Initialize data structure if needed
        if reqId not in self.account_summary_data:
            self.account_summary_data[reqId] = {}
        
        if account not in self.account_summary_data[reqId]:
            self.account_summary_data[reqId][account] = {}
        
        # Store the summary data
        self.account_summary_data[reqId][account][tag] = {
            'value': value,
            'currency': currency,
            'timestamp': time.time()
        }
        
        self.logger.info(f"Account Summary - ReqId: {reqId}, Account: {account}, "
                        f"Tag: {tag}, Value: {value}, Currency: {currency}")
        
        # Trigger callbacks
        for callback in self.subscription_callbacks['summary_received']:
            try:
                callback(reqId, account, tag, value, currency)
            except Exception as e:
                self.logger.error(f"Summary callback error: {e}")
    
    def accountSummaryEnd(self, reqId: int):
        """
        Handle end of account summary data transmission.
        
        This method is called when all account summary data for a request
        has been transmitted.
        
        Args:
            reqId (int): Request ID for the completed subscription
        """
        self.logger.info(f"Account Summary End - ReqId: {reqId}")
        
        # Trigger end callbacks
        for callback in self.subscription_callbacks['summary_end']:
            try:
                callback(reqId)
            except Exception as e:
                self.logger.error(f"Summary end callback error: {e}")
    
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors related to account summary requests.
        
        Args:
            reqId (int): Request ID
            errorCode (int): Error code
            errorString (str): Error description
            advancedOrderRejectJson (str): Additional error details
        """
        self.logger.error(f"Error {reqId} {errorCode} {errorString}")
        
        # Handle subscription-specific errors
        if reqId in self.active_subscriptions:
            self.logger.error(f"Account summary subscription {reqId} error: {errorString}")
            
            # Trigger error callbacks
            for callback in self.subscription_callbacks['subscription_error']:
                try:
                    callback(reqId, errorCode, errorString)
                except Exception as e:
                    self.logger.error(f"Error callback error: {e}")
    
    def request_account_summary(self, req_id: int, group: str = "All", 
                              tags: Optional[List[str]] = None) -> bool:
        """
        Request account summary data with specified parameters.
        
        Args:
            req_id (int): Unique request identifier
            group (str): Account group ("All" for all accounts or specific group name)
            tags (list, optional): List of specific tags to request
            
        Returns:
            bool: True if request was sent successfully
        """
        # Validate request ID
        if req_id in self.active_subscriptions:
            self.logger.error(f"Request ID {req_id} already has an active subscription")
            return False
        
        # Check subscription limit (only 2 active subscriptions allowed)
        if len(self.active_subscriptions) >= 2:
            self.logger.error("Maximum of 2 active account summary subscriptions allowed")
            return False
        
        try:
            # Prepare tags string
            if tags is None:
                tags_string = AccountSummaryTags.AllTags
            else:
                # Validate tags
                valid_tags = []
                for tag in tags:
                    if tag in self.summary_tags:
                        valid_tags.append(tag)
                    else:
                        self.logger.warning(f"Unknown tag: {tag}")
                
                if not valid_tags:
                    self.logger.error("No valid tags specified")
                    return False
                
                tags_string = ",".join(valid_tags)
            
            # Send the request
            self.reqAccountSummary(req_id, group, tags_string)
            self.active_subscriptions.add(req_id)
            
            self.logger.info(f"Requested account summary - ReqId: {req_id}, "
                           f"Group: {group}, Tags: {tags_string}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to request account summary: {e}")
            return False
    
    def cancel_account_summary(self, req_id: int) -> bool:
        """
        Cancel an active account summary subscription.
        
        Args:
            req_id (int): Request ID to cancel
            
        Returns:
            bool: True if cancellation was sent successfully
        """
        if req_id not in self.active_subscriptions:
            self.logger.warning(f"No active subscription found for request ID {req_id}")
            return False
        
        try:
            self.cancelAccountSummary(req_id)
            self.active_subscriptions.remove(req_id)
            
            self.logger.info(f"Canceled account summary subscription - ReqId: {req_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cancel account summary: {e}")
            return False
    
    def get_account_summary_data(self, req_id: Optional[int] = None) -> Dict:
        """
        Get stored account summary data.
        
        Args:
            req_id (int, optional): Specific request ID to retrieve
            
        Returns:
            dict: Account summary data
        """
        if req_id is not None:
            return self.account_summary_data.get(req_id, {})
        else:
            return self.account_summary_data.copy()
    
    def get_account_value(self, account: str, tag: str, req_id: Optional[int] = None) -> Optional[Dict]:
        """
        Get a specific account value by tag.
        
        Args:
            account (str): Account identifier
            tag (str): Summary tag name
            req_id (int, optional): Specific request ID to search
            
        Returns:
            dict: Value information or None if not found
        """
        if req_id is not None:
            req_ids = [req_id]
        else:
            req_ids = list(self.account_summary_data.keys())
        
        for rid in req_ids:
            if (rid in self.account_summary_data and 
                account in self.account_summary_data[rid] and
                tag in self.account_summary_data[rid][account]):
                return self.account_summary_data[rid][account][tag]
        
        return None
    
    def add_summary_callback(self, event_type: str, callback):
        """
        Add a callback for account summary events.
        
        Args:
            event_type (str): Type of event ('summary_received', 'summary_end', 'subscription_error')
            callback: Callback function to add
        """
        if event_type in self.subscription_callbacks:
            self.subscription_callbacks[event_type].append(callback)
    
    def get_available_tags(self) -> Dict[str, str]:
        """
        Get available account summary tags with descriptions.
        
        Returns:
            dict: Dictionary of tag names and descriptions
        """
        return self.summary_tags.copy()


def demonstrate_account_summary():
    """
    Demonstrate account summary functionality with practical examples.
    """
    print("=== Account Summary Demo ===")
    
    app = AccountSummaryApp()
    
    # Add callbacks for demonstration
    def on_summary_received(req_id, account, tag, value, currency):
        print(f"Received: {account} - {tag}: {value} {currency}")
    
    def on_summary_end(req_id):
        print(f"Summary complete for request {req_id}")
    
    app.add_summary_callback('summary_received', on_summary_received)
    app.add_summary_callback('summary_end', on_summary_end)
    
    try:
        # Connect to TWS
        app.connect("127.0.0.1", 7497, 0)
        
        # Start processing thread
        api_thread = threading.Thread(target=app.run, daemon=True)
        api_thread.start()
        
        # Wait for connection
        time.sleep(3)
        
        if app.isConnected():
            print("Connected - requesting account summary")
            
            # Request account summary with specific tags
            important_tags = ['NetLiquidation', 'TotalCashValue', 'BuyingPower', 'AvailableFunds']
            
            if app.request_account_summary(9001, "All", important_tags):
                print("Account summary requested successfully")
                
                # Wait for data
                time.sleep(10)
                
                # Display received data
                summary_data = app.get_account_summary_data(9001)
                print("\n--- Account Summary Data ---")
                for account, data in summary_data.items():
                    print(f"Account: {account}")
                    for tag, info in data.items():
                        print(f"  {tag}: {info['value']} {info['currency']}")
                
                # Cancel subscription
                app.cancel_account_summary(9001)
                print("Account summary subscription canceled")
        
        else:
            print("Failed to connect to TWS")
    
    except KeyboardInterrupt:
        print("\nShutting down...")
    
    finally:
        # Cancel all active subscriptions
        for req_id in list(app.active_subscriptions):
            app.cancel_account_summary(req_id)
        
        app.disconnect()


if __name__ == "__main__":
    """
    Main execution block for account summary demonstrations.
    """
    print("TWS API Account Summary Examples")
    print("=" * 40)
    
    # Show available tags
    app = AccountSummaryApp()
    print("Available Account Summary Tags:")
    for tag, description in app.get_available_tags().items():
        print(f"  {tag}: {description}")
    
    print("\n" + "=" * 40)
    
    # Run the demonstration
    demonstrate_account_summary()
