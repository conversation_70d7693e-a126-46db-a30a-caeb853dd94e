"""
EReader Thread Management for TWS API

This module demonstrates the EReader thread architecture and message processing
mechanisms in the TWS API. The EReader is responsible for reading incoming
messages from the TWS socket and managing the message queue.

Key Topics Covered:
- Understanding the two-thread architecture (main thread + EReader thread)
- EReader initialization and management
- Message queue processing and handling
- Thread synchronization and error handling
- Performance optimization for message processing
- Custom message processing implementations

The EReader thread is critical for TWS API applications as it handles all
incoming data from TWS in a separate thread, allowing the main application
thread to continue processing without blocking.
"""

import threading
import time
import queue
import logging
from collections import deque
from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.reader import EReader


class EReaderDemoApp(EClient, EWrapper):
    """
    Demonstration application showing EReader thread management.
    
    This class illustrates how the EReader thread works in conjunction
    with the main application thread to process TWS API messages efficiently.
    
    Attributes:
        reader (EReader): The EReader instance for message processing
        msg_queue (queue.Queue): Message queue for thread communication
        processing_active (bool): Flag to control message processing
        message_count (int): Counter for processed messages
        processing_stats (dict): Statistics about message processing
    """
    
    def __init__(self):
        """Initialize the EReader demonstration application."""
        EClient.__init__(self, self)
        
        # EReader and message queue
        self.reader = None
        self.msg_queue = queue.Queue()
        
        # Processing control
        self.processing_active = False
        self.message_count = 0
        
        # Statistics tracking
        self.processing_stats = {
            'messages_processed': 0,
            'processing_errors': 0,
            'average_processing_time': 0,
            'queue_size_history': []
        }
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def connectAck(self):
        """
        Handle connection acknowledgment and start EReader.
        
        This method is called when the connection is established and
        initializes the EReader thread for message processing.
        """
        self.logger.info("Connection established - starting EReader")
        
        # Create and start the EReader thread
        self.reader = EReader(self.conn, self.msg_queue)
        self.reader.start()
        
        self.logger.info("EReader thread started successfully")
    
    def nextValidId(self, orderId):
        """
        Receive next valid order ID.
        
        Args:
            orderId (int): Next valid order ID from TWS
        """
        self.logger.info(f"Next valid order ID: {orderId}")
        self.logger.info("Connection ready for requests")
    
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors from TWS.
        
        Args:
            reqId (int): Request ID
            errorCode (int): Error code
            errorString (str): Error description
            advancedOrderRejectJson (str): Additional error details
        """
        self.logger.error(f"Error {reqId} {errorCode} {errorString}")
        
        if errorCode in [502, 504]:
            self.processing_active = False
    
    def run(self):
        """
        Main message processing loop.
        
        This method demonstrates the standard approach to processing
        messages from the EReader queue. It runs in the main thread
        while the EReader runs in its own thread.
        """
        self.processing_active = True
        self.logger.info("Starting message processing loop")
        
        while self.processing_active:
            try:
                # Check if there are messages in the queue
                if not self.msg_queue.empty():
                    # Process messages from the queue
                    start_time = time.time()
                    
                    # Get message from queue (non-blocking)
                    try:
                        msg = self.msg_queue.get_nowait()
                        if msg is not None:
                            # Decode and process the message
                            self.decoder.interpret(msg)
                            self.message_count += 1
                            
                            # Update processing statistics
                            processing_time = time.time() - start_time
                            self._update_processing_stats(processing_time)
                            
                    except queue.Empty:
                        pass  # No messages available
                    
                else:
                    # Small delay to prevent busy waiting
                    time.sleep(0.001)
                
                # Periodically log queue status
                if self.message_count % 100 == 0 and self.message_count > 0:
                    self._log_processing_stats()
                    
            except Exception as e:
                self.logger.error(f"Message processing error: {e}")
                self.processing_stats['processing_errors'] += 1
                time.sleep(0.1)  # Brief pause on error
        
        self.logger.info("Message processing loop stopped")
    
    def _update_processing_stats(self, processing_time):
        """
        Update message processing statistics.
        
        Args:
            processing_time (float): Time taken to process the message
        """
        self.processing_stats['messages_processed'] += 1
        
        # Update average processing time
        current_avg = self.processing_stats['average_processing_time']
        message_count = self.processing_stats['messages_processed']
        
        new_avg = ((current_avg * (message_count - 1)) + processing_time) / message_count
        self.processing_stats['average_processing_time'] = new_avg
        
        # Track queue size
        queue_size = self.msg_queue.qsize()
        self.processing_stats['queue_size_history'].append(queue_size)
        
        # Keep only recent queue size history
        if len(self.processing_stats['queue_size_history']) > 1000:
            self.processing_stats['queue_size_history'] = \
                self.processing_stats['queue_size_history'][-500:]
    
    def _log_processing_stats(self):
        """Log current processing statistics."""
        stats = self.processing_stats
        queue_size = self.msg_queue.qsize()
        
        self.logger.info(f"Processing Stats:")
        self.logger.info(f"  Messages processed: {stats['messages_processed']}")
        self.logger.info(f"  Processing errors: {stats['processing_errors']}")
        self.logger.info(f"  Average processing time: {stats['average_processing_time']:.6f}s")
        self.logger.info(f"  Current queue size: {queue_size}")
    
    def get_processing_statistics(self):
        """
        Get comprehensive processing statistics.
        
        Returns:
            dict: Processing statistics and metrics
        """
        stats = self.processing_stats.copy()
        stats['current_queue_size'] = self.msg_queue.qsize()
        stats['reader_active'] = self.reader is not None and self.reader.is_alive()
        stats['processing_active'] = self.processing_active
        
        return stats


class OptimizedEReaderApp(EClient, EWrapper):
    """
    Optimized EReader implementation with batched message processing.
    
    This class demonstrates advanced message processing techniques including
    message batching, priority queuing, and performance optimization.
    
    Attributes:
        message_buffer (deque): Buffer for batched message processing
        buffer_size (int): Maximum buffer size for batching
        processing_thread (Thread): Dedicated processing thread
        priority_queue (queue.PriorityQueue): Priority-based message queue
    """
    
    def __init__(self, buffer_size=50):
        """
        Initialize optimized EReader application.
        
        Args:
            buffer_size (int): Size of message buffer for batching
        """
        EClient.__init__(self, self)
        
        # Message processing optimization
        self.message_buffer = deque()
        self.buffer_size = buffer_size
        self.processing_thread = None
        self.processing_active = False
        
        # Priority queue for important messages
        self.priority_queue = queue.PriorityQueue()
        
        # Performance metrics
        self.performance_metrics = {
            'batch_processing_times': [],
            'messages_per_second': 0,
            'peak_queue_size': 0
        }
        
        self.logger = logging.getLogger(__name__)
    
    def connectAck(self):
        """Handle connection and start optimized processing."""
        self.logger.info("Starting optimized EReader processing")
        
        # Start EReader
        self.reader = EReader(self.conn, self.msg_queue)
        self.reader.start()
        
        # Start optimized processing thread
        self.start_optimized_processing()
    
    def start_optimized_processing(self):
        """Start optimized message processing with batching."""
        if self.processing_active:
            return
        
        self.processing_active = True
        
        def optimized_processing_loop():
            """Optimized processing loop with message batching."""
            while self.processing_active:
                try:
                    # Collect messages into buffer
                    messages_collected = 0
                    start_time = time.time()
                    
                    while (not self.msg_queue.empty() and 
                           messages_collected < self.buffer_size):
                        try:
                            msg = self.msg_queue.get_nowait()
                            if msg is not None:
                                self.message_buffer.append(msg)
                                messages_collected += 1
                        except queue.Empty:
                            break
                    
                    # Process buffered messages
                    if self.message_buffer:
                        self._process_message_batch()
                        
                        # Update performance metrics
                        batch_time = time.time() - start_time
                        self.performance_metrics['batch_processing_times'].append(batch_time)
                        
                        # Calculate messages per second
                        if batch_time > 0:
                            mps = len(self.message_buffer) / batch_time
                            self.performance_metrics['messages_per_second'] = mps
                    
                    # Update peak queue size
                    current_queue_size = self.msg_queue.qsize()
                    if current_queue_size > self.performance_metrics['peak_queue_size']:
                        self.performance_metrics['peak_queue_size'] = current_queue_size
                    
                    # Small delay if no messages processed
                    if messages_collected == 0:
                        time.sleep(0.001)
                        
                except Exception as e:
                    self.logger.error(f"Optimized processing error: {e}")
                    time.sleep(0.1)
        
        self.processing_thread = threading.Thread(
            target=optimized_processing_loop, 
            daemon=True
        )
        self.processing_thread.start()
        
        self.logger.info("Optimized processing thread started")
    
    def _process_message_batch(self):
        """
        Process a batch of messages efficiently.
        
        This method processes multiple messages in a single batch,
        reducing the overhead of individual message processing.
        """
        batch_size = len(self.message_buffer)
        
        try:
            while self.message_buffer:
                msg = self.message_buffer.popleft()
                self.decoder.interpret(msg)
                
        except Exception as e:
            self.logger.error(f"Batch processing error: {e}")
        
        if batch_size > 10:  # Log only for significant batches
            self.logger.debug(f"Processed batch of {batch_size} messages")
    
    def stop_processing(self):
        """Stop optimized message processing."""
        self.processing_active = False
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=5)
        self.logger.info("Optimized processing stopped")
    
    def get_performance_metrics(self):
        """
        Get performance metrics for optimized processing.
        
        Returns:
            dict: Performance metrics and statistics
        """
        metrics = self.performance_metrics.copy()
        
        # Calculate average batch processing time
        if metrics['batch_processing_times']:
            avg_batch_time = sum(metrics['batch_processing_times']) / len(metrics['batch_processing_times'])
            metrics['average_batch_processing_time'] = avg_batch_time
        
        metrics['current_queue_size'] = self.msg_queue.qsize()
        metrics['buffer_size'] = len(self.message_buffer)
        
        return metrics


def demonstrate_ereader_basics():
    """
    Demonstrate basic EReader functionality and message processing.
    """
    print("=== EReader Basics Demo ===")
    
    app = EReaderDemoApp()
    
    try:
        # Connect to TWS
        app.connect("127.0.0.1", 7497, 0)
        
        # Start processing in separate thread
        processing_thread = threading.Thread(target=app.run, daemon=True)
        processing_thread.start()
        
        # Wait for connection
        time.sleep(3)
        
        if app.isConnected():
            print("Connected - EReader is processing messages")
            
            # Request some data to generate messages
            app.reqCurrentTime()
            
            # Let it run for a bit
            time.sleep(5)
            
            # Show processing statistics
            stats = app.get_processing_statistics()
            print("\nProcessing Statistics:")
            for key, value in stats.items():
                print(f"  {key}: {value}")
        
        app.processing_active = False
        app.disconnect()
        
    except Exception as e:
        print(f"Demo error: {e}")


def demonstrate_optimized_processing():
    """
    Demonstrate optimized EReader processing with batching.
    """
    print("\n=== Optimized EReader Demo ===")
    
    app = OptimizedEReaderApp(buffer_size=25)
    
    try:
        app.connect("127.0.0.1", 7497, 1)
        time.sleep(3)
        
        if app.isConnected():
            print("Connected - Optimized processing active")
            
            # Generate some message traffic
            app.reqCurrentTime()
            
            time.sleep(5)
            
            # Show performance metrics
            metrics = app.get_performance_metrics()
            print("\nPerformance Metrics:")
            for key, value in metrics.items():
                if isinstance(value, list) and len(value) > 5:
                    print(f"  {key}: {len(value)} entries (avg: {sum(value)/len(value):.6f})")
                else:
                    print(f"  {key}: {value}")
        
        app.stop_processing()
        app.disconnect()
        
    except Exception as e:
        print(f"Optimized demo error: {e}")


if __name__ == "__main__":
    """
    Main execution block for EReader demonstrations.
    """
    print("TWS API EReader Thread Examples")
    print("=" * 40)
    
    # Demonstrate basic EReader functionality
    demonstrate_ereader_basics()
    
    # Demonstrate optimized processing
    demonstrate_optimized_processing()
