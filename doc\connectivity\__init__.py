"""
TWS API Connectivity Documentation

This module provides comprehensive documentation and examples for establishing,
maintaining, and managing connections to the Interactive Brokers TWS API.

The connectivity module covers:
- establishing_connection: Basic connection setup and configuration
- verify_connection: Connection status checking and validation
- ereader_thread: Message processing and threading architecture
- remote_connections: Remote TWS connections and network configuration
- multiple_applications: Multi-client management and connection pooling
- broken_connections: Connection recovery and resilience mechanisms

Each submodule contains working Python code examples with detailed explanations
that demonstrate best practices for TWS API connectivity.

Key Concepts:
- Socket-based TCP connection between API client and TWS
- EClient/EWrapper architecture for request/response handling
- Two-thread design for message processing
- Support for up to 32 simultaneous client connections per TWS session
- Automatic reconnection and error recovery mechanisms

Usage:
    from doc.connectivity import establishing_connection
    from doc.connectivity import verify_connection
    # ... import other modules as needed
"""

# Import all connectivity modules for easy access
from . import establishing_connection
from . import verify_connection
from . import ereader_thread
from . import remote_connections
from . import multiple_applications
from . import broken_connections

__all__ = [
    'establishing_connection',
    'verify_connection', 
    'ereader_thread',
    'remote_connections',
    'multiple_applications',
    'broken_connections'
]
