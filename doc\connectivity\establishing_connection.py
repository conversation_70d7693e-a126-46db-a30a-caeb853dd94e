"""
Establishing TWS API Connections

This module demonstrates how to establish connections to the Interactive Brokers
TWS or IB Gateway using the Python API. It covers basic connection setup,
parameter configuration, and connection process flow.

Key Topics Covered:
- Basic connection setup with EClient and EWrapper
- Connection parameters (host, port, clientId)
- Connection options and authentication
- Error handling during connection establishment
- Connection process flow and handshake

The examples in this module provide a foundation for building robust TWS API
applications with proper connection management.
"""

import threading
import time
from ibapi.client import EClient
from ibapi.wrapper import EWrapper


class BasicIBApp(EClient, EWrapper):
    """
    Basic TWS API application demonstrating connection establishment.
    
    This class inherits from both EClient (for sending requests) and EWrapper
    (for receiving responses) to create a complete API application.
    
    Attributes:
        nextValidOrderId (int): Next valid order ID from TWS
        connected (bool): Connection status flag
        connection_time (str): Time when connection was established
        server_version (int): TWS server version
    """
    
    def __init__(self):
        """
        Initialize the API application.
        
        The EClient.__init__ call sets up the client-side functionality
        for sending requests to TWS.
        """
        EClient.__init__(self, self)
        self.nextValidOrderId = None
        self.connected = False
        self.connection_time = None
        self.server_version = None
    
    def connectAck(self):
        """
        Callback method called when connection is successfully established.
        
        This method is automatically called by the API framework when the
        initial handshake with TWS is completed. It signals that the
        connection is ready for API requests.
        """
        print("API Connection Established.")
        self.connected = True
        
        # Get connection details
        self.server_version = self.serverVersion()
        self.connection_time = self.twsConnectionTime()
        
        print(f"Connected to TWS Server Version: {self.server_version}")
        print(f"Connection Time: {self.connection_time}")
    
    def nextValidId(self, orderId):
        """
        Receive the next valid order ID from TWS.
        
        This callback is triggered after successful connection and provides
        the next available order ID. It's commonly used as an indicator
        that the connection is fully established and ready for requests.
        
        Args:
            orderId (int): Next valid order ID from TWS
        """
        self.nextValidOrderId = orderId
        print(f"Next valid order ID: {orderId}")
        print("Connection is ready for API requests")
    
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors and messages from TWS.
        
        This method receives all error messages and system notifications
        from TWS. Proper error handling is crucial for robust applications.
        
        Args:
            reqId (int): Request ID associated with the error
            errorCode (int): Error code number
            errorString (str): Human-readable error description
            advancedOrderRejectJson (str): Additional error details (optional)
        """
        print(f"Error {reqId} {errorCode} {errorString}")
        
        # Handle specific connection error codes
        if errorCode == 502:
            print("Connection failed. Check:")
            print("1. TWS/IB Gateway is running")
            print("2. API connections are enabled in TWS settings")
            print("3. Correct port number is being used")
            print("4. No firewall blocking the connection")
            self.connected = False
        elif errorCode == 504:
            print("Not connected - connection was lost or never established")
            self.connected = False


def establish_basic_connection():
    """
    Demonstrate basic connection establishment to TWS.
    
    This function shows the standard process for connecting to TWS:
    1. Create application instance
    2. Connect to TWS with appropriate parameters
    3. Start message processing thread
    4. Wait for connection confirmation
    
    Returns:
        BasicIBApp: Connected application instance
    """
    print("=== Basic Connection Example ===")
    
    # Step 1: Create application instance
    app = BasicIBApp()
    
    # Step 2: Connect to TWS
    # Parameters: host, port, clientId
    # - host: "127.0.0.1" for local TWS, or IP address for remote
    # - port: 7497 (paper), 7496 (live) for TWS; 4002 (paper), 4001 (live) for Gateway
    # - clientId: Unique identifier (0-31) for this application
    print("Connecting to TWS...")
    app.connect("127.0.0.1", 7497, clientId=0)
    
    # Step 3: Start message processing thread
    # The run() method processes incoming messages from TWS
    api_thread = threading.Thread(target=app.run, daemon=True)
    api_thread.start()
    
    # Step 4: Wait for connection to be established
    print("Waiting for connection...")
    timeout = 10  # seconds
    start_time = time.time()
    
    while not app.connected and (time.time() - start_time) < timeout:
        time.sleep(0.1)
    
    if app.connected:
        print("Successfully connected to TWS!")
        return app
    else:
        print("Failed to connect within timeout period")
        return None


def establish_connection_with_options():
    """
    Demonstrate connection establishment with additional options.
    
    This example shows how to use connection options like PACEAPI
    for enhanced functionality.
    
    Returns:
        BasicIBApp: Connected application instance with options
    """
    print("\n=== Connection with Options Example ===")
    
    app = BasicIBApp()
    
    # Set connection options before connecting
    # "+PACEAPI" enables pacing functionality to handle rate limiting
    app.setConnectOptions("+PACEAPI")
    print("Connection options set: +PACEAPI")
    
    # Connect with the options
    print("Connecting to TWS with options...")
    app.connect("127.0.0.1", 7497, clientId=1)
    
    # Start processing thread
    api_thread = threading.Thread(target=app.run, daemon=True)
    api_thread.start()
    
    # Wait for connection
    timeout = 10
    start_time = time.time()
    
    while not app.connected and (time.time() - start_time) < timeout:
        time.sleep(0.1)
    
    if app.connected:
        print("Successfully connected with options!")
        return app
    else:
        print("Failed to connect with options")
        return None


class ConnectionParametersDemo:
    """
    Demonstration class for different connection parameter configurations.
    
    This class shows various connection scenarios and parameter combinations
    that developers might encounter when connecting to TWS.
    """
    
    @staticmethod
    def get_connection_parameters():
        """
        Get standard connection parameters for different environments.
        
        Returns:
            dict: Dictionary of connection parameter sets
        """
        return {
            'local_paper': {
                'host': '127.0.0.1',
                'port': 7497,
                'description': 'Local TWS Paper Trading'
            },
            'local_live': {
                'host': '127.0.0.1', 
                'port': 7496,
                'description': 'Local TWS Live Trading'
            },
            'gateway_paper': {
                'host': '127.0.0.1',
                'port': 4002,
                'description': 'Local IB Gateway Paper Trading'
            },
            'gateway_live': {
                'host': '127.0.0.1',
                'port': 4001,
                'description': 'Local IB Gateway Live Trading'
            },
            'remote_example': {
                'host': '*************',
                'port': 7497,
                'description': 'Remote TWS Paper Trading'
            }
        }
    
    @staticmethod
    def validate_client_id(client_id):
        """
        Validate client ID parameter.
        
        Client IDs must be in the range 0-31 as TWS supports up to
        32 simultaneous API connections.
        
        Args:
            client_id (int): Client ID to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        if not isinstance(client_id, int):
            print(f"Client ID must be an integer, got {type(client_id)}")
            return False
        
        if not (0 <= client_id <= 31):
            print(f"Client ID must be between 0 and 31, got {client_id}")
            return False
        
        return True
    
    @staticmethod
    def demonstrate_parameters():
        """
        Demonstrate different connection parameter configurations.
        """
        print("\n=== Connection Parameters Demo ===")
        
        params = ConnectionParametersDemo.get_connection_parameters()
        
        print("Available connection configurations:")
        for config_name, config in params.items():
            print(f"  {config_name}:")
            print(f"    Host: {config['host']}")
            print(f"    Port: {config['port']}")
            print(f"    Description: {config['description']}")
        
        print("\nClient ID validation examples:")
        test_ids = [0, 15, 31, 32, -1, "invalid"]
        for test_id in test_ids:
            is_valid = ConnectionParametersDemo.validate_client_id(test_id)
            print(f"  Client ID {test_id}: {'Valid' if is_valid else 'Invalid'}")


if __name__ == "__main__":
    """
    Main execution block demonstrating connection establishment examples.
    
    This block runs when the module is executed directly and demonstrates
    the various connection establishment techniques.
    """
    print("TWS API Connection Establishment Examples")
    print("=" * 50)
    
    # Demonstrate connection parameters
    ConnectionParametersDemo.demonstrate_parameters()
    
    # Establish basic connection
    basic_app = establish_basic_connection()
    
    if basic_app:
        # Keep connection alive for demonstration
        print("\nConnection established. Press Ctrl+C to disconnect...")
        try:
            time.sleep(5)  # Keep alive for 5 seconds
        except KeyboardInterrupt:
            print("\nDisconnecting...")
        finally:
            basic_app.disconnect()
            print("Disconnected from TWS")
    
    # Demonstrate connection with options
    options_app = establish_connection_with_options()
    
    if options_app:
        try:
            time.sleep(5)
        except KeyboardInterrupt:
            print("\nDisconnecting...")
        finally:
            options_app.disconnect()
            print("Disconnected from TWS")
